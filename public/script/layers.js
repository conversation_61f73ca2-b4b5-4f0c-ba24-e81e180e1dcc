const fs = require("fs");
const path = require("path");

// 要解析的 JSON 文件路径
const jsonFilePath = path.join(__dirname, "../config/OneMap/LayerWidget.json");
const globalData = {};
const unKeys = ["filter", "applyConfig"];

// title 处理删除年份
const titleHandle = (title) => {
  const pattern = /\d+年/;
  const result = title.replace(pattern, "");
  return result;
};

// 递归函数，用于解析 JSON 中的树结构并取出所有 config
function getValues(node) {
  if (Array.isArray(node)) {
    for (const child of node) {
      getValues(child);
    }
    return;
  }
  if (Array.isArray(node.children)) {
    for (const child of node.children) {
      getValues(child);
    }
  }
  if (node.config) {
    let data = fs.readFileSync(path.join(__dirname, `../${node.config}`), "utf8");
    data = JSON.parse(data);
    if (globalData.hasOwnProperty(data.name)) {
      for (let key in data) {
        if (!globalData[data.name].hasOwnProperty(key) && !unKeys.includes(key)) {
          if (key === "popup" && data.popup.type == "specialRequest") return;
          if(key === "title")  data[key] = titleHandle(data[key])
          if(key === "legend")  data[key].title = titleHandle(data[key].title)
          globalData[data.name][key] = data[key];
        }
      }
    } else {
      let obj = {};
      Object.keys(data).forEach((key) => {
        if (!unKeys.includes(key)) {
          if (key === "popup" && data.popup.type == "specialRequest") return;
          if(key === "title")  data[key] = titleHandle(data[key])
          if(key === "legend")  data[key].title = titleHandle(data[key].title)
          obj[key] = data[key];
        }
      });
      globalData[data.name] = obj;
    }
  }
}

// 从指定的路径中读取 JSON 文件
fs.readFile(jsonFilePath, (err, data) => {
  if (err) {
    console.error(`文件读取失败: ${err.message}`);
    return;
  }
  const json = JSON.parse(data.toString());
  getValues(json);
  Object.keys(globalData).forEach((key) => {
    if (!fs.existsSync(`./Layers`)) {
      fs.mkdirSync(`./Layers`, { recursive: true });
    }
    fs.writeFile(`./Layers/${key}.json`, JSON.stringify(globalData[key], null, 2), (err) => {
      if (err) throw err;
    });
  });
  console.log("文件已被保存");
});
