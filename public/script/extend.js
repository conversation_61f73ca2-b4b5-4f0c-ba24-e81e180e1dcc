const fs = require("fs");
const path = require("path");

// 要提取的键名数组
const keysToExtract = ["filter", "applyConfig", "title"];

// 处理判断特殊的值或者字段是否存在的情况，3 长度为判断值，2 长度为判断存在
const specialCase = [["popup", "type", "specialRequest"]];

function processConfigPath(configPath) {
  // 移除config/和.json，然后以'/'拆分，得到每一个部分
  return configPath.replace("config/", "").replace(".json", "").split("/");
}

// 递归处理函数，处理每一个菜单和子菜单
function processMenu(menuItem) {
  if (menuItem.config) {
    try {
      // 读取相应的文件
      let data = fs.readFileSync(path.join(__dirname, `../${menuItem.config}`), "utf8");
      let json = JSON.parse(data);

      // 提取相应的键名键值组成对象
      let newObj = {};
      keysToExtract.forEach((key) => {
        if (json.hasOwnProperty(key)) {
          newObj[key] = json[key];
        }
      });

      // 特殊处理 'popup.filter' : 如果 'filter' 在 'popup' 存在，则把整个 'popup' 存入 newObj
      specialCase.forEach((ary) => {
        if (ary.length == 3) {
          if (json.hasOwnProperty(ary[0]) && json[ary[0]][ary[1]] == ary[2]) {
            newObj[ary[0]] = json[ary[0]];
          }
        } else if (ary.length == 2) {
          newObj[ary[0]] = json[ary[0]];
        }
      });
      // 解析config路径，得到文件名和目录
      let paths = processConfigPath(menuItem.config);
      let fileName = paths.pop();
      let newFolderPath = path.join(__dirname, "Extend", ...paths);
      console.log(paths, fileName, newFolderPath);
      // 如果不存在则创建新目录
      if (!fs.existsSync(newFolderPath)) {
        fs.mkdirSync(newFolderPath, { recursive: true });
      }

      // 写入文件
      fs.writeFileSync(path.join(newFolderPath, `${fileName}.json`), JSON.stringify(newObj, null, 2));
    } catch (error) {
      console.error(`Error processing ${menuItem.config}: `, error);
    }
  }

  if (menuItem.children) {
    menuItem.children.forEach(processMenu);
  }
}

// 读取配置文件
let configPath = path.join(__dirname, "LayerWidget.json");
let configData = fs.readFileSync(configPath, "utf8");
let config = JSON.parse(configData);

// 开始递归处理
config.forEach((item, index) => {
  if (index == 0) return;
  processMenu(item);
});
