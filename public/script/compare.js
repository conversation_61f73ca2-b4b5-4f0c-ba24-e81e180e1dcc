const fs = require("fs");
function compareConfig(treeA, treeB) {
  if (treeA.length !== treeB.length) {
    console.log("error length");
    return false;
  }
  for (let i = 0; i < treeA.length; i++) {
    if (treeA[i].children && treeB[i].children) {
      compareConfig(treeA[i].children, treeB[i].children);
    } else {
      fs.readFile(`../${treeA[i].config}`, "utf8", (err, dataA) => {
        if (err) throw err;
        if (treeB[i].extend) {
          fs.readFile(`../${treeB[i].extend}`, "utf8", (err, data) => {
            if (err) throw err;
            fs.readFile(`../${treeB[i].config}`, "utf8", (err, dataB) => {
              if (err) throw err;
              let filter = JSON.parse(data);
              const objA = JSON.parse(dataA);
              const objB = JSON.parse(dataB);
              for (let key in objA) {
                if (objB.hasOwnProperty(key)) {
                  if (JSON.stringify(objA[key]) !== JSON.stringify(objB[key])) {
                    filter[key] = objA[key];
                  }
                }
              }
              fs.writeFile(`../${treeB[i].extend}`, JSON.stringify(filter, null, 2), "utf8", (err) => {
                if (err) throw err;
              });
            });
          });
        }
      });
      console.log(treeA[i].config, treeB[i].config);
    }
  }

  return true;
}
// 读取文件
const fileA = "./LayerWidget.json";
const fileB = "./LayerWidget1.json";

fs.readFile(fileA, "utf8", (err, dataA) => {
  if (err) {
    console.error(err);
    return;
  }

  fs.readFile(fileB, "utf8", (err, dataB) => {
    if (err) {
      console.error(err);
      return;
    }

    const configA = JSON.parse(dataA);
    const configB = JSON.parse(dataB);

    compareConfig(configA, configB);
  });
});
