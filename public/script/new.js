const fs = require("fs");
const path = require("path");

function getNameFromConfigPath(configPath) {
  // 读取configPath指定的文件并解析为json
  let configData = fs.readFileSync(path.join(__dirname, `../${configPath}`), "utf8");
  let configJson = JSON.parse(configData);
  // 返回 name 属性
  return configJson.name;
}

// 主要处理函数，处理每一级菜单并更新它们的config和extend属性
function processItem(item) {
  if (item.config) {
    // 从config对应的文件中获取name属性,
    let name = getNameFromConfigPath(item.config);
    // 获取新的config和extend的值
    if (name) {
      item.extend = item.config.replace("config/", "config/Extend/");
      item.config = "config/Layers/" + name + ".json";
    }
  }

  // 如果存在子菜单，递归对子菜单进行处理
  if (item.children) {
    item.children.forEach(processItem);
  }
}

// 读取配置文件
let configData = fs.readFileSync("./LayerWidget.json", "utf8");
let config = JSON.parse(configData);

// 开始处理每一项
config.forEach((item, index) => {
  if (index != 0) processItem(item);
});

// console.log(JSON.stringify(config, null, 2));
// 最后可以选择是否覆盖原来的文件
fs.writeFileSync(`./LayerWidget1.json`, JSON.stringify(config, null, 2));
