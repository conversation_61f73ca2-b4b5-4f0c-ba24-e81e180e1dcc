{"name": "species_jcyj_yf_2022", "type": "feature", "enableGeometryFilter": true, "primaryKey": "_id", "enableRegionFilter": true, "style": {"type": "circle", "paint": {"circle-radius": 10, "circle-color": "#0ee99d", "circle-stroke-width": 2, "circle-stroke-color": "rgba(255,255,255,.7)"}}, "highlight": {"type": "circle", "paint": {"circle-radius": 10, "circle-color": "#ff0000", "circle-stroke-width": 2, "circle-stroke-color": "rgba(255,255,255,.5)"}}, "componentPopup": "app/Components/TCDPopup", "popup": {"title": "样方编号: {样方编号}", "maxWidth": "600px", "childrenTable": {"table": "species_jcyj_yf_wz_2022", "primaryKey": "样方编号", "label": "物种详情", "fields": [{"field": "物种名称", "label": "物种名称"}, {"field": "覆盖度", "label": "覆盖度(%)"}, {"field": "株数", "label": "株数"}, {"field": "直立植物高度", "label": "直立植物高度"}]}, "fields": [{"field": "样地编号", "label": "样地编号"}, {"field": "生境类型", "label": "生境类型"}, {"field": "地址", "label": "地址"}, {"field": "海拔", "label": "样方海拔高度(米)"}, {"field": "样方内物种数量", "label": "样方物种数(个)"}]}, "legend": {"title": "", "type": "point", "items": [{"label": "样方", "symbol": {"color": "#0ee99d"}}]}}