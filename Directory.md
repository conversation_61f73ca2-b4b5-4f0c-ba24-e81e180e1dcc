# 目录重构计划

## 问题分析

当前的目录结构比较混乱，模块菜单简单，但是目录的使用经常有跨目录使用，导致寻找配置时十分繁杂，维护困难

## 解决方案

重构`/public/config`目录下的文件，将按照模块菜单细分，之后的每次调整也相对应的调整目录结构，保证可以按目录寻到到相应的配置文件

## 目录结构

`/public/config`目录下的文件夹以及注释

```sh
├── Layers                  # 用于存放公共图层配置文件
├── Extend                  # 用于存放公共图层扩展配置文件
├── style                   # map基础配置文件
├── Modules                 # 模块配置文件存放目录
│   ├── OneMap              # 综合图
│   ├── PlowLand            # 农用地安全利用
│   │   ├── lbhf            # 类别划分
│   │   │   ├── lbhf        # 类别划分-类别划分
│   │   │   ├── aqly        # 类别划分-安全利用
│   │   │   ├── yggk        # 类别划分-严格管控
│   │   │   └── lbhf_list   # 类别划分-类别划分清单列表
│   │   ├── dttz            # 动态调整
│   │   │   ├── dttz        # 动态调整-动态调整
│   │   │   ├── aqly        # 动态调整-安全利用
│   │   │   ├── yggk        # 动态调整-严格管控
│   │   │   └── dttz_list   # 动态调整-动态调整清单列表
│   │   ├── use_rate        # 安全利用率核算
│   │   ├── plant_check     # 作物种植情况核查
│   │   └── data_analysis   # 数据统计分析
│   │       └── compare     # 对比分析
│   │   └── analysis        # 统计分析
│   ├── FarmLand            # 产地环境监测
│   │   ├── dl              # 产地环境监测-农田氮磷流失监测
│   │   │   ├── gkd         # 国控监测点调查
│   │   │   ├── county      # 县级基本情况调查
│   │   │   └── reduce      # 减排措施调查
│   │   ├── dm              # 产地环境监测-农田地膜残留监测
│   │   │   ├── residue     # 地膜残留监测调查
│   │   │   ├── overall     # 总体概况
│   │   │   └── recycle     # 农膜回收情况
│   │   ├── tr_ncp          # 产地环境监测-土壤与农产品预警监测
│   │   └── straw           # 产地环境监测-农作物秸秆还田监测
│   │       ├── overall     # 总体概况
│   │       └── recycle     # 农膜回收情况
│   ├── Straw               # 秸秆综合利用
│   │   ├── overall         # 秸秆综合利用-总体概况
│   │   ├── output          # 秸秆综合利用-秸秆产生情况
│   │   ├── use             # 秸秆综合利用-秸秆利用情况
│   │   └── return          # 秸秆综合利用-还田离田情况
│   ├── farm                # 生态农场
│   │   ├── distribute      # 生态农场分布
│   │   ├── detail          # 生态农场详情
│   │   └── monitor         # 生态农场监测
│   └─── EcoBase            # 一干七支基地模块
│       ├── distribute      # 示范基地分布
│       └── detail          # 示范基地详情
```
