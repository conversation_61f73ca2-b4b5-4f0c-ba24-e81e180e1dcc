<template>
  <div class="jimu-widget table">
    <component
      v-for="(child, index) in children"
      :is="child.module"
      :key="index"
    />
  </div>
</template>

<script>
/**
 * 布局组件，用于通过配置文件对子组件布局
 */
import BaseWidget from '@/BaseWidget'
export default {
  name: 'VerticalLayout',

  mixins: [BaseWidget],

  data() {
    return {
      // 子组件配置
      children: []
    }
  },

  created() {},

  configLoaded() {
    this.bus.$on(this.$events.widget.OPEN_WIDGET, this.addWidget)
    this.bus.$on(this.$events.widget.CLOSE_WIDGET, this.removeWidget)
  },
  mounted() {},
  methods: {
    async addWidget(config) {
      let module = await this._loadModule(this.config.children, config)
      this.children.push({ title: config.title, module: module })
    },
    async removeWidget(config) {
      this.children = this.children.filter(item => {
        return item.title !== config.title
      })
    },
    // // 加载模块
    async _loadModule(config, layerConfig) {
      const module = await import(`@/${config}`)
      const theModule = this.$utils.object.deepCloneObj(module.default)
      let dataObj = {}
      if (theModule.data) {
        dataObj = theModule.data()
      }
      dataObj.layerConfig = layerConfig
      theModule.data = () => dataObj
      return theModule
    }
  }
}
</script>

<style lang="less" scoped>
@primaryColor: #2a45ad;
.jimu-widget {
  max-height: calc(100% - 20px);
}
.jimu-root-layout {
  width: 100%;
  // height: 100%;
  position: absolute;
  background: #fff;
}
.table {
  overflow-y: scroll;
}
</style>
