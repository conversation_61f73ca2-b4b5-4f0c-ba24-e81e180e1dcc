<template>
  <div class="jimu-root-layout">
    <component v-for="(child, index) in children" :is="child.module" :key="index" :style="child.style" />
  </div>
</template>

<script>
import { uid } from "uid";
import Vue from "vue";

/**
 * 布局组件，用于通过配置文件对子组件布局
 */
export default {
  name: "RootLayout",

  data() {
    return {
      // 子组件配置
      children: null,
    };
  },

  // 向子孙组件注入事件总线，实现模块内部消息通信
  provide() {
    return {
      bus: new Vue(),
      module: `module_${uid()}`,
    };
  },

  created() {},

  mounted() {},
  methods: {
    setChildren(children, path) {
      const modules = [];
      if (!children || !Array.isArray(children)) return;
      children.forEach(async (child) => {
        let module = await this._loadModule(child, path);
        let style = this._parseStyle(child.style);
        modules.push({
          module,
          style,
        });
      });
      this.children = modules;
    },

    // 加载模块
    async _loadModule(config, path) {
      const module = await import(`@/${config.uri}`);
      const theModule = this.$utils.object.deepCloneObj(module.default);
      let dataObj = {};
      if (theModule.data) {
        dataObj = theModule.data();
      }

      let configUrl = config.config;
      if (configUrl && configUrl.startsWith("./")) {
        configUrl = path + configUrl.substring(2);
      }
      console.log("configUrl", configUrl);

      dataObj.configUrl = configUrl;
      dataObj.states = config.states;
      dataObj.style = config.style;
      dataObj.changeStates = config.changeStates;
      theModule.data = () => dataObj;

      return theModule;
    },

    // 解析样式
    _parseStyle(style) {
      if (!style) return;
      let { left, top, right, bottom, width, height } = style;
      left = this.$utils.number.isPositiveInteger(left) ? `${left}px` : "";
      top = this.$utils.number.isPositiveInteger(top) ? `${top}px` : "";
      right = this.$utils.number.isPositiveInteger(right) ? `${right}px` : "";
      bottom = this.$utils.number.isPositiveInteger(bottom) ? `${bottom}px` : "";
      width = this.$utils.number.isPositiveInteger(width) ? `${width}px` : "";
      height = this.$utils.number.isPositiveInteger(height) ? `${height}px` : "";
      return {
        left,
        top,
        right,
        bottom,
        width,
        height,
      };
    },
  },
};
</script>

<style>
.jimu-root-layout {
  width: 100%;
  height: 100%;
  position: absolute;
}
</style>
