<template>
  <div id="app">
    <RootLayout ref="rootLayout"/>
  </div>
</template>

<script>

import RootLayout from '@/layouts/RootLayout'
import ConfigManager from '@/managers/ConfigManager'
import RouterManager from '@/managers/RouterManager'
import MenuManager from '@/managers/MenuManager'
import StyleManager from '@/managers/StyleManager'
export default {

  name: 'App',

  components: {
    RootLayout
  },

  async created(){
    // 初始化路由
    this.routerManager = new RouterManager()
    // 初始化样式
    this.styleManager = new StyleManager()
    // 初始化菜单
    this.menuManager = new MenuManager()
    // 读取配置文件
    this.configManager = new ConfigManager()
    let config = await this.configManager.load()
    // 开始渲染
    this.$refs.rootLayout.setChildren(config.children)

  }
}
</script>

<style>

html, body{
  height: 100%;
  width: 100%;
  margin: 0;
  overflow: hidden;
}

#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  width: 100%;
  height: 100%;
}
</style>
