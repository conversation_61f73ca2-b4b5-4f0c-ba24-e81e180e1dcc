const isPositiveInteger = (value) => {
  return /^(0|[1-9][0-9]*)$/.test(value);
};

const format = function (number, decimal, percentage) {
  if (isNaN(number) || isNaN(decimal)) return number;
  number = +number;
  if (percentage) {
    number *= 100;
    number = number.toFixed(decimal);
    return `${parseFloat(number)}%`;
  }
  return parseFloat(number.toFixed(decimal));
};

const bigDataFormat = function (value, factor, decimals, units) {
  //数据 倍数 保留小数 单位为数组 units[0]:小单位  unit[1]:大单位
  if (isNaN(value) || parseInt(factor) <= 0 || parseInt(decimals) < 0 || units.length != 2) return "";
  value = parseFloat(value);
  if (value > factor) return { value: parseFloat((value / factor).toFixed(decimals)), unit: units[1] };
  else return { value: parseFloat(value.toFixed(decimals)), unit: units[0] };
};

const divisor = function (value, factor, decimals = 2) {
  if (isNaN(value) || parseInt(factor) <= 0 || parseInt(decimals) < 0) return "";
  // 获取分母的长度处理基本的保留小数位
  let base = factor.toString().length + 1;
  let result =
    Number((value / factor).toFixed(decimals)) == 0 ? parseFloat((value / factor).toFixed(base)) : Number((value / factor).toFixed(decimals));
  return result;
};

export { isPositiveInteger, format, bigDataFormat, divisor };
