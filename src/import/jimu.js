import Vue from "vue";

// layouts
import RouterLayout from "@/layouts/RouterLayout";

// views
import DefaultView from "@/views/DefaultView";

// components
import TableComponent from "@/components/TableComponent";

// widgets
import HeaderWidget from "@/widgets/HeaderWidget";
import RouteMenuHWidget from "@/widgets/RouteMenuHWidget";
import RouteMenuVWidget from "@/widgets/RouteMenuVWidget";
import NavigationWidget from "@/widgets/NavigationWidget";
import MapWidget from "@/widgets/MapWidget";
import AdminWidget from "@/widgets/AdminWidget";
import DrawWidget from "@/widgets/DrawWidget";
import LayerWidget from "@/widgets/LayerWidget";
import LegendWidget from "@/widgets/LegendWidget";
import TableWidget from "@/widgets/TableWidget";
import InfoWidget from "@/widgets/InfoWidget";
import HMenuWidget from "@/widgets/HMenuWidget";
import VMenuWidget from "@/widgets/VMenuWidget";

// extend widgets
import RegionLayerStyleContralWidget from "@/app/widgets/RegionLayerStyleContralWidget";
import RegionAnalysisWidget from "@/app/widgets/RegionAnalysisWidget";
import RegionListChartWidget from "@/app/widgets/RegionListChartWidget";
import SpecialMapWidget from "@/app/widgets/SpecialMapWidget";
import SpecialTableListWidget from "@/app/widgets/SpecialTableListWidget";
import DropListWidget from "@/app/widgets/DropListWidget";
import InSpPcRegionMapWidget from "@/app/widgets/InSpPcRegionMapWidget";
import BatchMapWidget from "@/app/widgets/BatchMapWidget";
import BatchLayerWidget from "@/app/widgets/BatchLayerWidget";
import RegionMapFilterWidget from "@/app/widgets/RegionMapFilterWidget";
import RegionAnalysisChartWidget from "@/app/widgets/RegionAnalysisChartWidget";
import DmNumAnalysisWidget from "@/app/widgets/DmNumAnalysisWidget";
import PlantLayerContralWidget from "@/app/widgets/PlantLayerContralWidget";
import SpeciesLayerContralWidget from "@/app/widgets/SpeciesLayerContralWidget";
import Straw from "@/app/Views/Straw";
import Environment from "@/app/Views/Environment";
import PollutionInspection from "@/app/Views/PollutionInspection";
import LandAqlyTabWidget from "@/app/widgets/LandAqlyTabWidget/";
import NydLayerFilterWidget from "@/app/widgets/NydLayerFilterWidget/";

[
  RouterLayout,
  DefaultView,
  HeaderWidget,
  RouteMenuHWidget,
  RouteMenuVWidget,
  NavigationWidget,
  MapWidget,
  AdminWidget,
  DrawWidget,
  LayerWidget,
  LegendWidget,
  TableWidget,
  InfoWidget,
  HMenuWidget,
  VMenuWidget,
  TableComponent,
  RegionLayerStyleContralWidget,
  RegionAnalysisWidget,
  RegionListChartWidget,
  SpecialMapWidget,
  SpecialTableListWidget,
  DropListWidget,
  InSpPcRegionMapWidget,
  BatchMapWidget,
  BatchLayerWidget,
  RegionMapFilterWidget,
  RegionAnalysisChartWidget,
  DmNumAnalysisWidget,
  PlantLayerContralWidget,
  SpeciesLayerContralWidget,
  Straw,
  Environment,
  LandAqlyTabWidget,
  PollutionInspection,
  NydLayerFilterWidget,
].forEach((w) => {
  Vue.use(w);
});
