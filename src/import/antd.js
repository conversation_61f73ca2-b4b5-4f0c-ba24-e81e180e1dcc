/*
 *  ant-design-vue的组件引入
 * */
import Vue from "vue";
import {
  Button,
  Input,
  Row,
  Col,
  Icon,
  Pagination,
  Modal,
  InputNumber,
  Select,
  Checkbox,
  Table,
  Radio,
  Tabs,
  Tree,
  Empty,
  Dropdown,
  Collapse,
  Menu,
  Cascader,
  Statistic,
  Descriptions,
  Carousel,
  Message,
  Card,
  Notification,
  Spin,
  Drawer,
} from "ant-design-vue";

[
  Button,
  Input,
  Row,
  Col,
  Icon,
  Pagination,
  Modal,
  InputNumber,
  Select,
  Checkbox,
  Spin,
  Table,
  Radio,
  Tabs,
  Tree,
  Empty,
  Dropdown,
  Collapse,
  Menu,
  Cascader,
  Statistic,
  Descriptions,
  Carousel,
  Card,
  Drawer,
].forEach((c) => {
  Vue.use(c);
});

Vue.prototype.$message = Message;
Vue.prototype.$notification = Notification;
