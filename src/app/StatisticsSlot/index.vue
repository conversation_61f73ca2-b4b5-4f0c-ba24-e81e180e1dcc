<template>
  <div class="chart-wrap">
    <div class="header">
      <slot name="title"></slot>
    </div>
    <slot name="contain"></slot>
  </div>
</template>

<script>
export default {
  name: 'StatisticsSlot',
  data() {
    return {}
  },
  methods: {}
}
</script>

<style lang="less" scoped>
.chart-wrap {
  font-weight: 600;
  border-bottom: 1px solid #F1F3FD;
  margin-bottom: 20px;
  .header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    border-radius: 10px;
    padding: 0px 12px;
    margin-bottom: 14px;
    .title {
      margin-left: 5px;
      color: white;
      font-size: 25px;
    }
  }
}
</style>
