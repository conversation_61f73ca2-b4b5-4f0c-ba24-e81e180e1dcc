<template>
  <div id="jimu-popup-content">
    <div class="item" v-for="(item, index) in fields" :key="index">
      <span class="label">{{ item.label }}{{ item.label ? ":" : "" }}</span>
      <template v-if="item.type == 'title'">
        <div class="title">{{ item.title }}</div>
      </template>
      <template v-if="item.type == 'link'">
        <div>
          <a
            v-if="initUrl(item, data)"
            :href="apiUrl.document + plusUrl(item, data) + initUrl(item, data)"
            target="_blank"
            >{{ initUrl(item, data) || "" }} {{ item.unit }}</a
          >
        </div>
      </template>
      <template v-else-if="item.type == 'video'">
        <div v-for="(video, i) in initVideo(item, data)" :key="i">
          <!-- <a
            v-if="initUrl(item, data)"
            :href="apiUrl.document + plusUrl(item, data) + initUrl(item, data)"
            target="_blank"
            >{{ initUrl(item, data) || '' }} {{ item.unit }}</a
          > -->
          <a
            class="img-item"
            :href="apiUrl.document + video.uri"
            v-if="video"
            target="_blank"
          >
            {{ video.name }}
          </a>
        </div>
      </template>
      <template v-else-if="item.type == 'imgs'">
        <a-carousel class="carousel" arrows>
          <div
            slot="prevArrow"
            class="custom-slick-arrow"
            style="left: 10px; zindex: 1"
          >
            <a-icon type="left" />
          </div>
          <div slot="nextArrow" class="custom-slick-arrow" style="right: 10px">
            <a-icon type="right" />
          </div>
          <div v-for="(img, i) in initImage(item, data)" :key="i">
            <a
              class="img-item"
              :href="apiUrl.img + img"
              v-if="img"
              target="_blank"
            >
              <img class="img" :src="apiUrl.img + img" alt="" />
            </a>
          </div>
        </a-carousel>
      </template>
      <template v-else>
        <span class="value">{{ init(item, data) || "" }} {{ item.unit }}</span>
      </template>
    </div>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";

import { mapState } from "vuex";
export default {
  data() {
    return {};
  },
  props: {
    data: {
      type: Object,
    },
    fields: {
      type: Array,
    },
  },
  mixins: [BaseWidget],
  computed: {
    // 从全局配置中获取地图初始状态
    ...mapState({
      apiUrl: (state) => state.config.api,
    }),
  },
  mounted() {
    this.bus.$emit("onBaseClick", this.data);
  },

  methods: {
    geti() {
      return [1, 1, 1, 1, 1];
    },
    plusUrl(item, data) {
      return (
        (item.uri ? item.uri : "") +
        (item.uriField ? data[item.uriField] + "/" : "")
      );
      // return item
    },
    initUrl(item, data) {
      return data[item.field] == "null" ? "" : data[item.field];
    },
    initImage(item, data) {
      let value = data[item.field];
      if (value && value !== "null") {
        value = value.split("、").map((img) => {
          return (
            (item.uri ? item.uri : "") +
            (item.uriField ? data[item.uriField] + "/" : "") +
            img
          );
        });
      } else {
        value = [];
      }
      return value;
    },
    initVideo(item, data) {
      let value = data[item.field];
      if (value && value !== "null") {
        value = value.split("、").map((video) => {
          return {
            uri:
              (item.uri ? item.uri : "") +
              (item.uriField ? data[item.uriField] + "/" : "") +
              video,
            name: video,
          };
        });
      } else {
        value = [];
      }
      return value;
    },
    init(item, data) {
      let value = data[item.field];
      if (item.joint) {
        value = "";
        item.field.forEach((item) => {
          value += data[item] + "  ";
        });
      }
      if (value == "null") {
        return "";
      }
      return value;
    },
  },
};
</script>

<style lang="less" scoped>
#jimu-popup-content {
  width: 350px;
  font-size: 17px;
}
.carousel {
  width: 100%;
}
.title {
  font-size: 21px;
  font-weight: bold;
  padding: 0 8px 4px 0;
}
/deep/.ant-carousel .slick-slide {
  text-align: center;
  // background: #364d79;
  overflow: hidden;
}
/deep/.slick-track {
  display: flex;
  background: rgb(230, 230, 230);
  align-items: center;
}

/deep/.ant-carousel .custom-slick-arrow {
  width: 25px;
  height: 25px;
  font-size: 25px;
  color: #fff;
  background-color: rgba(31, 45, 61, 0.11);
  opacity: 0.3;
  z-index: 999;
}
/deep/.ant-carousel .custom-slick-arrow:before {
  display: none;
}
/deep/.ant-carousel .custom-slick-arrow:hover {
  opacity: 0.5;
}

/deep/.ant-carousel .slick-slide h3 {
  color: #fff;
}
.img-item {
  width: 100%;
  height: 200px;
  .img {
    width: 100%;
    height: 200px;
    object-fit: cover;
  }
}
.item {
  margin-top: 6px;
}
</style>
