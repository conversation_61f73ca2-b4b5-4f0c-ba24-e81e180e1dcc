<template>
  <div class="nav-widget">
    <div class="flex-column menu-list">
      <div
        class="menu-item"
        v-for="item in menuList"
        :key="item.key"
        :style="activeItem === item.key ? item.active : ''"
        :class="{ active: activeItem === item.key }"
        @click="changeMenu(item.key)"
      >
        {{ item.name }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: '',
  data() {
    return {
      menuList: [],
      activeItem: 'strawAll'
    }
  },
  props: {
    value: {
      type: String,
      default: () => {
        return ''
      }
    },
    config: {
      type: String,
      default: () => {
        return ''
      }
    }
  },
  components: {},
  mounted() {
    this.getMenuList()
  },
  methods: {
    async getMenuList(value) {
      const _val = value || this.value
      const data = await import(`./config/${_val}.json`)
      this.menuList = data.default
    },
    changeMenu(key) {
      this.activeItem = key
      this.$emit('click', key)
    }
  },
  watch: {
    value: () => {
      this.getMenuList()
    }
  }
}
</script>
<style lang="less" scoped>
.menu-list {
  display: flex;
}
.menu-item {
  line-height: 35px;
  text-align: center;
  width: 150px;
  height: 35px;
  border-radius: 3px;
  color: #4065E0;
  border: 1px solid #4065E0;
  margin-right: 30px;
  &:hover{
    cursor: pointer;
  }
}
</style>
