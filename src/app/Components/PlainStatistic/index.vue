<template>
  <div class="icon-statistic" :style="{ 'width': width }">
    <span class="value" :style="{ 'color': color }">{{value}}</span><span class="unit">{{unit}}</span>
    <span class="label">{{label}}</span>
  </div>
</template>

<script>

export default {

  name: 'PlainStatistic',

  props: {
    label: {
      type: String
    },
    value: {
      type: Number
    },
    unit: {
      type: String
    },
    color: {
      type: String
    },
    width: {
      type: String
    }
  }
  
}
</script>

<style lang="less" scoped>

.icon-statistic{

  display: inline-block;
  text-align: center;
  padding: 4px;

  .value{
    font-size: 24px;
    font-weight: 600;
  }

  .unit{
    font-size: 12px;
    padding-left: 8px;
  }

  .label{
    font-size: 12px;
    display: block;
  }

}
</style>
