<template>
  <div id="jimu-popup-content">
    <div class="item" v-for="(item, index) in fields" :key="index">
      <span class="label">{{ item.label }}{{ item.label ? ':' : '' }} </span>
      <span class="value">{{ resData[item.field] }}</span>
    </div>
  </div>
</template>

<script>
import BaseWidget from '@/BaseWidget'
export default {
  mixins: [BaseWidget],
  data() {
    return {
      resData: {}
    }
  },
  props: {
    data: {
      type: Object
    },
    fields: {
      type: Array
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    initUrl(item, data) {
      return data[item]
    },
    init(item, data) {
      let value = data[item.field]
      if (item.joint) {
        value = ''
        item.field.forEach(item => {
          value += data[item]
        })
      }
      if (item.type == 'imgs') {
        if (value && value !== 'null') {
          value = value.split('、')
        } else {
          value = []
        }
      }
      return value
    },
    async getData() {
      let param = {
        returnGeometry: false,
        format: 'json',
        filter: ['=', 'FXZQDM', this.data.FXZQDM]
      }
      let res = await this.$apis.feature.query('plant_resource_gx', param)
      let content = ''
      res.forEach(item => {
        content += item.cname
        content += '、'
      })
      this.$set(this.resData, 'cname', content)
      // this.cname = content
      this.$set(
        this.resData,
        'dlwz',
        res[0].PXZQMC + res[0].CXZQMC + res[0].FXZQMC
      )
    }
  }
}
</script>

<style lang="less" scoped>
.item {
  font-size: 17px;
  margin-top: 7px;
}
</style>