<template>
  <div id="jimu-popup-content">
    <a-row>
      <a-col
        :span="item.col ? 24 / item.col : 24"
        class="item"
        v-for="(item, index) in fields"
        :key="index"
      >
        <span class="label">{{ item.label }}</span> :
        <template v-if="item.type == 'link'">
          <a
            :href="apiUrl.document + item.uri + init(item, data)"
            target="_blank"
            >{{ init(item, data) || "" }} {{ item.unit }}</a
          >
        </template>
        <template v-else>
          <span class="value"
            >{{ init(item, data) || "" }} {{ item.unit }}</span
          >
        </template>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { mapState } from "vuex";
import dictionaries from "@/app/Dictionaries/dictionaries.js";
export default {
  name: "DefaultPopupContent",

  props: {
    data: {
      type: Object,
    },
    fields: {
      type: Array,
    },
  },

  data() {
    return {
      items: [],
      dictionaries,
      position: "",
    };
  },

  watch: {
    data() {
      this.init();
    },
  },
  computed: {
    // 从全局配置中获取地图初始状态
    ...mapState({
      apiUrl: (state) => state.config.api,
    }),
  },

  mounted() {
    this.init();
  },

  methods: {
    init(item, data) {
      if (!item && !data) return false;
      let value = data[item.field];
      if (item.label == "现场为非农用") {
        value = value ? value : "否";
      }
      if (item.type == "tr") {
        value = this.dictionaries["tr"][value];
      } else if (item.type == "ncp") {
        value = this.dictionaries["ncp"][value];
      } else if (item.dictionaries) {
        value = this.dictionaries.zw[value];
      }
      if (this.$utils.number.isPositiveInteger(item.decimals)) {
        value = parseFloat(value);
        if (!isNaN(value)) {
          value = parseFloat(value.toFixed(item.decimals));
        }
        if (value == 0) value = "0";
      }
      if (value === 0) return "0";
      return value;
    },
  },
};
</script>

<style lang="less" scoped>
#jimu-popup-content {
  max-width: 350px;
  .item {
    font-size: 17px;
    padding: 2px 0 2px 0;
    margin-top: 5px;
  }
}
</style>
