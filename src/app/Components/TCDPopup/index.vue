<template>
  <div id="jimu-popup-content">
    <a-row v-show="!showTable">
      <a-col :span="item.col ? 24 / item.col : 24" class="item" v-for="(item, index) in fields" :key="index">
        <span class="label">{{ item.label }}</span> :
        <template v-if="item.type == 'link'">
          <a :href="apiUrl.document + item.uri + init(item, data)" target="_blank">{{ init(item, data) || "" }} {{ item.unit }}</a>
        </template>
        <template v-else>
          <span class="value">{{ init(item, data) || "" }} {{ item.unit }}</span>
        </template>
      </a-col>
    </a-row>
    <a-button class="link" type="link" @click="getChildData" v-if="!showTable">{{ this.popupConfig.label || "物种信息" }} ></a-button>

    <a-table
      v-if="showTable"
      :columns="tableColumn"
      :data-source="tableData"
      :scroll="{ x: this.popupConfig.scrollX ? this.popupConfig.scrollX : true, y: 400 }"
      :pagination="false"
    ></a-table>
    <a-button class="link" type="link" @click="hideTable" v-if="showTable">点位信息 ></a-button>
  </div>
</template>

<script>
import { mapState } from "vuex";
import dictionaries from "@/app/Dictionaries/dictionaries.js";
import BaseWidget from "@/BaseWidget";
export default {
  name: "DefaultPopupContent",
  mixins: [BaseWidget],

  props: {
    data: {
      type: Object,
    },
    fields: {
      type: Array,
    },
    popupConfig: {
      type: Object,
    },
  },

  data() {
    return {
      items: [],
      dictionaries,
      position: "",
      tableColumn: [],
      showTable: false,
      tableData: [],
    };
  },

  watch: {
    data(e) {
      console.log(e);
    },
  },
  computed: {
    // 从全局配置中获取地图初始状态
    ...mapState({
      apiUrl: (state) => state.config.api,
    }),
  },

  mounted() {
    console.log(this.data, this.fields, this.popupConfig);
    this.bus.$emit("tcdPopupClickHandler", this.data);
  },

  methods: {
    hideTable() {
      this.showTable = false;
    },
    async getChildData() {
      this.tableColumn = this.popupConfig.childrenTable.fields.map((item) => {
        return {
          dataIndex: item.field,
          title: item.label,
        };
      });
      let filter = this.popupConfig.childrenTable.filter
        ? [
            "all",
            this.popupConfig.childrenTable.filter,
            ["=", this.popupConfig.childrenTable.primaryKey, this.data[this.popupConfig.childrenTable.primaryKey]],
          ]
        : ["=", this.popupConfig.childrenTable.primaryKey, this.data[this.popupConfig.childrenTable.primaryKey]];
      let res = await this.$apis.feature.query(this.popupConfig.childrenTable.table, {
        filter,
        format: "json",
      });
      console.log(res);
      this.tableData = res;
      this.showTable = true;
    },
    init(item, data) {
      let value = data[item.field];

      if (this.$utils.number.isPositiveInteger(item.decimals)) {
        value = parseFloat(value);
        if (!isNaN(value)) {
          value = parseFloat(value.toFixed(item.decimals));
        }
      }
      if (value === 0) return "0";
      return value;
    },
  },
};
</script>

<style lang="less" scoped>
#jimu-popup-content {
  // width: 500px;
  .item {
    font-size: 17px;
    padding: 2px 0 2px 0;
    margin-top: 5px;
  }
  .link {
    font-size: 18px;
    margin-top: 10px;
  }

  /deep/.ant-table-thead > tr > th,
  /deep/.ant-table-tbody > tr > td {
    font-size: 17px;
  }
}
</style>
