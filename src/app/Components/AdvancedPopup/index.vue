<template>
  <div id="jimu-popup-content">
    <template v-if="loaded">
      <div class="item" v-for="(item, index) in fields" :key="index">
        <template v-if="showField(item, data)">
          <span class="label">{{ item.label }}{{ item.label ? ":" : "" }}</span>
          <!-- 标题 -->
          <template v-if="item.type == 'title'">
            <div class="title">{{ item.title }}</div>
          </template>
          <template v-else-if="item.type == 'data'">
            <advanced-popup
              :data="getFormData(item)"
              :fields="item.data"
              :none-show="item.noneShow"
            ></advanced-popup>
          </template>
          <template v-else-if="item.type == 'list'">
            <div
              v-for="(items, ids) in getListData(item, data)"
              :key="ids"
              style="margin-bottom: 10px"
            >
              <span class="label">{{ `${item.listLabel}_${ids + 1}：` }}</span>
              <span class="value"
                >{{ init(items, data) || "" }} {{ items.unit }}</span
              >
            </div>
          </template>
          <template v-else-if="item.type == 'link'">
            <div>
              <a
                v-if="initUrl(item, data)"
                :href="
                  apiUrl.document + plusUrl(item, data) + initUrl(item, data)
                "
                target="_blank"
                >{{ initUrl(item, data) || "" }} {{ item.unit }}</a
              >
            </div>
          </template>
          <template v-else-if="item.type == 'video'">
            <div v-for="(video, i) in initVideo(item, data)" :key="i">
              <a
                class="img-item"
                :href="apiUrl.document + video.uri"
                v-if="video"
                target="_blank"
              >
                {{ video.name }}
              </a>
            </div>
          </template>
          <template v-else-if="item.type == 'imgs'">
            <a-carousel class="carousel" arrows>
              <div
                slot="prevArrow"
                class="custom-slick-arrow"
                style="left: 10px; z-index: 1"
              >
                <a-icon type="left" />
              </div>
              <div
                slot="nextArrow"
                class="custom-slick-arrow"
                style="right: 10px"
              >
                <a-icon type="right" />
              </div>
              <div v-for="(img, i) in initImage(item, data)" :key="i">
                <a
                  class="img-item"
                  :href="apiUrl.img + img"
                  v-if="img"
                  target="_blank"
                >
                  <img class="img" :src="apiUrl.img + img" alt="" />
                </a>
              </div>
            </a-carousel>
          </template>
          <template v-else>
            <span class="value"
              >{{ init(item, data) || "" }} {{ item.unit }}</span
            >
          </template>
        </template>
      </div>
    </template>
    <template v-else>
      <div class="loading">
        <a-spin size="large" />
        <span>信息加载中...</span>
      </div>
    </template>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";

import { mapState } from "vuex";
export default {
  name: "AdvancedPopup",
  data() {
    return {
      formData: {},
      loaded: false,
    };
  },
  props: {
    data: {
      type: Object,
    },
    fields: {
      type: Array,
    },
    noneShow: {
      type: Boolean,
      default: true,
    },
  },
  mixins: [BaseWidget],
  computed: {
    // 从全局配置中获取地图初始状态
    ...mapState({
      apiUrl: (state) => state.config.api,
    }),
  },
  async mounted() {
    this.loaded = false;
    await this.initFormData();
    this.loaded = true;
    this.bus.$emit("onBaseClick", this.data);
  },

  methods: {
    // 处理list类型的数据
    getListData(info, data) {
      if (info.filterNone) {
        return info.data.filter((item) => {
          return data[item.field] ? true : false;
        });
      } else {
        return info.data;
      }
    },
    showField(item, data) {
      // 如果没有类型，则默认判断为值显示
      if (!item.type) {
        // 没数据时是否要展示
        if (!this.noneShow) {
          return data[item.field] ? true : false;
        }
        return true;
      } else {
        return true;
      }
    },
    getFormData(data) {
      let key = data.key ? data.key : data.formName;
      return this.formData[key][0];
    },
    geti() {
      return [1, 1, 1, 1, 1];
    },
    plusUrl(item, data) {
      return (
        (item.uri ? item.uri : "") +
        (item.uriField ? data[item.uriField] + "/" : "")
      );
      // return item
    },
    initUrl(item, data) {
      return data[item.field] == "null" ? "" : data[item.field];
    },
    initImage(item, data) {
      let value = data[item.field];
      if (value && value !== "null") {
        value = value.split("、").map((img) => {
          return (
            (item.uri ? item.uri : "") +
            (item.uriField ? data[item.uriField] + "/" : "") +
            img
          );
        });
      } else {
        value = [];
      }
      return value;
    },
    initVideo(item, data) {
      let value = data[item.field];
      if (value && value !== "null") {
        value = value.split("、").map((video) => {
          return {
            uri:
              (item.uri ? item.uri : "") +
              (item.uriField ? data[item.uriField] + "/" : "") +
              video,
            name: video,
          };
        });
      } else {
        value = [];
      }
      return value;
    },
    init(item, data) {
      let value = data[item.field];
      if (item.joint) {
        value = "";
        item.field.forEach((item) => {
          value += data[item] + "  ";
        });
      }

      if (this.$utils.number.isPositiveInteger(item.decimals)) {
        value = parseFloat(value);
        if (!isNaN(value)) {
          value = parseFloat(value.toFixed(item.decimals));
        }
        if (value == 0) value = "0";
      }

      if (value == "null") {
        return "";
      }
      return value;
    },
    async initFormData() {
      this.formData = {};

      for (const item of this.fields) {
        if (item.type == "data") {
          let params = {
            format: "json",
            returnGeometry: false,
            filter: ["=", item.field, this.data[item.field]],
          };
          let data = await this.$apis.feature.query(item.formName, params);
          let key = item.key ? item.key : item.formName;
          this.formData[key] = data;
        }
      }
    },
  },
};
</script>

<style lang="less" scoped>
#jimu-popup-content {
  max-height: 450px;
  overflow-y: scroll;
  font-size: 17px;
  &::-webkit-scrollbar {
    display: none !important;
  }
}
.carousel {
  width: 100%;
}
.title {
  font-size: 18px;
  font-weight: bold;
  padding: 4px 0;
}
/deep/.ant-carousel .slick-slide {
  text-align: center;
  // background: #364d79;
  overflow: hidden;
}
/deep/.slick-track {
  display: flex;
  background: rgb(230, 230, 230);
  align-items: center;
}

/deep/.ant-carousel .custom-slick-arrow {
  width: 25px;
  height: 25px;
  font-size: 25px;
  color: #fff;
  background-color: rgba(31, 45, 61, 0.11);
  opacity: 0.3;
  z-index: 999;
}
/deep/.ant-carousel .custom-slick-arrow:before {
  display: none;
}
/deep/.ant-carousel .custom-slick-arrow:hover {
  opacity: 0.5;
}

/deep/.ant-carousel .slick-slide h3 {
  color: #fff;
}
.img-item {
  width: 100%;
  height: 200px;
  .img {
    width: 100%;
    height: 200px;
    object-fit: cover;
  }
}
.item {
  margin-top: 6px;
}
.data-item {
  display: flex;
  margin-top: 6px;
  .data-label {
    flex-shrink: 0;
  }
  .data-value {
    flex: 1;
    margin-left: 10px;
  }
}
.loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 300px;
  height: 300px;
  span {
    font-size: 20px;
    margin-top: 20px;
  }
}
</style>
