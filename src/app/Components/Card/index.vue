<template>
  <div>
    <a-card>
      <TitleBar :title="title" v-if="title"></TitleBar>
      <slot>
        <span></span>
      </slot>
    </a-card>
  </div>
</template>

<script>
import TitleBar from '../TitleBar'
export default {
  name: 'Card',
  components: {
    TitleBar
  },
  props:{
    title:{
      type:String
    },
    title:{
      type:String
    }
  },
  data() {
    return {}
  },
  methods: {}
}
</script>

<style lang="less" scoped>
/deep/.ant-card-body {
  padding: 8px;
}
</style>