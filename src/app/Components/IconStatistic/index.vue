<template>
  <div class="icon-statistic" :style="{ 'width': width }">
    <a-row type="flex" align="middle" :gutter="8">
      <a-col :span="10">
        <span class="icon-wrapper" :style="{ 'color': color, 'background': bgColor}">
          <span class="gt-icon icon" :class="icon"></span>
        </span>
      </a-col>
      <a-col :span="14" class="value-wrapper">
        <span class="value">{{value}}<span class="unit">{{unit}}</span></span>
        <span class="label">{{label}}</span>
      </a-col>
    </a-row>
  </div>
</template>

<script>

export default {

  name: 'IconStatistic',

  props: {
    icon: {
      type: String
    },
    label: {
      type: String
    },
    value: {
      type: Number
    },
    unit: {
      type: String
    },
    color: {
      type: String
    },
    bgColor: {
      type: String
    },
    width: {
      type: String
    }
  }
  
}
</script>

<style lang="less" scoped>

.icon-statistic{

  display: inline-block;
  text-align: center;
  padding: 12px;
  width: 100%;

  .icon-wrapper{
    padding: 8px;
    border-radius: 50%;
    font-size: 20px;
    width: 48px;
    height: 48px;
    display: inline-block;
  }

  .value-wrapper{

    text-align: left;
    padding-left: 8px;

    .value{
      font-size: 24px;
      font-weight: 600;
    }

    .unit{
      font-size: 12px;
      padding-left: 8px;
    }

    .label{
      font-size: 12px;
      display: block;
    }

  }

}
</style>
