<template>
  <div class="jimu-popup-content">
    <div class="item" v-for="(item, index) in fields" :key="index">
      <span class="label">{{ item.label }}{{ item.label ? ':' : '' }}</span>
      <template v-if="item.type == 'video'">
        <a-carousel class="carousel" arrows>
          <div
            slot="prevArrow"
            class="custom-slick-arrow"
            style="left: 10px; zindex: 1"
          >
            <a-icon type="left" />
          </div>
          <div slot="nextArrow" class="custom-slick-arrow" style="right: 10px">
              <a-icon type="right" />
          </div>
           <video v-for="(video,index) in initVideo(item,data)" :key="index" width="350" height="240" controls>
              <source :src="data[item.video]" type="video/mp4">
              <source :src="item[item.video]" type="video/ogg">
            </video>
      </a-carousel>
       
      </template>
      <template v-else>
        <span class="value">{{ init(item, data) || '' }} {{ item.unit }}</span>
      </template>
    </div>
  </div>
</template>

<script>
import BaseWidget from '@/BaseWidget'

import { mapState } from 'vuex'

export default {
    name: 'index',

    mixins: [BaseWidget],

    computed: {
    // 从全局配置中获取地图初始状态
      ...mapState({
        apiUrl: state => state.config.api
      })
    },

    props: {
      data: {
        type: Object
      },
      fields: {
        type: Array
      }
    },

    data() {
      return {
        videoArr:[]
      };
    },
    methods: {
      initVideo(item, data) {
        let value = data[item.field]
        if (value && value !== 'null') {
          value = value.split('、').map(video => {
            return (
              (item.uri ? item.uri : '') +
              (item.uriField ? data[item.uriField] + '/' : '') +
              video
            )
          })
        } else {
          value = []
        }
        return value
      },
      init(item, data) {
        let value = data[item.field]
        if (item.joint) {
          value = ''
          item.field.forEach(item => {
            value += data[item] + '  '
          })
        }
        if(value=='null'){
          return ''
        }
        return value
      }
    },

    mounted() {
    this.bus.$emit('onBaseClick', this.data)
  },
};
</script>

<style lang="less" scoped>
#jimu-popup-content {
  width: 350px;
  font-size: 17px;
}
.carousel {
  width: 100%;
}
/deep/.ant-carousel .slick-slide {
  text-align: center;
  // background: #364d79;
  overflow: hidden;
}
/deep/.slick-track {
  display: flex;
  background: rgb(230, 230, 230);
  align-items: center;
}

/deep/.ant-carousel .custom-slick-arrow {
  width: 25px;
  height: 25px;
  font-size: 25px;
  color: #fff;
  background-color: rgba(31, 45, 61, 0.11);
  opacity: 0.3;
  z-index: 999;
}
/deep/.ant-carousel .custom-slick-arrow:before {
  display: none;
}
/deep/.ant-carousel .custom-slick-arrow:hover {
  opacity: 0.5;
}

/deep/.ant-carousel .slick-slide h3 {
  color: #fff;
}
</style>
