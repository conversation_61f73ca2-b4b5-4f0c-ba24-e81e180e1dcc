<template>
  <div id="jimu-popup-content">
    <a-carousel class="carousel" arrows>
      <div
        slot="prevArrow"
        class="custom-slick-arrow"
        style="left: 10px; zindex: 1"
      >
        <a-icon type="left" />
      </div>
      <div slot="nextArrow" class="custom-slick-arrow" style="right: 10px">
        <a-icon type="right" />
      </div>
      <template v-for="(item, index) in fields">
        <template v-if="item.type == 'image'">
          <a
            class="img-item"
            :href="apiUrl.img + 'sl' + init(item, data)"
            target="_blank"
          >
            <img
              class="img"
              :key="index"
              :src="apiUrl.img + 'sl' + init(item, data)"
              alt=""
            />
          </a>
        </template>
      </template>
    </a-carousel>
    <div class="item" v-for="(item, index) in fields" :key="index">
      <template v-if="item.type !== 'image'">
        <span class="label">{{ item.label }}</span> :
        <template v-if="item.type == 'link'">
          <a
            :href="apiUrl.img + 'jsbg/' + init(item, data) + '.PDF'"
            target="_blank"
            >{{ init(item, data) || '' }} {{ item.unit }}</a
          >
        </template>
        <template v-else>
          <span class="value"
            >{{ init(item, data) || '' }} {{ item.unit }}</span
          >
        </template>
      </template>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import dictionaries from '@/app/Dictionaries/dictionaries.js'
export default {
  name: 'DefaultPopupContent',

  props: {
    data: {
      type: Object
    },
    fields: {
      type: Array
    }
  },

  data() {
    return {
      items: [],
      dictionaries,
      position: ''
    }
  },

  watch: {
    data() {
      this.init()
    }
  },
  computed: {
    // 从全局配置中获取地图初始状态
    ...mapState({
      apiUrl: state => state.config.api
    })
  },

  mounted() {
    this.init()
  },

  methods: {
    init(item, data) {
      let value = data[item.field]
      if (this.$utils.number.isPositiveInteger(item.decimals)) {
        value = parseFloat(value)
        if (!isNaN(value)) {
          value = value.toFixed(item.decimals)
        }
      }
      return value
    }
  }
}
</script>

<style lang="less" scoped>
#jimu-popup-content {
  .item {
    font-size: 17px;
    padding: 2px 0 2px 0;
  }
}
</style>


<style lang="less" scoped>
#jimu-popup-content {
  max-width: 350px;
}
.carousel {
  width: 100%;
  background-color: rgb(230, 230, 230);
}
/deep/.ant-carousel .slick-slide {
  text-align: center;
  overflow: hidden;
}
/deep/.ant-carousel .custom-slick-arrow {
  width: 25px;
  height: 25px;
  font-size: 25px;
  color: #fff;
  background-color: rgba(31, 45, 61, 0.5);
  opacity: 0.3;
  z-index: 999;
}
/deep/.ant-carousel .custom-slick-arrow:before {
  display: none;
}
/deep/.ant-carousel .custom-slick-arrow:hover {
  opacity: 0.5;
}

/deep/.ant-carousel .slick-slide h3 {
  color: #fff;
}
.img-item {
  width: 100%;
  height: 100%;

  .img {
    width: 100%;
    height: 100%;
  }
}
</style>