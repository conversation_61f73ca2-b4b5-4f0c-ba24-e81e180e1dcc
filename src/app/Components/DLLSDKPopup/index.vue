<template>
  <div id="jimu-popup-content">
    <div v-show="!showTable">
      <!-- <div class="title" v-show="title">{{ title }}</div> -->
      <div class="item" v-for="(item, index) in items" :key="index">
        <template v-if="item.type == 'link'">
          <template v-if="item.value !== 'null'">
            <span class="label">{{ item.label }}</span> :
            <a
              :href="apiUrl.document + (item.uri ? item.uri : '') + item.value"
              class="value"
              target="_blank"
              >{{ item.value || "" }} {{ item.unit }}</a
            >
          </template>
        </template>
        <template v-else>
          <span class="label">{{ item.label }}</span> :
          <span class="value">{{ item.value || "" }} {{ item.unit }}</span>
        </template>
      </div>
      <a-button
        class="link"
        type="link"
        @click="getChildData"
        v-if="!showTable"
      >
        作物信息 >
      </a-button>
    </div>
    <div v-show="showTable">
      <div v-show="!showTable1">
        <a-table
          v-if="showTable"
          :columns="tableColumn"
          :data-source="tableData"
          :scroll="{ x: '1200px', y: '400px' }"
          :pagination="false"
        >
          <template slot="operation" slot-scope="text, record">
            <a-button type="link" @click="rowClick1(record)">详情</a-button>
          </template></a-table
        >
        <a-button
          class="link"
          type="link"
          @click="showTable = false"
          v-if="showTable"
          >点位信息 ></a-button
        >
      </div>
      <div v-show="showTable1">
        <a-table
          v-if="showTable1"
          :columns="tableColumn1"
          :data-source="tableData1"
          :scroll="{ x: '1200px', y: '400px' }"
          :pagination="false"
        ></a-table>
        <a-button
          class="link"
          type="link"
          @click="showTable1 = false"
          v-if="showTable"
          >作物信息 ></a-button
        >
      </div>
    </div>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";

export default {
  data() {
    return {
      title: null,
      items: [],
      showTable: false,
      showTable1: false,
      tableColumn: [],
      tableData: [],
      tableColumn1: [
        { dataIndex: "施肥类型", title: "施肥类型" },
        { dataIndex: "肥料种类", title: "肥料种类" },
        { dataIndex: "施肥方式", title: "施肥方式" },
        { dataIndex: "施用量", title: "施用量" },
        { dataIndex: "n养分含量", title: "n养分含量" },
        { dataIndex: "p2o5养分含量", title: "P2O5养分含量" },
        { dataIndex: "k2o养分含量", title: "K2O养分含量" },
        { dataIndex: "施肥时间", title: "施肥时间" },
      ],
      tableData1: [],
    };
  },
  props: {
    data: {
      type: Object,
    },
    popupConfig: {
      type: Object,
    },
    popup: {
      type: Object,
    },
    defaultWidth: {
      type: String,
    },
  },
  mixins: [BaseWidget],

  mounted() {
    console.log(this.popupConfig, this.data, "popupConfig");
    this.init();
  },

  methods: {
    init() {
      this.title = this._getPopupTitle(this.popupConfig, this.data);
      this.items = this.popupConfig.fields.map((item) => {
        return {
          label: item.label,
          value: this.data[item.field] == 0 ? "0" : this.data[item.field],
          type: item.type,
          uri: item.uri,
        };
      });
    },
    _getPopupTitle(popup, props) {
      if (!popup.title || !props) return;
      // 正则匹配
      const match = popup.title.match(/\{(.+?)\}/g);
      // 如果没有匹配{xxx}则返回原始内容作为标题
      if (!match) return popup.title;
      // 对匹配的内容用props中的值替换
      let title = popup.title;
      match.forEach((m) => {
        const field = `${m}`.substr(1, m.length - 2);
        let value = props[field];
        value = value === undefined ? "" : value;
        title = title.replace(`${m}`, value);
      });
      return title;
    },
    async getChildData() {
      let tableColumn = this.popupConfig.childrenTable.fields.map((item) => {
        return {
          dataIndex: item.field,
          title: item.label,
        };
      });
      tableColumn.push({
        // width: 50,
        title: "",
        dataIndex: "operation",
        scopedSlots: { customRender: "operation" },
      });
      this.tableColumn = tableColumn;
      let filter = this.popupConfig.childrenTable.filter
        ? [
            "all",
            this.popupConfig.childrenTable.filter,
            [
              "=",
              this.popupConfig.childrenTable.primaryKey,
              this.data[this.popupConfig.childrenTable.primaryKey],
            ],
          ]
        : [
            "=",
            this.popupConfig.childrenTable.primaryKey,
            this.data[this.popupConfig.childrenTable.primaryKey],
          ];
      let res = await this.$apis.feature.query(
        this.popupConfig.childrenTable.table,
        {
          filter,
          format: "json",
        }
      );
      console.log(res);
      this.tableData = res;
      // console.log(this.popup);
      // this.popup.setMaxWidth("700px");
      this.showTable = true;
    },

    hideTable() {
      // this.popup.setMaxWidth(this.defaultWidth);
      this.showTable = false;
    },
    async rowClick1(e) {
      console.log(e);
      let res = await this.$apis.feature.query("dlls_dkfl", {
        filter: [
          "all",
          ["=", "地块编码", e["地块编码"]],
          ["=", "作物名称", e["作物名称"]],
        ],
        format: "json",
      });
      console.log(res);
      this.tableData1 = res;
      this.showTable1 = true;
    },
  },
};
</script>

<style lang="less" scoped>
#jimu-popup-content {
  padding-top: 10px;
  font-size: 17px;

  .title {
    font-size: 21px;
    font-weight: bold;
    padding: 0 8px 4px 0;
  }

  .item {
    font-size: 17px;
    padding: 2px 0 2px 0;
  }
  .link {
    font-size: 18px;
    margin-top: 10px;
  }
}
</style>
