<template>
  <div class="popup-content">
    <div class="item" v-for="(item, index) in arr" :key="index">
      <span class="label"
        >{{ item.label }} :
        {{ item.dict ? dict[data[item.field]] : data[item.field] }}</span
      >
    </div>
    <div class="chart-wrap">
      <Chart @barClick="barClick" :options="option" />
    </div>
    <div class="year-wrap">
      <div v-for="(item, index) in list" :key="index">
        <a-statistic
          :title="item"
          :value="data[param['all'][index]]"
          class="demo-class"
          :value-style="{ color: colors[index] }"
        >
          <template #suffix>
            <span> 个</span>
          </template>
        </a-statistic>
      </div>
    </div>
    <template v-if="year">
      <div>{{ year }}年点位</div>
      <div class="year-wrap">
        <div v-for="(item, index) in list" :key="index">
          <a-statistic
            :title="item"
            :value="data[param[year][index]]"
            class="demo-class"
            :value-style="{ color: colors[index] }"
          >
            <template #suffix>
              <span> 个</span>
            </template>
          </a-statistic>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import Chart from "@/components/Chart";
export default {
  components: { Chart },
  data() {
    return {
      year: null,
      list: ["合格", "不合格", "重度超标", "合计"],
      colors: [
        "rgb(0,179,0)",
        "rgb(179,179,0)",

        "rgb(179,0,0)",
        "rgb(6, 34, 86)",
      ],
      param: {
        2017: ["one_17", "two_17", "three_17", "all_17"],
        2018: ["one_18", "two_18", "three_18", "all_18"],
        2019: ["one_19", "two_19", "three_19", "all_19"],
        2020: ["one_20", "two_20", "three_20", "all_20"],
        all: ["one_all", "two_all", "three_20", "all_all"],
      },
      dict: {
        1: "优先保护",
        2: "安全利用",
        3: "严格管控",
      },
      arr: [
        { label: "农用地面积", field: "mj_mu" },
        { label: "五项综合等级", field: "zh_class", dict: true },
        { label: "市", field: "CXZQMC" },
        { label: "区/县", field: "FXZQMC" },
        { label: "镉等级", field: "cd_class", dict: true },
        { label: "汞等级", field: "hg_class", dict: true },
        { label: "砷等级", field: "as_class", dict: true },
        { label: "铅等级", field: "pb_class", dict: true },
        { label: "铬等级", field: "cr_class", dict: true },
      ],
    };
  },
  computed: {
    option() {
      return {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          formatter: "{b0}:<br /> {c0}(%)",
        },
        xAxis: {
          type: "category",
          data: ["2017", "2018", "2019", "2020"],
        },
        grid: {
          top: "5%",
          left: "5%",
          right: "3%",
          bottom: "5%",
          containLabel: true,
        },
        yAxis: {
          max: 100,
          type: "value",
          axisLabel: {
            formatter: "{value} %",
          },
        },
        series: [
          {
            name: "合格率",
            data: [
              this.data["hgl_17"] ? Number(this.data["hgl_17"]) * 100 : null,
              this.data["hgl_18"] ? Number(this.data["hgl_18"]) * 100 : null,
              this.data["hgl_19"] ? Number(this.data["hgl_19"]) * 100 : null,
              this.data["hgl_20"] ? Number(this.data["hgl_20"]) * 100 : null,
            ],
            type: "line",
          },
        ],
      };
    },
  },
  props: {
    data: {
      type: Object,
    },
    fields: {
      type: Array,
    },
  },
  methods: {
    barClick(e) {
      if (e.data[0].value || e.data[0].value == 0) {
        this.year = e.name;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.popup-content {
  font-size: 17px;
  .item {
    margin-left: 16px;
    margin-top: 7px;
  }
}
.chart-wrap {
  margin-top: 20px;
  width: 300px;
  height: 200px;
}
.year {
  margin-left: 20px;
}
.year-wrap {
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
}
</style>
