<template>
  <div style="width:100%">
    <a-cascader
      class="city-cascader"
      :options="options"
      change-on-select
      :load-data="loadData"
      @change="onChange"
      placeholder="广西"
    />
  </div>
</template>
<script>
export default {
  data() {
    return {
      options: [
        {
          value: '45',
          label: '广西',
          isLeaf: false
        }
      ],
      cityParam: [
        {
          url: 'gx_land_region_city',
          pcode: 'PXZQDM',
          code: 'CXZQDM',
          name: 'CXZQMC'
        },
        {
          url: 'gx_land_region_county',
          pcode: 'CXZQDM',
          code: 'FXZQDM',
          name: 'FXZQMC'
        },
        {
          url: 'gx_land_region_town',
          pcode: 'FXZQDM',
          code: 'TXZQDM',
          name: 'TXZQMC'
        }
      ]
    }
  },
  methods: {
    onChange(value, a) {
      if (value.length == 0) {
        value = ['45']
      }
      this.$emit(
        'change',
        value[value.length - 1],
        value,
        a[a.length - 1].label
      )
    },
    async loadData(option) {
      const targetOption = option[option.length - 1]
      const url = this.cityParam[option.length - 1].url
      const pcodeKey = this.cityParam[option.length - 1].pcode
      const codeKey = this.cityParam[option.length - 1].code
      const nameKey = this.cityParam[option.length - 1].name
      const code = option[option.length - 1].value
      let list = await this.getData(url, ['=', pcodeKey, code])
      const childrens = list.map(item => {
        let i = { label: item[nameKey], value: item[codeKey] }
        if (option.length < 2) {
          i.isLeaf = false
        }
        return i
      })
      targetOption.children = childrens
      this.options = [...this.options]
    },
    async getData(url, filter) {
      const params = {
        returnGeometry: false,
        format: 'json',
        filter
      }
      let data = await this.$apis.feature.query(url, params)
      return data
    }
  }
}
</script>
<style lang="less" scoped>
.city-cascader {
  width: 100%;
  height: 100%;
}
</style>