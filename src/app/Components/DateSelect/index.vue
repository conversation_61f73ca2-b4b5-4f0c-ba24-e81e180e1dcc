<template>
  <div v-if="year.length > 0">
    <a-select
      :default-value="thisYear"
      style="width: 100%"
      @change="handleChange"
    >
      <template v-for="(item, index) in year">
        <a-select-option :key="index" :value="item">
          {{ item }}
        </a-select-option>
      </template>
    </a-select>
  </div>
</template>

<script>
import { year } from "./config/year.config";
export default {
  props: {
    years: {
      type: Array,
    },
  },
  data() {
    return {
      year: [],
      thisYear: "",
    };
  },
  mounted() {
    const years = this.years.map((item) => {
      return item.toString();
    });
    this.year = years.length > 0 ? years : year;
    this.thisYear = years.length > 0 ? years[0] : year[0];
    console.log(this.thisYear);
  },
  methods: {
    handleChange(e, i) {
      const lastYear = this.year[i.key - 1];
      this.$emit("change", { year: e, lastYear: lastYear });
    },
  },
};
</script>

<style></style>
