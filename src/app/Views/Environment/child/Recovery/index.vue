<template>
  <div class="recovery">
    <TitleBar class="title" :title="title"></TitleBar>
    <Table :filterObj="filterObj" :config="params" class="table"></Table>
  </div>
</template>

<script>
import TitleBar from "@/app/Components/TitleBar/";
import Table from "../../component/Table";
import params from "./config/config.js";
export default {
  name: "Recovery",
  components: {
    TitleBar,
    Table,
  },
  props: {
    filterObj: {
      type: Object,
    },
  },
  data() {
    return {
      params,
      title: "全区农膜回收率",
    };
  },
  methods: {},
};
</script>

<style lang="less" scoped>
.recovery {
  height: 100%;
  background-color: #fff;
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  .table {
    // 滚动
    max-height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
  }
  .title {
    font-size: 24px;
  }
}
</style>
