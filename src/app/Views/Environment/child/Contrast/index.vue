<template>
  <div>
    <TitleBar class="title" :title="'农膜使用情况对比'"></TitleBar>
    <div class="nowDate">
      <div>
        <h2>{{ year }}年对比情况</h2>
        <div class="info">
          <span class="zl">农膜使用总量</span><br />
          <span class="num"
            >{{ formatNumber(nowTableData.nm_use) }}<span>万吨</span></span
          >
        </div>
        <div class="info">
          <span class="zl">农膜回收量</span><br />
          <span class="num"
            >{{ formatNumber(nowTableData.nm_hsl) }}<span>万吨</span></span
          >
        </div>
        <div class="info">
          <span class="zl">农膜回收率</span><br />
          <span class="num"
            >{{ Number(nowTableData.nm_rate).toFixed(2) }}<span>%</span></span
          >
        </div>
      </div>
      <div>
        <h2>上年对比情况</h2>
        <div class="info">
          <span class="zl">农膜使用总量</span><br />
          <span class="num"
            >{{ lastTableData ? formatNumber(lastTableData.nm_use) : "暂无数据"
            }}<span>万吨</span></span
          >
        </div>
        <div class="info">
          <span class="zl">农膜回收量</span><br />
          <span class="num"
            >{{ lastTableData ? formatNumber(lastTableData.nm_hsl) : "暂无数据"
            }}<span>万吨</span></span
          >
        </div>
        <div class="info">
          <span class="zl">农膜回收率</span><br />
          <span class="num"
            >{{
              lastTableData
                ? Number(lastTableData.nm_rate).toFixed(2)
                : "暂无数据"
            }}<span>%</span></span
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import TitleBar from "@/app/Components/TitleBar/";
export default {
  name: "Contrast",
  props: {
    address: {
      type: String,
    },
    year: {
      type: Object,
    },
    lastYear: {
      type: Object,
    },
  },
  watch: {
    address() {
      this.getNowData();
      this.getLastData();
    },
    year() {
      this.getNowData();
      this.getLastData();
    },
    lastYear() {
      this.getLastData();
    },
  },

  components: {
    TitleBar,
  },
  mounted() {
    this.getNowData();
    this.getLastData();
  },
  data() {
    return {
      nowTableData: "",
      lastTableData: "",
    };
  },
  methods: {
    formatNumber(num) {
      return num.toLocaleString("en-US", {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      });
    },
    async getNowData() {
      let params = {
        returnGeometry: false,
        format: "json",
        filter: ["all", ["=", "date", this.year], ["=", "qymc", this.address]],
      };

      // let data = await this.$apis.feature.query(`film_recover`, params)
      let data = await this.$apis.feature.query(`film_gx`, params);
      console.log(data, params, "今年");
      data.forEach((item) => {
        item.nm_use = item.nm_use / 10000;
        item.nm_hsl = item.nm_hsl / 10000;
      });
      this.nowTableData = data[0];
    },
    async getLastData() {
      let params = {
        returnGeometry: false,
        format: "json",
        filter: [
          "all",
          ["=", "date", this.lastYear],
          ["=", "qymc", this.address],
        ],
      };
      // let data = await this.$apis.feature.query(`film_recover`, params)
      let data = await this.$apis.feature.query(`film_gx`, params);
      data.forEach((item) => {
        item.nm_use = item.nm_use / 10000;
        item.nm_hsl = item.nm_hsl / 10000;
      });
      this.lastTableData = data[0];
      console.log(data, params, "去年");
    },
  },
};
</script>

<style lang="less" scoped>
.title {
  font-size: 24px;
}
.nowDate {
  width: 100%;
  height: 97%;
  display: flex;
  flex-direction: column;
  div {
    height: calc(50% - 12px);
    margin: 6px 0px;
    border: 1px solid rgb(155, 155, 155);
    .info {
      width: 100%;
      height: 20%;
      border: 0px;
      .zl {
        font-size: 2.2vh;
        font-weight: 700;
        color: #dc8956;
      }
      .num {
        display: inline-block;
        font-size: 2vh;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.85);
        margin-left: 50%;
      }
      span {
        font-size: 1vh;
        font-weight: 400;
        margin-left: 10px;
        color: rgba(0, 0, 0, 0.85);
      }
    }
    h2 {
      text-align: center;
    }
  }
}
</style>
