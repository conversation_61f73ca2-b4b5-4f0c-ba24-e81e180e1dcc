<template>
  <div class="box">
    <a-radio-group size="large" default-value="dm_use" button-style="solid">
      <a-radio-button value="dm_use" @click="getData('dm_use')">
        主要作物地膜使用量情况
      </a-radio-button>
      <!-- <a-radio-button value="bzmj" @click="getData('bzmj')">
        主要作物播种面积情况
      </a-radio-button>
      <a-radio-button value="fmmj" @click="getData('fmmj')">
        主要作物覆膜面积
      </a-radio-button> -->
      <a-radio-button value="bzmj" @click="getData('bz_mj')">
        主要作物播种面积情况
      </a-radio-button>
      <a-radio-button value="fmmj" @click="getData('dm_fmmj')">
        主要作物覆膜面积
      </a-radio-button>
    </a-radio-group>
    <div class="contain">
      <div class="titles">
        <span style="position: absolute; left: 12%; font-size: 20px">指标</span>
        <span style="position: absolute; right: 18%; font-size: 20px">{{
          value == "dm_use" ? "使用量(吨)" : "覆膜面积(亩)"
        }}</span>
      </div>
      <div class="chart">
        <ul>
          <li v-for="(item, index) in listData" :key="index">
            <div
              class="icon"
              :style="{ background: colors[index % colors.length] }"
            ></div>
            <span class="zw_mc">{{ item.zwmc }}</span>
            <div class="progress">
              <div
                :style="{
                  right: `${((1 - item.current / all) * 100).toFixed(2)}%`,
                  'background-color': colors[index % colors.length],
                }"
              >
                <div style="font-size: 8px; color: #fff; border-radius: 50%">
                  {{ ((item.current / all) * 100).toFixed(2) + "%" }}
                </div>
              </div>
            </div>
            <span class="use_mj">{{ item.current }}</span>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ZyzwEcharts",
  props: {
    filterObj: {
      type: Object,
    },
  },
  watch: {
    filterObj: {
      deep: true,
      immediate: true,
      handler() {
        this.getData(this.value);
      },
    },
  },
  data() {
    return {
      listData: [],
      value: "dm_use",
      all: "",
      colors: [
        "#449bd0",
        "#91cb63",
        "#e9ca70",
        "#d99666",
        "#d36e5d",
        "#c6a7e2",
        "#7eddb2",
        "#d5e071",
        "#e395bc",
      ],
    };
  },
  methods: {
    async getData(type) {
      this.value = type;
      let params = {
        returnGeometry: false,
        format: "json",
        filter: [
          "all",
          ["=", "date", this.filterObj.nowDate],
          ["=", "qymc", this.filterObj.address],
        ],
      };
      // let data = await this.$apis.feature.query(`film_zw`, params);
      // console.log(data, "data");
      let data1 = await this.$apis.feature.query(`film_gx`, params);
      this.setData(type, data1);
    },
    setData(type, data) {
      console.log(type, data);
      this.all = data?.[0]?.[type];
      this.listData = data.filter((item) => {
        return item.zwmc != "合计" && item[type] != 0;
      });
      console.log(this.listData, "listData");
      this.listData.forEach((item) => {
        this.$set(item, "current", item[type] || 0);
      });
    },
  },
  mounted() {
    this.getData("dm_use");
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";
.box {
  height: 100%;
  background-color: #fff;
  position: relative;
  overflow: scroll;
  .contain {
    padding: 12px;
    width: 100%;
    .titles {
      height: 40px;
      width: 100%;
      display: flex;
      justify-content: flex-start;

      font-size: 16px;
    }
    .chart {
      height: 100%;
      width: 100%;
      ul {
        li {
          list-style: none;
          padding: 10px;
          display: flex;
          align-items: center;
          .icon {
            height: 12px;
            width: 12px;
            border-radius: 10px;
          }
          .progress {
            position: absolute;
            left: 10%;
            right: 40%;
            height: 12px;
            background-color: rgb(216, 214, 214);
            border-radius: 10px;
            margin-left: 10%;
            div {
              position: absolute;
              top: 0;
              left: 0;
              bottom: 0;
              height: 100%;
              border-radius: 10px;
              div {
                position: absolute;
                left: 20px;
                top: 0;
              }
            }
          }
          .zw_mc {
            position: absolute;
            left: 12%;
          }
          .use_mj {
            position: absolute;
            right: 20%;
          }
        }
      }
    }
  }
}
</style>
