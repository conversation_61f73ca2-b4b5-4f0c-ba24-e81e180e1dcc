<template>
  <div class="use-echarts">
    <TitleBar class="title" :title="title"></TitleBar>
    <div class="chart">
      <Echart :options="option"></Echart>
    </div>
  </div>
</template>

<script>
import Echart from "@/components/Chart";
import BaseWidget from "@/BaseWidget";
import TitleBar from "@/app/Components/TitleBar/";
export default {
  name: "UseEcharts",

  mixins: [BaseWidget],

  components: {
    Echart,
    TitleBar,
  },

  props: {
    type: {
      type: String,
    },
    filterObj: {
      type: Object,
    },
  },

  watch: {
    filterObj: {
      deep: true,
      immediate: true,
      handler() {
        this.getData(this.type);
      },
    },
  },

  computed: {
    title() {
      if (this.type == "use") {
        return "农膜使用情况（万吨）";
      } else {
        return "农膜回收情况（万吨）";
      }
    },
  },

  data() {
    return {
      states: "use",
      option: {
        tooltip: {
          trigger: "item",
          confine: true,
        },
        grid: {
          top: "5%",
          left: "15%",
          right: "5%",
          bottom: "10%",
        },
        xAxis: {
          type: "category",
          position: 12,
          data: ["农膜使用量", "棚膜使用量", "地膜使用量"],
          axisLabel: {
            interval: 0,
            textStyle: {
              color: "#002159",
              fontSize: 18,
              fontFamily: "PingFangSC-Semibold, PingFang SC",
            },
          },
          axisTick: {
            show: true, // 不显示坐标轴刻度线
          },
          axisLine: {
            show: true, //显示坐标轴线
          },
        },
        yAxis: {
          type: "value",
          axisLabel: {
            formatter: "{value}万吨",
          },
        },
        series: [
          {
            type: "bar",
            barWidth: 40,
            label: {
              show: true,
              position: "top",
              formatter: function (params) {
                return params.value.toLocaleString("en-US", {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                });
              },
            },
            data: [
              {
                value: 0,
                itemStyle: {
                  color: "#ee6860",
                },
              },
              {
                value: 0,
                itemStyle: {
                  color: "#7ECF52",
                },
              },
              {
                value: 0,
                itemStyle: {
                  color: "#EDCC5F",
                },
              },
            ],
          },
        ],
      },
    };
  },
  methods: {
    async getData(type) {
      let params = {
        returnGeometry: false,
        format: "json",
        orderField: this.chartField,
        orderType: "DESC",
        filter: [
          "all",
          ["=", "date", this.filterObj.nowDate],
          ["=", "qymc", this.filterObj.address],
        ],
      };
      let queryData = {};
      // let data = await this.$apis.feature.query(
      //   `film_recover`,
      //   params
      // )
      let data = await this.$apis.feature.query(`film_gx`, params);
      queryData = data[0];
      console.log("queryData", queryData);

      if (type == "use") {
        this.states = "use";
        this.setUseEchartData(queryData);
      }
      if (type == "recovery") {
        this.states = "recovery";
        this.setRecoveryEchartData(queryData);
      }
    },

    setUseEchartData(value) {
      if (!value) return;
      this.option.xAxis.data = ["农膜使用量", "棚膜使用量", "地膜使用量"];
      this.option.series[0].data[0].value = value.nm_use / 10000;
      this.option.series[0].data[1].value = value.pm_use / 10000;
      this.option.series[0].data[2].value = value.dm_use / 10000;
    },

    setRecoveryEchartData(value) {
      if (!value) return;
      this.option.xAxis.data = ["农膜回收量", "棚膜回收量", "地膜回收量"];
      this.option.series[0].data[0].value = value.nm_hsl / 10000;
      this.option.series[0].data[1].value = value.pm_hsl / 10000;
      this.option.series[0].data[2].value = value.dm_hsl / 10000;
    },
    formatNumber(num) {
      return num.toLocaleString("en-US", {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      });
    },
  },
  mounted() {
    this.getData(this.type);
  },
};
</script>

<style lang="less" scoped>
.use-echarts {
  height: 100%;
  background-color: #fff;
  .title {
    font-size: 24px;
  }
  .title {
    font-size: 24px;
  }
  .chart {
    // padding: 12px;
    height: 90%;
    width: 100%;
  }
}
</style>
