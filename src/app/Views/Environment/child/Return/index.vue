<template>
  <div class="box">
    <TitleBar class="title" :title="'农膜回收方式'"></TitleBar>
    <div class="contain">
      <div class="table-item">
        <table class="table">
          <tr>
            <td style="color: #002159">地膜</td>
          </tr>
          <tr v-for="(item, index) in dmTableConfig" :key="index">
            <td :style="{ color: item.color }">{{ item.label }}</td>
            <td style="color: rgba(0, 0, 0, 0.68)">
              {{ item.value === null ? "暂无数据" : item.value
              }}<span class="unit">{{ item.unit }}</span>
            </td>
          </tr>
        </table>
      </div>
      <div class="table-item">
        <table class="table">
          <tr>
            <td style="color: #002159">棚膜</td>
          </tr>
          <tr v-for="(item, index) in pmTableConfig" :key="index">
            <td :style="{ color: item.color }">{{ item.label }}</td>
            <td style="color: rgba(0, 0, 0, 0.68)">
              {{ item.value === null ? "暂无数据" : item.value
              }}<span class="unit">{{ item.unit }}</span>
            </td>
          </tr>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
import TitleBar from "@/app/Components/TitleBar/";
export default {
  name: "Return",
  components: {
    TitleBar,
  },
  props: {
    filterObj: {
      type: Object,
    },
  },
  data() {
    return {
      type: ["PXZQDM", "PXZQDM", "CXZQDM", "FXZQDM"],
      dmTableConfig: [
        {
          label: "人工回收作业面积",
          value: 0,
          color: "#d99666",
          field: "rghs_mj",
          unit: "万亩",
        },
        {
          label: "机械回收作业面积",
          value: 0,
          color: "#d99666",
          field: "jxhs_mj",
          unit: "万亩",
        },
        {
          label: "人工+机械回收作业面积",
          value: 0,
          color: "#d99666",
          field: "rg_jxhs_mj",
          unit: "万亩",
        },
        {
          label: "不回收作业面积",
          value: 0,
          color: "#d99666",
          field: "nmbhs_mj",
          unit: "万亩",
        },
      ],
      pmTableConfig: [
        {
          label: "人工回收作业面积",
          value: 0,
          color: "#d99666",
          field: "rghs_mj",
          unit: "万亩",
        },
        {
          label: "机械回收作业面积",
          value: 0,
          color: "#d99666",
          field: "jxhs_mj",
          unit: "万亩",
        },
        {
          label: "人工+机械回收作业面积",
          value: 0,
          color: "#d99666",
          field: "rg_jxhs_mj",
          unit: "万亩",
        },
        {
          label: "不回收作业面积",
          value: 0,
          color: "#d99666",
          field: "nmbhs_mj",
          unit: "万亩",
        },
      ],
    };
  },
  watch: {
    filterObj: {
      deep: true,
      // immediate: true,
      handler() {
        this.getData();
      },
    },
  },
  methods: {
    formatNumber(num) {
      return num.toLocaleString("en-US", {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      });
    },
    async getData() {
      const fetchData = async (filmType) => {
        let params = {
          aggregates: [
            ["rghs_mj", "sum"],
            ["jxhs_mj", "sum"],
            ["rg_jxhs_mj", "sum"],
            ["nmbhs_mj", "sum"],
          ],
          filter: [
            "all",
            ["=", "date", this.filterObj.nowDate],
            [
              "=",
              this.type[this.filterObj.numLevel],
              String(this.filterObj.code),
            ],
            ["=", "dwlx", filmType],
          ],
        };
        return await this.$apis.feature.aggregate(`film_zzh_point`, params);
      };

      try {
        // 获取地膜数据
        let res1 = await fetchData("地膜");
        this.dmTableConfig.forEach((item) => {
          if (res1[0][item.field + "_sum"] == null) {
            item.value = null;
          } else {
            item.value = res1[0][item.field + "_sum"] / 10000;
            item.value = this.formatNumber(item.value);
          }
        });

        // 获取棚膜数据
        let res2 = await fetchData("棚膜");
        this.pmTableConfig.forEach((item) => {
          if (res2[0][item.field + "_sum"] == null) {
            item.value = null;
          } else {
            item.value = res2[0][item.field + "_sum"] / 10000;
            item.value = this.formatNumber(item.value);
          }
        });
      } catch (error) {
        console.error("Error fetching data:", error);
        // 处理错误情况
      }
    },
  },
  async mounted() {
    await this.getData();
  },
};
</script>

<style lang="less" scoped>
.box {
  height: 100%;
  background-color: #fff;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;

  .title {
    font-size: 24px;
  }
  .contain {
    margin-top: 20px;
    font-size: 20px;

    white-space: nowrap;

    display: flex;
    // flex左右排列
    justify-content: space-between;
    .table-item {
      flex: 1;
      table {
        tr {
          width: 50%;
          height: 40px;
          th {
            width: 50%;
            line-height: 40px;
          }
          td {
            width: 50%;
            text-align: center;
            font-weight: 700;
            span {
              display: inline-block;
              border-radius: 50%;
              height: 10px;
              width: 10px;
              margin-right: 20px;
            }
          }
        }
      }
    }
  }
}
.unit {
  font-size: 14px;
}
</style>
