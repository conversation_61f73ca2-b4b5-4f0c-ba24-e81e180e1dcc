<template>
  <div class="wrap">
    <div class="left">
      <div class="top">
        <DateSelect
          @change="getDate"
          :years="years"
          class="dropData"
        ></DateSelect>
        <Recovery :filterObj="filterObj" class="recovery"></Recovery>
      </div>

      <Return :filterObj="filterObj" class="return"></Return>
    </div>
    <div class="center">
      <UseEcharts
        :filterObj="filterObj"
        class="use-Echarts"
        :type="'use'"
      ></UseEcharts>
      <UseEcharts
        :filterObj="filterObj"
        class="use-Echarts"
        :type="'recovery'"
      ></UseEcharts>
    </div>
    <div class="right">
      <div class="adminSelect">
        <AdminSelect class="admin-select" @change="adminChange"></AdminSelect>
      </div>
      <Contrast
        :year="year"
        :lastYear="lastYear"
        :filterObj="filterObj"
        :address="address"
        class="contrast"
      ></Contrast>
    </div>
  </div>
</template>

<script>
import Recovery from "./child/Recovery";
import UseEcharts from "./child/UseEcharts";
// import ZyzwEcharts from "./child/ZyzwEcharts";
import Return from "./child/Return";
import DateSelect from "@/app/Components/DateSelect";
import AdminSelect from "@/app/Components/AdminSelect";
import Contrast from "./child/Contrast";
import { mapState } from "vuex";
export default {
  name: "Environment",
  components: {
    Recovery,
    UseEcharts,
    // ZyzwEcharts,
    Return,
    DateSelect,
    AdminSelect,
    Contrast,
  },
  data() {
    return {
      // years: ['2020', '2021'],
      level: ["province", "city", "county", "county"],
      year: "2020",
      lastYear: null,
      address: "广西",
      filterObj: {
        nowDate: "2020",
        lastDate: null,
        level: "city",
        address: "广西",
        code: 45,
        numLevel: 0,
      },
    };
  },
  computed: {
    // 从全局配置中获取年份配置
    ...mapState({
      years: (state) => state.config.year?.environment,
    }),
  },
  methods: {
    getDate(value) {
      console.log(value);
      // this.filterObj.nowDate = value.year
      // this.filterObj.lastDate = value.lastDate
      this.year = value.year;
      this.lastYear = value.lastYear;
      this.$set(this.filterObj, "nowDate", value.year);
      this.$set(this.filterObj, "lastDate", value.lastYear);
    },
    adminChange(e, i, name) {
      this.address = name;
      this.code = e;
      this.adminData = i;
      this.adminName = name;
      this.filterObj.level = this.level[[i.length]];
      this.filterObj.address = name;
      this.filterObj.code = e;
      this.filterObj.numLevel = i.length;
    },
  },
  mounted() {},
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";
.wrap {
  height: 100%;

  display: flex;
  background-color: rgb(233, 233, 233);
  padding: 12px;
  margin-left: 100px; /* 左边距 */

  .left {
    flex: 2;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-right: 12px;

    .top {
      height: 60%;
      position: relative;

      .recovery {
        padding: 24px;
        overflow-y: hidden;
      }

      .dropData {
        position: absolute;
        top: 15px;
        right: 10%;
        width: 120px;
        z-index: 99999 !important;
      }
    }

    .return {
      height: 39%;
      padding: 24px;
    }
  }

  .center {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-right: 12px; /* 右边距 */
    padding: 24px;
    background-color: #fff; /* 背景色 */

    .UseEcharts {
      flex: 1;
      padding: 12px;
    }
  }

  .right {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background-color: #fff; /* 背景色 */
    padding: 24px;
    .adminSelect {
      width: 100%;
      height: 6%;

      display: flex;
      align-items: center;

      .admin-select {
        width: 100%;
      }
    }

    .contrast {
      width: 100%;
      margin-top: 12px; /* 上边距 */
      height: 93%;
      background-color: rgb(255, 255, 255);
    }
  }
}
</style>
