<template>
  <div>
    <a-select
      :default-value="thisYear"
      style="width: 120px"
      @change="handleChange"
    >
      <template v-for="(item, index) in years">
        <a-select-option :key="index" :value="item">
          {{ item }}
        </a-select-option>
      </template>
    </a-select>
  </div>
</template>

<script>
export default {
  props:{
    years:{
      type:Array
    }
  },
  data() {
    return {
      thisYear:this.years[0]
    }
  },
  methods: {
    handleChange(e, i) {
      console.log(e,i)
      const lastYear = this.years[i.key - 1]
      this.$emit('change', { year: e, lastYear: lastYear })
    }
  }
}
</script>

<style>
</style>