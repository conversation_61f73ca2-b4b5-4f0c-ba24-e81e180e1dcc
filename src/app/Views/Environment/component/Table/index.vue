<template>
  <div class="main">
    <table>
      <thead>
        <th style="width: 1%"></th>
        <th
          v-for="(item, index) in config.tableColumns"
          class="th"
          :style="{ width: item.width, 'text-align': item.align }"
          :key="index"
        >
          {{ item.title }}
        </th>
      </thead>
      <tbody>
        <tr v-for="(item, index) in tableData" :key="index">
          <td class="icon">{{ index + 1 }}</td>
          <td class="td" v-for="td in config.tableColumns" :key="td.key">
            {{ td.decimals ? formatNumber(item[td.key]) : item[td.key] }}
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
export default {
  name: "Table",
  props: {
    config: {
      type: Object,
    },
    filterObj: {
      type: Object,
    },
  },
  watch: {
    filterObj: {
      deep: true,
      immediate: true,
      handler() {
        this.getTableData();
      },
    },
  },
  data() {
    return {
      tableData: [],
      level: ["province", "city", "county"],
      tbody: [],
    };
  },
  methods: {
    formatNumber(num) {
      return num.toLocaleString("en-US", {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      });
    },
    async getTableData() {
      let params = {
        returnGeometry: false,
        format: "json",
        filter: [
          "all",
          ["=", "date", this.filterObj.nowDate],
          [
            "=",
            "level",
            this.filterObj.level ? this.filterObj.level : "county",
          ],
          ["=", "zwzl", "合计"],
        ],
      };
      if (this.filterObj.level == "county") {
        params.filter.push(["=", "ccode", this.filterObj.code.slice(0, 4)]);
      }
      // let data = await this.$apis.feature.query(
      //   `film_recover`,
      //   params
      // )
      let data = await this.$apis.feature.query(`film_gx`, params);
      console.log(data, "data");
      data.forEach((item) => {
        item.nm_use = item.nm_use / 10000;
        item.nm_hsl = item.nm_hsl / 10000;
        item.dm_fmmj = item.dm_fmmj / 10000;
      });

      this.tableData = data;
    },
    getTbody() {
      let data = this.config.tableColumns;
      data.shift();
      this.tbody = data;
    },
  },
  mounted() {
    this.getTableData();
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";
.main {
  padding: 20px 0;
  font-family: Avenir, Helvetica, Arial, sans-serif;
  table {
    width: 100%;
    max-height: 95%;
    color: #002159;
    .th {
      font-size: 20px;
      font-weight: 700;
    }
    tbody {
      overflow: scroll;
      width: 100%;
      font-size: 20px;
      color: rgba(0, 0, 0, 0.68);

      tr {
        line-height: 200%;
      }
      .icon {
        display: inline-block;
        height: 30px;
        width: 30px;
        border-radius: 50%;
        background-color: skyblue;
        text-align: center;
        line-height: 30px;
        color: white;
      }
      .td {
        text-align: center;
        line-height: 200%;
        font-weight: 800;
      }
    }
  }
}
</style>
