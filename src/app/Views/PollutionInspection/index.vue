<template>
  <div class="box">
    <div class="nm">
      <div class="top">
        <TitleBar :title="'农膜使用及回收情况'"></TitleBar>
        <div class="details" @click="to('/environment')">查看详情</div>
      </div>
      <div class="contain">
        <div class="data">
          <span>上一年情况</span>
          <div class="use">
            <p>农膜使用总量:</p>
            <p class="sl">
              <span class="num">{{
                dataFormat(lastNmTableData ? lastNmTableData.nm_use : "")
              }}</span>
              <span class="unit">{{
                lastNmTableData
                  ? lastNmTableData.nm_use > 10000
                    ? "万吨"
                    : "吨"
                  : ""
              }}</span>
            </p>
          </div>
          <div class="hsl">
            <p>农膜回收量:</p>
            <p class="sl">
              <span class="num">{{
                dataFormat(lastNmTableData ? lastNmTableData.nm_hsl : "")
              }}</span>
              <span class="unit">{{
                lastNmTableData
                  ? lastNmTableData.nm_hsl > 10000
                    ? "万吨"
                    : "吨"
                  : ""
              }}</span>
            </p>
          </div>
          <div class="hsll">
            <p>农膜回收率:</p>
            <p class="sl">
              <span class="num">{{
                area(lastNmTableData ? lastNmTableData.nm_rate : "")
              }}</span>
              <span class="unit">%</span>
            </p>
          </div>
        </div>
        <div class="data">
          <span>{{ nowNmTableData.date }}年情况</span>
          <div class="use">
            <p>农膜使用总量:</p>
            <p class="sl">
              <span class="num">{{ dataFormat(nowNmTableData.nm_use) }}</span>
              <span class="unit">{{
                nowNmTableData.nm_use > 10000 ? "万吨" : "吨"
              }}</span>
            </p>
          </div>
          <div class="hsl">
            <p>农膜回收量:</p>
            <p class="sl">
              <span class="num">{{ dataFormat(nowNmTableData.nm_hsl) }}</span>
              <span class="unit">{{
                nowNmTableData.nm_hsl > 10000 ? "万吨" : "吨"
              }}</span>
            </p>
          </div>
          <div class="hsll">
            <p>农膜回收率:</p>
            <p class="sl">
              <span class="num">{{ area(nowNmTableData.nm_rate) }}</span>
              <span class="unit">%</span>
            </p>
          </div>
        </div>
      </div>
    </div>
    <div class="nm">
      <div class="top">
        <TitleBar :title="'秸秆利用情况'"></TitleBar>
        <div class="details" @click="to('/straw')">查看详情</div>
      </div>
      <div class="contain">
        <div class="data">
          <span>上一年情况</span>
          <div class="use">
            <p>秸秆可收集量:</p>
            <p class="sl">
              <span class="num">{{
                dataFormat(lastJgTableData.straw_collect)
              }}</span>
              <span class="unit">{{
                lastJgTableData
                  ? lastJgTableData.straw_collect > 10000
                    ? "万吨"
                    : "吨"
                  : ""
              }}</span>
            </p>
          </div>
          <div class="hsl">
            <p>秸秆利用量:</p>
            <p class="sl">
              <span class="num">{{
                dataFormat(lastJgTableData.straw_use_scale)
              }}</span>
              <span class="unit">{{
                lastJgTableData
                  ? lastJgTableData.straw_use_scale > 10000
                    ? "万吨"
                    : "吨"
                  : ""
              }}</span>
            </p>
          </div>
          <div class="hsll">
            <p>秸秆利用率:</p>
            <p class="sl">
              <span class="num">{{
                area(lastJgTableData.straw_use_rate)
              }}</span>
              <span class="unit">%</span>
            </p>
          </div>
        </div>
        <div class="promote">
          <div class="use-promote">
            <span class="num">{{
              dataFormat(
                nowJgTableData.straw_collect,
                lastJgTableData.straw_collect
              )
            }}</span>
            <span class="unit">{{
              nowJgTableData.straw_collect - lastJgTableData.straw_use_scale >
              10000
                ? "万吨"
                : "吨"
            }}</span>
            <a-icon
              type="arrow-up"
              v-if="
                nowJgTableData.straw_collect > lastJgTableData.straw_collect
              "
              style="font-size: 20px; color: green"
            />
            <a-icon
              type="arrow-down"
              v-else
              style="font-size: 20px; color: red"
            />
          </div>
          <div class="use-promote">
            <span class="num">{{
              dataFormat(
                nowJgTableData.straw_use_scale,
                lastJgTableData.straw_use_scale
              )
            }}</span>
            <span class="unit">{{
              nowJgTableData.straw_use_scale - lastJgTableData.straw_use_scale >
              10000
                ? "万吨"
                : "吨"
            }}</span>
            <a-icon
              type="arrow-up"
              v-if="
                nowJgTableData.straw_use_scale > lastJgTableData.straw_use_scale
              "
              style="font-size: 1.5vh; color: green"
            />
            <a-icon
              type="arrow-down"
              v-else
              style="font-size: 1.5vh; color: red"
            />
          </div>
          <div class="use-promote">
            <span class="num">{{ area(lastJgTableData.straw_use_rate) }}</span>
            <span class="unit">%</span>
          </div>
        </div>
        <div class="data">
          <span>2020年情况</span>
          <div class="use">
            <p>秸秆可收集量:</p>
            <p class="sl">
              <span class="num">{{
                dataFormat(nowJgTableData.straw_collect)
              }}</span>
              <span class="unit">{{
                nowJgTableData.straw_collect > 10000 ? "万吨" : "吨"
              }}</span>
            </p>
          </div>
          <div class="hsl">
            <p>秸秆利用量:</p>
            <p class="sl">
              <span class="num">{{
                dataFormat(nowJgTableData.straw_use_scale)
              }}</span>
              <span class="unit">{{
                nowJgTableData.straw_use_scale > 10000 ? "万吨" : "吨"
              }}</span>
            </p>
          </div>
          <div class="hsll">
            <p>秸秆利用率:</p>
            <p class="sl">
              <span class="num">{{ area(nowJgTableData.straw_use_rate) }}</span>
              <span class="unit">%</span>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import TitleBar from "../../Components/TitleBar";
export default {
  name: "index",

  components: {
    TitleBar,
  },

  data() {
    return {
      nowNmTableData: {},
      lastNmTableData: {},
      nowJgTableData: {},
      lastJgTableData: {},
      filterObj: {
        nowDate: 2020,
        name: "广西",
        level: "province",
        code: 45,
      },
    };
  },
  methods: {
    async getNmNowData() {
      let params = {
        returnGeometry: false,
        format: "json",
        filter: [
          "all",
          ["=", "date", this.filterObj.nowDate],
          ["=", "level", this.filterObj.level],
          ["=", "qydm", this.filterObj.code],
        ],
      };
      // let data = await this.$apis.feature.query(
      //   `film_recover`,
      //   params
      // )
      let data = await this.$apis.feature.query(`film_gx`, params);
      this.nowNmTableData = data[0];
    },
    async getNmLastData() {
      let params = {
        returnGeometry: false,
        format: "json",
        filter: [
          "all",
          ["=", "date", this.filterObj.nowDate - 1],
          ["=", "level", this.filterObj.level],
          ["=", "qydm", this.filterObj.code],
        ],
      };
      // let data = await this.$apis.feature.query(`film_recover`, params);
      let data = await this.$apis.feature.query(`film_gx`, params);
      this.lastNmTableData = data[0];
    },
    async getJgNowData() {
      let params = {
        format: "json",
        returnGeometry: false,
        filter: [
          "all",
          ["=", "qydm", this.filterObj.code],
          ["=", "date", this.filterObj.nowDate],
          ["=", "crop", "合计"],
        ],
      };
      let data = await this.$apis.feature.query("straw_data_gx", params);
      this.nowJgTableData = data[0];
    },
    async getJgLastData() {
      let params = {
        format: "json",
        returnGeometry: false,
        filter: [
          "all",
          ["=", "qydm", this.filterObj.code],
          ["=", "date", this.filterObj.nowDate - 1],
          ["=", "crop", "合计"],
        ],
      };
      let data = await this.$apis.feature.query("straw_data_gx", params);
      this.lastJgTableData = data[0];
    },
    dataFormat(a, b) {
      a = parseInt(a);
      b = parseInt(b);
      if (!a) return "暂无数据";
      if (!b) {
        return a > 10000 ? (a / 10000).toFixed(2) : a.toFixed(2);
      }
      if (a && b) {
        let difference = a - b;
        return difference > 10000
          ? (difference / 10000).toFixed(2)
          : difference.toFixed;
      }
    },
    area(value) {
      value = parseInt(value);
      if (!value) return "暂无数据";
      return (value * 100).toFixed(2);
    },
    to(path) {
      this.$router.push(path);
    },
  },
  async mounted() {
    await this.getNmNowData();
    await this.getNmLastData();
    await this.getJgNowData();
    await this.getJgLastData();
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";
.box {
  height: 100%;
  width: calc(100% - 100px);
  background-color: #e9e9e9;
  display: flex;
  justify-content: space-around;
  padding: 12px 0;
  margin-left: 100px;

  .nm {
    padding: 12px 20px;
    height: 100%;
    width: 49%;
    background-color: white;
    .top {
      width: 100%;
      display: flex;
      justify-content: space-between;
      margin-bottom: 12px;
    }
    .details {
      height: 40px;
      width: 100px;
      text-align: center;
      line-height: 40px;
      border-radius: 5px;
      border: 2px solid #3d8ae3;
      font-size: 18px;
      cursor: pointer;
      color: #3d8ae3;
      font-weight: 500;
    }
    .contain {
      height: calc(100% - 54px);
      width: 100%;
      padding: 12px;
      display: flex;
      justify-content: space-around;
      .data {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        align-items: center;
        height: 100%;
        width: 40%;
        .use {
          border: 1px solid #8d75dd;
          background-color: #f1eefb;
          .num {
            color: #8d75dd;
          }
        }
        .hsl {
          border: 1px solid #d99666;
          background-color: #faf1eb;
          .num {
            color: #d99666;
          }
        }
        .hsll {
          border: 1px solid #6fa4e3;
          background-color: #e5eefa;
          .num {
            color: #6fa4e3;
          }
        }
        span {
          font-size: 3vh;
          font-weight: 500;
        }
        div {
          height: 25%;
          width: 100%;
          padding: 12px;
          p {
            font-size: 2.5vh;
          }
          .sl {
            float: right;
          }
          .num {
            font-size: 2.6vh;
          }
          .unit {
            margin-left: 0.5vw;
            font-size: 1.4vh;
          }
        }
      }
      .promote {
        height: 85%;
        width: 20%;
        text-align: center;
        margin-top: 11%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .use-promote {
          width: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          height: 29%;
          .num {
            font-size: 2.5vh;
          }
          .unit {
            font-size: 1.5vh;
            transform: translateY(0.5vh);
          }
        }
      }
    }
  }
}
</style>
