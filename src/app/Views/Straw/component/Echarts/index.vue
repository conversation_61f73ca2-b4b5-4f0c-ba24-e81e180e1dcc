<template>
    <div class="jimu-widget jimu-tool-widget">
        <Chart :options="option"></Chart>
    </div>
</template>
<script>
import Chart from '@/components/Chart/index.vue'
import config from '../../config/echarts.config'
export default {
    name:'Echarts',
    components: {Chart},
    data() {
      return {
          config,
      }
   },
   activated() {
   },
 watch: {
},
created(){
},
mounted(){
},
methods:{
}


}
</script>
<style>
</style>
