<template>
  <div class="wrap">
    <!-- <div class="title jimu-widget-title-ui">
      {{ adminName
      }}{{
        title === "离田后五化利用及占比"
          ? typeName + "利用量 (吨)"
          : title + "(吨)"
      }}
    </div> -->
    <div class="title jimu-widget-title-ui" v-if="dun.includes(title)">
      {{ adminName }}{{ title === "离田后五化利用及占比" ? typeName + "利用量 (万吨)" : title + "(万吨)" }}
    </div>
    <div class="title jimu-widget-title-ui" v-if="mj.includes(title)">{{ adminName }}{{ title + "(万亩)" }}</div>
    <div class="title jimu-widget-title-ui" v-if="lyl.includes(title)">{{ adminName }}{{ title + "(%)" }}</div>
    <div class="title jimu-widget-title-ui" v-if="zs.includes(title)">{{ adminName }}{{ title }}</div>
    <a-radio-group class="btn-list" v-model="index" button-style="solid" @change="getData">
      <a-radio-button value="city"> 市 </a-radio-button>
      <a-radio-button value="county"> 区/县 </a-radio-button>
    </a-radio-group>
    <div class="chart-wrap">
      <Chart class="Chart" :options="option"></Chart>
    </div>
  </div>
</template>

<script>
import Chart from "@/components/Chart/index.vue";
import BaseWidget from "@/BaseWidget";
const dun = [
  "秸秆产生量",
  "秸秆可收集量",
  "粮食产量",
  "五化利用量",
  "离田后五化利用及占比",
  "秸秆离田利用量",
  "市场规模化主体利用量",
  "农户分散离田利用量",
  "秸秆直接还田量",
];
const mj = ["播种面积"];
const lyl = ["秸秆综合利用率"];
const zs = ["综合利用指数", "产业化利用指数"];

export default {
  name: "BarChartList",
  components: { Chart },
  mixins: [BaseWidget],
  data() {
    return {
      dun: dun,
      mj: mj,
      lyl: lyl,
      zs: zs,
      index: "city",
      admin: [],
      option: {
        textStyle: {
          color: "#000",
        },
        legend: {},
        tooltip: {
          trigger: "axis",
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "30",
          top: "5%",
          containLabel: true,
        },
        xAxis: {
          axisLabel: {
            interval: 0,
            show: true,
          },
          type: "category",
          data: [],
        },
        yAxis: {
          type: "value",
        },

        series: [
          {
            type: "bar",
            data: [],
            itemStyle: {
              color: "#2a45ad",
            },
            label: {
              show: true,
              position: "top",
              formatter: (res) => {
                return res.value.toFixed(2);
              },
            },
            barMaxWidth: 40,
            barGap: "40%",
          },
        ],
        dataZoom: {
          show: true,
          type: "slider",
          xAxisIndex: [0],
          start: 0,
          height: 25,
          bottom: 0,

          handleSize: 0,
        },
      },
      regionConfig: {
        1: {
          level: "province",
        },
        2: {
          level: "city",
        },
        3: {
          level: "county",
        },
      },
      dataRegionConfig: {
        province: {
          name: "pname",
          code: "pcode",
        },
        city: {
          name: "cname",
          code: "ccode",
        },
        county: {
          name: "qymc",
          code: "qydm",
        },
      },
      crop: "全部",
      filter: [],
    };
  },
  props: {
    field: {
      type: String,
    },
    adminData: {
      type: Array,
    },
    year: {
      type: String,
    },
    code: {
      type: String,
    },
    title: {
      type: String,
    },
    adminName: {
      type: String,
    },
    typeName: {
      type: String,
    },
  },
  created() {},
  mounted() {
    this.getData();
    console.log(this.typeName, "ttt");
  },
  watch: {
    year() {
      this.getData();
    },
    code() {
      if (this.adminData.length == 1) {
        this.index = "city";
      } else if (this.adminData.length >= 2) {
        this.index = "county";
      } else {
        this.index = "city";
      }
      this.getData();
    },
    field() {
      this.getData();
    },
    option() {
      console.log("柱状图配置数据", this.option);
    },
  },
  methods: {
    getFilter() {
      let region = this.regionConfig[this.adminData.length].level;
      const codeKey = this.dataRegionConfig[region].code;
      return ["all", ["=", "level", this.index], ["=", codeKey, this.code], ["=", "crop", "合计"], ["=", "date", this.year]];
    },
    async getData() {
      let param = {
        format: "json",
        returnGeometry: false,
        orderField: this.field,
        orderType: "DESC",
        limit: 9999,
      };

      let filter = this.getFilter();
      param.filter = filter;
      let url = "straw_data_gx";
      let data = await this.$apis.feature.query(url, param);
      this.setChartData(data);
    },
    setChartData(data) {
      let nameList = data.map((item) => {
        return item[this.dataRegionConfig[this.index].name];
      });
      this.$set(this.option.xAxis, "data", nameList);
      this.$set(
        this.option.series[0],
        "data",
        data.map((item) => {
          let value = item[this.field];
          if (dun.includes(this.title) || mj.includes(this.title)) {
            value = this.$utils.number.divisor(value, 10000, 2);
          }
          return value;
        })
      );
      if (data.length > 20) {
        this.$set(this.option.dataZoom, "end", (20 / data.length) * 100);
      } else {
        this.$set(this.option.dataZoom, "end", 100);
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";
/deep/.ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
  background: #4065e0;
  border-color: #4065e0;
}
.wrap {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.chart-wrap {
  flex: 1;
  width: 100%;
}
.btn-list {
  position: absolute;
  right: 18px;
  top: 18px;
  z-index: 10000;
}
</style>
