<template>
  <div class="wrap">
    <div class="box">
      <div class="back" v-if="moreKey" @click="backClick">
        <a-icon type="left" />
      </div>
      <div class="title">{{ year }}年度情况</div>
      <div class="box-content">
        <template v-for="(item, index) in thisList">
          <div class="cont-wrap" :key="index">
            <div class="cont">
              <div
                @click="getMore(item)"
                :class="['label', item.detail ? 'more' : '']"
              >
                {{ item.name }}
              </div>
              <div class="content">
                <span class="value">{{ convert(item.value) }}</span>
                <span class="unit">{{ item.unit }}</span>
              </div>

              <div
                v-if="lastYear && item.value - lastList[index].value !== 0"
                class="contrast"
              >
                <span
                  :style="{
                    color: colorConvert(item.value - lastList[index].value),
                  }"
                  ><a-icon
                    :type="
                      item.value - lastList[index].value > 0
                        ? 'arrow-up'
                        : 'arrow-down'
                    "
                  />{{ convert(item.value - lastList[index].value)
                  }}{{ item.unit }}</span
                >
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
    <div class="box">
      <div class="title">上一年度情况</div>
      <div class="box-content">
        <template v-for="(item, index) in lastList">
          <div class="cont-wrap" :key="index">
            <div class="cont">
              <div class="label">{{ item.name }}</div>
              <div class="content">
                <span class="value">{{ convert(item.value) }}</span>
                <span class="unit">{{ item.unit }}</span>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import param from "./config/param.config";
export default {
  data() {
    return { param, thisList: [], lastList: [], moreKey: null };
  },
  computed: {
    list() {
      return this.param[this.moreKey ? this.moreKey : this.paramKey];
    },
  },
  props: {
    year: {
      type: String,
    },
    lastYear: {
      type: String,
    },
    code: {
      type: String,
    },
    paramKey: {
      type: String,
    },
  },
  mounted() {
    this.getData();
  },
  watch: {
    year() {
      this.getData();
    },
    code() {
      this.getData();
    },
    paramKey() {
      this.getData();
    },
  },
  methods: {
    colorConvert(e) {
      if (e == 0) return "yellow";
      if (e > 0) return "red";
      if (e < 0) return "green";
    },
    convert(data) {
      if (data == "暂无数据") return data;
      if (data == "0") return "0";
      if (Number(data) > 10000)
        return Number(data) > 10000
          ? (Number(data) / 10000).toFixed(2) + "万"
          : Number(data).toFixed(2);
      if (Number(data) < -10000)
        return Number(data) < -10000
          ? (Number(data) / 10000).toFixed(2) + "万"
          : Number(data)?.toFixed(2);
      return Number(data)?.toFixed(2);
    },
    async getData() {
      const params = {
        returnGeometry: false,
        format: "json",
        filter: [
          "all",
          ["=", "qydm", this.code],
          ["=", "date", this.year],
          ["=", "crop", "合计"],
        ],
      };
      let data = await this.$apis.feature.query("straw_data_gx", params);
      this.thisList = this.$utils.object.deepCloneObj(this.list).map((item) => {
        return Object.assign(item, { value: data[0][item.key] });
      });
      console.log(this.list, this.thisList, "thisList");
      if (this.lastYear) {
        const lastParams = {
          returnGeometry: false,
          format: "json",
          filter: [
            "all",
            ["=", "qydm", this.code],
            ["=", "date", this.lastYear],
            ["=", "crop", "合计"],
          ],
        };
        let lastData = await this.$apis.feature.query(
          "straw_data_gx",
          lastParams
        );
        this.lastList = this.$utils.object
          .deepCloneObj(this.list)
          .map((item) => {
            return Object.assign(item, { value: lastData[0][item.key] });
          });
      } else {
        this.lastList = this.$utils.object
          .deepCloneObj(this.list)
          .map((item) => {
            return Object.assign(item, { value: "暂无数据" });
          });
      }
    },
    getMore(item) {
      this.moreKey = item.name;
      this.getData();
    },
    backClick() {
      this.moreKey = null;
      this.getData();
    },
  },
};
</script>

<style lang="less" scoped>
.wrap {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  justify-content: space-between;

  .box {
    height: calc(50% - 6px);
    border: 2px solid rgb(226, 226, 226);
    padding: 6px;
    display: flex;
    flex-direction: column;
    align-items: center;
    .box-content {
      width: 100%;
      height: 100%;
      display: flex;
      flex: 1;
      flex-direction: column;
      justify-content: space-around;
      position: relative;
    }
    .back {
      position: absolute;
      cursor: pointer;
      z-index: 9999;
      top: 20px;
      left: 20px;
    }
    .cont {
      width: 100%;
      display: flex;
      padding: 0 20px;
      justify-content: space-between;
      position: relative;

      .content {
      }
      .more {
        text-decoration: underline;
        cursor: pointer;
      }
    }
    .title {
      font-size: 1.4vh;
      font-weight: 700;
      text-align: center;
    }
    .label {
      font-size: 1.8vh;
      font-weight: 700;
      color: #dc8956;
    }
    .value {
      font-size: 1.4vh;
      font-weight: 600;
      color: #2883e5;
    }
    .unit {
      font-size: 1.4vh;
      font-weight: 600;
    }
    .contrast {
      position: absolute;
      left: 30px;
      top: 2.2vh;
      font-size: 1.4vh;
      margin-left: 20%;
    }
  }
}
</style>
