<template>
  <div class="table-wrap">
    <!-- <a-table :thisTab='thisTab' :columns="config.columns[thisTab]" :data-source="tabledata()" :scroll="{x: 100,y:'100%'}" :pagination="false" size="small" :customRow='OnClickRow'>
      <div class="slot-box" slot="rate" slot-scope="rate">
        <div :class="rate===1?'slot':'nhfsly'" :style="{width:rate*100+'%',backgroundColor: '#1f91c3',color:'#fff'}">
          <span class="spannhfsly">{{rate===0?'':(rate*100).toFixed(2)+'%'}}</span>
        </div>
        <div :class="rate===1?'slot':'scztly'" :style="{width:(1-rate)*100+'%',backgroundColor:'#f19324',color:'#fff'}">
          <span  class="spanscztly">{{rate===1?'':((1-rate)*100).toFixed(2)+'%'}}</span>
        </div>
      </div>
    </a-table> -->
    <table class="default-table">
      <tr class="head-tr">
        <template v-for="(item, index) in thisConfig">
          <th :key="index">
            {{ item.title }}
          </th>
        </template>
        <template v-if="this.thisTab === '离田后五化利用及占比'">
          <th>
            <a-select
              style="width: 100%"
              @change="handleChange"
              :defaultValue="firmArr[0].value"
            >
              <a-select-option
                v-for="(option, index) in firmArr"
                :key="index"
                :value="option.value"
              >
                {{ option.name }}
              </a-select-option>
            </a-select>
          </th>
        </template>
      </tr>
      <div class="content-wrap">
        <template v-for="(item, index) in data">
          <tr :key="index" @click="OnClickRow(item, index)">
            <template v-for="(columnItem, i) in thisConfig">
              <template v-if="columnItem.dataIndex == 'id'">
                <td :key="i" class="id-td">
                  <span class="id-dot">{{ index + 1 }}</span>
                </td>
              </template>
              <template v-else>
                <td :key="i">
                  <template v-if="columnItem.rate">
                    <div class="slot-box">
                      <div
                        :class="'slot'"
                        :style="{
                          width: getRate(item, columnItem.rate)[0] + '%',
                          backgroundColor: '#1f91c3',
                          color: '#fff',
                        }"
                      >
                        <span class="spannhfsly">{{
                          getRate(item, columnItem.rate)[0].toFixed(1) + "%"
                        }}</span>
                      </div>
                      <div
                        :class="'scztly'"
                        :style="{
                          width: getRate(item, columnItem.rate)[1] + '%',
                          backgroundColor: '#f19324',
                          color: '#fff',
                        }"
                      >
                        <span class="spanscztly">{{
                          getRate(item, columnItem.rate)[1].toFixed(1) + "%"
                        }}</span>
                      </div>
                    </div>
                  </template>
                  <template v-if="columnItem.customRender">
                    {{ columnItem.customRender(item[columnItem.dataIndex]) }}
                  </template>
                  <template v-else>
                    {{ item[columnItem.dataIndex] }}
                  </template>
                </td>
              </template>
            </template>
          </tr>
        </template>
      </div>
    </table>
  </div>
</template>

<script>
import config from "../../config/echarts.config";
export default {
  name: "ListRankWidget",
  props: {
    thisTab: { type: String },
    data: { type: Array, default: () => {} },
    mainHeight: {
      type: Number,
    },
  },
  data() {
    return {
      width: 0,
      config,
      isUnfold: false,
      firmArr: [
        {
          name: "肥料化(离田)",
          value: "lt_flhlyl",
          zbValue: "lt_flhlyl_rate",
        },
        {
          name: "饲料化",
          value: "slh_use",
          zbValue: "lt_slhlyl_rate",
        },
        {
          name: "燃料化",
          value: "rlh_use",
          zbValue: "lt_rlhlyl_rate",
        },
        {
          name: "基料化",
          value: "jlh_use",
          zbValue: "lt_jlhlyl_rate",
        },
        {
          name: "原料化",
          value: "ylh_use",
          zbValue: "lt_ylhlyl_rate",
        },
      ],
    };
  },
  computed: {
    thisConfig() {
      return this.config.columns[this.thisTab];
    },
  },
  watch: {
    thisTab() {
      setTimeout(() => {
        this.width = this.$refs.table.offsetWidth;
      }, 300);
    },
  },
  mounted() {
    this.width = this.$refs.table?.offsetWidth;
    console.log("配置文件", this.thisConfig);
  },
  methods: {
    handleChange(e) {
      const deepFirmArr = JSON.parse(JSON.stringify(this.firmArr));
      deepFirmArr.forEach((item) => {
        if (item.value == e) {
          this.$emit("changeFirm", item);
        }
      });
    },
    getRate(item, rate) {
      let number1 = Number(item[rate[0]]);
      let number2 = Number(item[rate[1]]);
      let sum = number1 + number2;
      console.log(number1, number2, [
        (number1 / sum) * 100,
        (number2 / sum) * 100,
      ]);
      return [(number1 / sum) * 100, (number2 / sum) * 100];
    },
    unfoldClick() {
      this.isUnfold = !this.isUnfold;
    },
    // tabledata() {
    //   if (this.thisTab === '秸秆离田利用量') {
    //     this.data.forEach(item => {
    //       if (parseFloat(item.nhfsly_scale) > 0) {
    //         item.rate = parseFloat(
    //           parseFloat(item.nhfsly_scale) /
    //             (parseFloat(item.nhfsly_scale) + parseFloat(item.scztly_scale))
    //         )
    //       }
    //       if (
    //         parseFloat(item.nhfsly_scale) === 0 &&
    //         parseFloat(item.scztly_scale) === 0
    //       ) {
    //         item.rate = parseFloat(0.5)
    //       }
    //     })
    //   }
    //   return this.data
    // },
    OnClickRow(record, index) {
      this.$emit("OnClickRow", record, index);
    },
  },
};
</script>
<style lang="less" scoped>
.slot-box {
  width: 150px;
  // height: 30px;
  margin-left: -1vh;
  margin-right: -1vh;
  border-radius: 15px;
  overflow: hidden;
  .scztly {
    position: relative;
    margin-top: 3px;
    line-height: 20px;
    float: left;
    height: 20px;
    border-radius: 0 15px 15px 0;
    // padding: 10px 0;
    // margin-top: 12px;
    span {
      position: absolute;
      right: 7px;
      z-index: 9;
    }
  }
  .slot {
    position: relative;
    border-radius: 15px 0 0 15px;
    height: 20px;

    margin-top: 3px;
    line-height: 20px;
    float: left;
    // margin-top: 12px;
    // padding: 10px 0;
  }
  .nhfsly {
    position: relative;
    line-height: 15px;
    float: left;
    height: 15px;
    border-radius: 15px 0 0 15px;

    span {
      position: absolute;
      left: 7px;
      z-index: 9;
    }
  }
}
</style>

<style lang="less" scoped>
.table-wrap {
  position: relative;
  margin-top: 10px;
  .unfold {
    position: absolute;
    right: -110px;
    top: 10px;
    z-index: 99;
    background-color: rgb(230, 182, 111);
  }
}
// .default-table {
//   height: 100%;
//   font-size: 16px;
//   display: flex;
//   flex-direction: column;
//   .content-wrap {
//     overflow-y: scroll;
//     flex: 1;
//   }
//   .head-tr {
//     display: flex;
//     justify-content: space-between;
//   }
//   tr {
//     &:hover {
//       background-color: #cbdef5;
//     }
//     td,
//     th {
//       padding: 7px 10px;
//       white-space: nowrap;
//     }
//     .id-td {
//       .id-dot {
//         // padding: 10px;
//         display: block;
//         background-color: #6fa4e3;
//         border-radius: 50%;
//         height: 20px;
//         width: 20px;
//         text-align: center;
//         line-height: 20px;
//         color: #fff;
//       }
//     }
//   }
// }
.default-table {
  height: 100%;
  font-size: 1.3vh;
  display: table;

  flex-direction: column;
  .content-wrap {
    overflow-y: scroll;
    display: table-row-group; /* 表格内容 */
  }
  .head-tr {
    display: table-header-group; /* 表头 */

    // 第一个元素
    th:first-child {
      width: 20px;
    }
  }
  tr {
    display: table-row;
    &:hover {
      background-color: #cbdef5;
    }

    td,
    th {
      display: table-cell;
      padding: 7px 10px;
      white-space: nowrap;
      text-align: center;
      // width: 20%;
    }
    .id-td {
      .id-dot {
        // padding: 10px;
        display: block;
        background-color: #6fa4e3;
        border-radius: 50%;
        height: 20px;
        width: 20px;
        text-align: center;
        line-height: 20px;
        color: #fff;
      }
    }
  }
}
</style>
