<template>
  <div class="wrap">
    <a-row class="row-1" :gutter="[16, 16]">
      <a-col class="col-item" :span="6">
        <slot></slot>
        <div class="left box">
          <TitleBar :title="adminName + '秸秆利用概况'" />
          <Contrast
            :paramKey="'all'"
            :code="code"
            :lastYear="lastYear"
            :year="year"
          />
        </div>
      </a-col>
      <a-col class="col-item" :span="12">
        <div class="box middle">
          <TitleBar :title="adminName + '秸秆五化利用情况'" />
          <div class="middle-content">
            <div class="detail">
              <div
                v-for="(item, i) in pieData"
                :style="{ color: `${item.color}` }"
                :key="i"
              >
                <p>
                  {{ item.name }}利用量:{{
                    item.value > 10000
                      ? (item.value / 10000).toFixed(2) + "万"
                      : item.value
                  }}吨
                </p>
                <p>占比:{{ (item.percent * 100).toFixed(2) }}%</p>
              </div>
            </div>
            <div class="pie-box"><Chart :options="Pieoption"></Chart></div>
          </div>
        </div>
      </a-col>

      <a-col class="col-item" :span="6">
        <div class="right-top">
          <TitleBar :title="adminName + '秸秆还田离田量构成（万吨）'" />
          <Chart :options="marketoption"></Chart>
        </div>
        <div class="right-bottom">
          <TitleBar :title="adminName + '市场主体数量（个）'" />
          <Chart :options="companyoption"></Chart>
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import Chart from "@/components/Chart/index.vue";
import Contrast from "../../component/Contrast/Contrast.vue";
import TitleBar from "@/app/Components/TitleBar/";

export default {
  name: "All",
  components: { Chart, Contrast, TitleBar },
  data() {
    return {
      pieData: [],
      Pieoption: {
        tooltip: {
          trigger: "item",
          formatter: function (params) {
            let value =
              params.data.value > 10000
                ? (params.data.value / 10000).toFixed(2) + "万"
                : params.data.value;
            return (
              '<span style="color:' +
              params.data.color +
              ';">' +
              params.data.name +
              "利用量:<br>" +
              value +
              "吨<br>占比:" +
              params.percent +
              "%" +
              "</span>"
            );
          },
        },
        grid: {
          bottom: "20%",
          left: "2%",
          containLabel: true,
        },
        series: [
          {
            type: "pie",
            // radius: '90%',
            radius: ["40%", "80%"],
            label: {
              fontSize: 16,
              formatter: function (params) {
                console.log("数据", params.data);
                let value =
                  params.data.value > 10000
                    ? (params.data.value / 10000).toFixed(2) + "万"
                    : params.data.value;
                return (
                  params.data.name +
                  ":" +
                  value +
                  "吨\n占比:" +
                  params.percent +
                  "%"
                );
              },
            },
            data: [],
          },
        ],
      },
      strawoption: {
        xAxis: {
          type: "category",
          data: [],
        },
        yAxis: {
          type: "value",
        },
        grid: {
          top: "20%",
          left: "5%",
          right: "5%",
          bottom: "20%",
          containLabel: true,
        },
        series: [],
      },
      marketoption: {
        xAxis: {
          type: "category",
          data: [],
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
            onZero: false,
          },
          axisLabel: {
            interval: 0,
            textStyle: {
              color: "#002159",
              fontSize: 12,
              padding: 8,
              fontFamily: "PingFangSC-Semibold, PingFang SC",
            },
          },
        },
        yAxis: {
          type: "value",
        },
        grid: {
          top: "20%",
          left: "5%",
          right: "5%",
          bottom: "5%",
          containLabel: true,
        },
        legend: {
          data: ["市场化主体利用量", "农户分散利用量", "直接还田量"],
          top: "5%",
          textStyle: {
            color: "#002159",
            fontSize: 12,
            fontFamily: "PingFangSC-Semibold, PingFang SC",
          },
        },
        series: [
          {
            name: "市场化主体利用量",
            data: [],
            type: "bar",
            barWidth: 40,
            stack: "秸秆离田利用量",
            itemStyle: {
              color: "#5470c6",
            },

            label: {
              show: true,
              position: "inside",
              formatter: "{c}",
            },
          },
          {
            name: "农户分散利用量",
            data: [],
            type: "bar",
            barWidth: 40,
            stack: "秸秆离田利用量",
            itemStyle: {
              color: "#91cc75",
            },
            label: {
              show: true,
              position: "inside",
              formatter: "{c}",
            },
          },
          {
            name: "直接还田量",
            data: [],
            type: "bar",
            barWidth: 40,
            stack: "秸秆离田利用量",
            itemStyle: {
              color: "#ee6666",
            },
            label: {
              show: true,
              position: "top",
              formatter: "{c}",
            },
          },
        ],
      },
      companyoption: {
        xAxis: {
          type: "category",
          data: [],
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            interval: 0,
            textStyle: {
              color: "#002159",
              fontSize: 12,
              padding: 8,
              fontFamily: "PingFangSC-Semibold, PingFang SC",
            },
          },
        },
        yAxis: {
          type: "value",
        },
        grid: {
          top: "20%",
          left: "5%",
          right: "5%",
          bottom: "5%",
          containLabel: true,
        },
        series: [
          {
            name: "ss",
            data: [],
            type: "bar",
            barWidth: 40,
            label: {
              show: true,
              position: "top",
              formatter: "{c}",
            },
            itemStyle: {
              normal: {
                color: function (params) {
                  const color = [
                    "#5470c6",
                    "#91cc75",
                    "#fac858",
                    "#ee6666",
                    "#73c0de",
                    "#3ba272",
                    "#fc8452",
                    "#9a60b4",
                    "#ea7ccc",
                  ];
                  return color[params.dataIndex % color.length];
                },
              },
            },
          },
        ],
      },
    };
  },
  props: {
    year: {
      type: String,
    },
    lastYear: {
      type: String,
    },
    code: {
      type: String,
    },
    adminName: {
      type: String,
    },
  },
  watch: {
    year() {
      this.getData();
      this.getCompanyData();
    },
    code() {
      this.getData();
      this.getCompanyData();
    },
  },
  methods: {
    async getCompanyData() {
      let param = {
        format: "json",
        returnGeometry: false,
        orderField: this.chartField,
        orderType: "DESC",
        filter: ["all", ["=", "qydm", this.code], ["=", "date", this.year]],
      };
      let data = await this.$apis.feature.query("straw_company_gx", param);
      this.setCompanyData(data[0]);
    },

    setCompanyData(data) {
      let list = [
        { data: [], key: "rlh_use_qy", name: "燃料化" },
        { data: [], key: "ylh_use_qy", name: "原料化" },
        { data: [], key: "slh_use_qy", name: "饲料化" },
        { data: [], key: "jlh_use_qy", name: "基料化" },
        { data: [], key: "flh_use_qy", name: "肥料化" },
      ];
      let datalist = [];
      let namelist = [];
      list.forEach((item) => {
        datalist.push(parseFloat(data[item.key]));
        namelist.push(item.name);
      });
      this.companyoption.series[0].data = datalist;
      this.companyoption.xAxis.data = namelist;
    },
    async getData() {
      let param = {
        format: "json",
        returnGeometry: false,
        filter: [
          "all",
          ["=", "qydm", this.code],
          ["=", "date", this.year],
          ["=", "crop", "合计"],
        ],
      };
      let url = "straw_data_gx";
      let data = await this.$apis.feature.query(url, param);
      this.getPie(data);
      this.getMarket(data);
    },
    getPie(data) {
      //饼图五化数据处理
      let total = 0;
      const color = [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc",
      ];
      let list = [
        { value: "", key: "flh_use", name: "肥料化", color: "", percent: 0 },
        { value: "", key: "slh_use", name: "饲料化", color: "", percent: 0 },
        { value: "", key: "rlh_use", name: "燃料化", color: "", percent: 0 },
        { value: "", key: "ylh_use", name: "原料化", color: "", percent: 0 },
        { value: "", key: "jlh_use", name: "基料化", color: "", percent: 0 },
      ];
      list.forEach((item, i) => {
        item.value = data[0][item.key];
        item.color = color[i % color.length];
        item.label = {};
        item.label.color = item.color;
        total += parseFloat(data[0][item.key]);
      });
      list.forEach((item) => {
        item.percent = parseFloat((item.value / total).toFixed(4));
        item.percent = item.percent ? item.percent : 0;
      });
      console.log("list", list);
      this.$set(this.Pieoption.series[0], "data", list);
      this.pieData = list;
    },
    getMarket(data) {
      console.log("data", data);

      const marketData = {
        市场化主体利用量: parseFloat(data[0]["scztly_scale"] / 10000).toFixed(
          2
        ),
        农户分散利用量: parseFloat(data[0]["nhfsly_scale"] / 10000).toFixed(2),
        直接还田量: parseFloat(data[0]["zjht_scale"] / 10000).toFixed(2),
      };

      this.marketoption.series[0].data = [marketData["市场化主体利用量"], null];
      this.marketoption.series[1].data = [marketData["农户分散利用量"], null];
      this.marketoption.series[2].data = [null, marketData["直接还田量"]];

      this.marketoption.xAxis.data = ["秸秆离田利用量", "直接还田量"];
    },
  },
  mounted() {},
  created() {
    this.getData();
    this.getCompanyData();
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";
.wrap {
  width: 100%;
  height: 100%;
  position: relative;
  .row-1 {
    height: 100%;
  }
  .col-item {
    height: 100%;
  }

  .box {
    background: #fff;
    padding: 12px;
    width: 100%;
    height: 100%;
  }
  .left {
    height: calc(100% - 50px);
    display: flex;
    flex-direction: column;
  }
  .middle {
    display: flex;
    flex-direction: column;
    .middle-content {
      display: flex;
      flex-direction: column;
      height: 100%;
      margin-top: 20px;
      .pie-box {
        width: 100%;
        flex: 1;
      }
      .detail {
        width: 100%;
        display: grid;
        grid-template-columns: 30% 30% 30%;
        column-gap: 3%;
        min-height: 240px;
        div {
          overflow: hidden;
          border: 2px dashed;
          width: 100%;
          padding: 20px;
          border-radius: 10px;
          margin-bottom: 20px;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          p {
            font-size: 18px;
            font-weight: bolder;
            line-height: 18px;
            margin-bottom: 20px;
            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
  .right-top {
    width: 100%;
    height: calc(50% - 6px);
    display: flex;
    flex-direction: column;
    background: #fff;
    padding: 12px;
  }
  .right-bottom {
    width: 100%;
    margin-top: 12px;
    background: #fff;
    padding: 12px;
    height: calc(50% - 6px);
    display: flex;
    flex-direction: column;
  }
  .bar-echarts {
    width: 23.5%;
    div {
      width: calc(100%-5%);
      height: 50%;
    }
  }
}
</style>
