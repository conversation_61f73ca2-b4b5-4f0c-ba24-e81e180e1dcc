<template>
  <div class="wrap">
    <!-- <div>{{ year }}年{{ lastYear }}{{ code }}</div> -->
    <a-row :gutter="[16, 16]">
      <a-col :span="18">
        <div class="box left">
          <div class="btn-wrap">
            <a-radio-group class="btn-tabs" v-model="thisTab" button-style="solid">
              <a-radio-button v-for="(item, index) in tabList" :key="index" :value="item.label" @click="radioClick(item)">
                {{ item.label }}
              </a-radio-button>
            </a-radio-group>
          </div>
          <div class="main" ref="main">
            <ListRankWidget
              class="main-table"
              :thisTab="thisTab"
              :mainHeight="mainHeight"
              :data="data"
              @OnClickRow="(e, i) => OnClickRow(e, i)"
              @changeFirm="changeFirm"
            ></ListRankWidget>
            <div class="main-chart">
              <Chart class="main-chart" :options="config.option[thisTab]"></Chart>
            </div>
          </div>
        </div>
      </a-col>
      <a-col :span="6">
        <div class="box right">
          <TitleBar class="right-title" :title="adminName + '利用情况对比'" />
          <template v-if="thisTab == '产业化利用指数'">
            <Contrast :paramKey="'产业化利用指数'" :code="code" :lastYear="lastYear" :year="year" />
            <Chart :options="companyoption"></Chart>
          </template>
          <template v-else>
            <Contrast :paramKey="'use'" :code="code" :lastYear="lastYear" :year="year" />
          </template>
        </div>
      </a-col>
    </a-row>

    <div class="box bottom">
      <BarChartList
        :title="thisTab"
        :field="chartField"
        :code="code"
        :year="year"
        :adminData="adminData"
        :adminName="adminName"
        :typeName="typeName"
      />
    </div>
  </div>
</template>

<script>
import Contrast from "../../component/Contrast";
import ListRankWidget from "../../component/ListRankWidget";
import config from "../../config/echarts.config";
import Chart from "@/components/Chart/index.vue";
import TitleBar from "@/app/Components/TitleBar/";
import BarChartList from "../../component/BarChartList";
export default {
  data() {
    return {
      config,
      thisTab: "秸秆综合利用率",
      chartField: "straw_use_rate",
      tabList: [
        { label: "秸秆综合利用率", key: "straw_use_rate" },
        { label: "五化利用量", key: "wlh_use_total" },
        { label: "综合利用指数", key: "zhly_rate" },
        { label: "产业化利用指数", key: "cyhly_rate" },
        { label: "离田后五化利用及占比", key: "ltly_scale" },
      ],
      typeName: "肥料化(离田)",
      data: [],
      mainHeight: "",

      companyoption: {
        xAxis: {
          type: "category",
          data: [],
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            interval: 0,
            textStyle: {
              color: "#002159",
              fontSize: 12,
              fontFamily: "PingFangSC-Semibold, PingFang SC",
            },
          },
        },
        yAxis: {
          type: "value",
        },
        grid: {
          top: "20%",
          left: "5%",
          right: "5%",
          bottom: "5%",
          containLabel: true,
        },
        series: [
          {
            name: "ss",
            data: [],
            type: "bar",
            barWidth: 40,
            label: {
              show: true,
              position: "top",
              formatter: "{c}",
            },
            itemStyle: {
              normal: {
                color: function (params) {
                  const color = ["#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de", "#3ba272", "#fc8452", "#9a60b4", "#ea7ccc"];
                  return color[params.dataIndex % color.length];
                },
              },
            },
          },
        ],
      },
    };
  },
  components: { Chart, Contrast, ListRankWidget, TitleBar, BarChartList },
  props: {
    year: {
      type: String,
    },
    lastYear: {
      type: String,
    },
    code: {
      type: String,
    },
    adminData: {
      type: Array,
    },
    adminName: {
      type: String,
    },
  },
  watch: {
    thisTab() {
      this.getOption();
    },
    year() {
      this.getData();
    },
    code() {
      this.getData();

      this.getCompanyData();
    },
  },
  methods: {
    changeFirm(e) {
      if (e.name === "肥料化") {
        this.config.columns.离田后五化利用及占比 = [
          {
            title: "",
            dataIndex: "id",
            key: "1",
            width: 70,
            customRender: (text, record, index) => `${index + 1}`,
            align: "center",
          },
          { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
          {
            title: "离田利用量(吨)",
            dataIndex: "ltly_scale",
            key: "3",
            align: "center",
            customRender: (text, record, index) => `${parseFloat(text).toFixed(2)}`,
          },
          {
            title: e.name + "(离田)利用量(吨)",
            dataIndex: e.value,
            key: "4",
            align: "center",
            customRender: (text, record, index) => `${parseFloat(text).toFixed(2)}`,
          },
          {
            title: "占离田利用量比例(%)",
            dataIndex: e.zbValue,
            key: "4",
            align: "center",
            customRender: (text, record, index) => `${parseFloat(text).toFixed(2)}`,
          },
        ];
      } else {
        this.config.columns.离田后五化利用及占比 = [
          {
            title: "",
            dataIndex: "id",
            key: "1",
            width: 70,
            customRender: (text, record, index) => `${index + 1}`,
            align: "center",
          },
          { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
          {
            title: "离田利用量(吨)",
            dataIndex: "ltly_scale",
            key: "3",
            align: "center",
            customRender: (text, record, index) => `${parseFloat(text).toFixed(2)}`,
          },
          {
            title: e.name + "利用量(吨)",
            dataIndex: e.value,
            key: "4",
            align: "center",
            customRender: (text, record, index) => `${parseFloat(text).toFixed(2)}`,
          },
          {
            title: "占离田利用量比例(%)",
            dataIndex: e.zbValue,
            key: "4",
            align: "center",
            customRender: (text, record, index) => `${parseFloat(text).toFixed(2)}`,
          },
        ];
      }

      this.chartField = e.value;
      this.typeName = e.name;
    },
    async getCompanyData() {
      let param = {
        format: "json",
        returnGeometry: false,
        filter: ["all", ["=", "qydm", this.code], ["=", "date", this.year]],
      };
      let data = await this.$apis.feature.query("straw_company_gx", param);
      this.setCompanyData(data[0]);
    },
    setCompanyData(data) {
      let list = [
        { data: [], key: "rlh_use_qy", name: "燃料化" },
        { data: [], key: "ylh_use_qy", name: "原料化" },
        { data: [], key: "slh_use_qy", name: "饲料化" },
        { data: [], key: "jlh_use_qy", name: "基料化" },
        { data: [], key: "flh_use_qy", name: "肥料化" },
      ];
      let datalist = [];
      let namelist = [];
      list.forEach((item) => {
        datalist.push(parseFloat(data[item.key]));
        namelist.push(item.name);
      });

      this.$set(this.companyoption.series[0], "data", datalist);
      this.$set(this.companyoption.xAxis, "data", namelist);
    },
    OnClickRow(e, i) {
      this.getOption();
      if (e.crop == "合计") return;
      this.getwlhlyldata(e);
      if (this.thisTab === "离田后五化利用及占比") {
        this.config.option["离田后五化利用及占比"].title.text = "广西离田后五化利用及占比情况-" + e.crop;
      }
      if (this.thisTab === "五化利用量") {
        return;
      }
      this.$set(this.config.option[this.thisTab].series[0].data, i, {
        value: this.config.option[this.thisTab].series[0].data[i],
        itemStyle: {
          color: "#58d9f9",
        },
      });
    },
    async getData() {
      let param = {
        format: "json",
        returnGeometry: false,
        orderField: this.chartField,
        orderType: "DESC",
        filter: [
          "all",
          ["=", "qydm", this.code],
          ["=", "date", this.year],
          // ["!=", "crop", "合计"],
        ],
      };
      let url = "straw_data_gx";
      let data = await this.$apis.feature.query(url, param);
      data = data.filter((item) => item.crop !== "合计").concat(data.filter((item) => item.crop === "合计"));
      this.data = data;
      this.getOption();
    },
    getOption() {
      let data = this.data;
      let datalist = {
        秸秆综合利用率: [],
        综合利用指数: [],
        产业化利用指数: [],
      };
      let namelist = [];
      if (this.thisTab === "五化利用量" || this.thisTab === "离田后五化利用及占比") {
        this.getwlhlyldata(data[0]);
        return;
      }

      data.forEach((item) => {
        if (item.crop == "合计") return;
        datalist["秸秆综合利用率"].push(parseFloat(item.straw_use_rate));
        datalist["综合利用指数"].push(parseFloat(item.zhly_rate));
        datalist["产业化利用指数"].push(parseFloat(item.cyhly_rate));
        namelist.push(item.crop);
      });

      if (this.thisTab === "产业化利用指数") {
        this.config.option[this.thisTab].xAxis.data = namelist;
      } else {
        this.config.option[this.thisTab].yAxis.data = namelist;
      }
      this.config.option[this.thisTab].series[0].data = datalist[this.thisTab];
      this.config.option[this.thisTab].title.text = this.adminName + "各类作物" + this.thisTab + "排名";
    },
    getwlhlyldata(data) {
      let rlh_use = this.$utils.number.divisor(data.rlh_use, 10000, 2);
      let flh_use = this.$utils.number.divisor(data.flh_use, 10000, 2);
      let ylh_use = this.$utils.number.divisor(data.ylh_use, 10000, 2);
      let jlh_use = this.$utils.number.divisor(data.jlh_use, 10000, 2);
      let slh_use = this.$utils.number.divisor(data.slh_use, 10000, 2);
      let datalist = [
        {
          value: rlh_use,
          name: "燃料化",
          label: { color: "" },
        },
        {
          value: flh_use,
          name: "肥料化",
          label: { color: "" },
        },
        {
          value: ylh_use,
          name: "原料化",
          label: { color: "" },
        },
        {
          value: jlh_use,
          name: "基料化",
          label: { color: "" },
        },
        {
          value: slh_use,
          name: "饲料化",
          label: { color: "" },
        },
      ];
      if (this.thisTab === "五化利用量") {
        this.config.option["五化利用量"].title.text = "广西" + data.crop + "五化利用量";
        this.config.option["五化利用量"].series[0].data = datalist;
      }
      if (this.thisTab === "离田后五化利用及占比") {
        this.config.option["离田后五化利用及占比"].title.text = "广西离田后五化利用及占比情况-合计";
        this.config.option["离田后五化利用及占比"].series[0].data = datalist;
      }
    },

    getHeight() {
      let a = this.$refs.main;
      if (!a) return;
      this.mainHeight = a.offsetHeight;
    },
    radioClick(param) {
      this.typeName = "肥料化(离田)";
      this.chartField = param.key;
      this.getData();
    },
  },
  created() {
    this.getData();
    this.getCompanyData();
  },
  mounted() {
    this.getHeight();
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";

/deep/.ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
  background: #4065e0;
  border-color: #4065e0;
}
.wrap {
  position: relative;
  width: 100%;
  height: 100%;

  .box {
    background: #fff;
    padding: 12px;
    overflow: scroll;
  }
  .left {
    left: 0.1vh;
    right: calc(24% - 6px);
    height: 55vh;
    display: flex;
    flex-direction: column;
    top: 0;
    .btn-wrap {
      height: 45px;

      .btn-tabs {
        .ant-radio-button-wrapper {
          padding: 0 30px;
        }
      }
    }

    .main {
      width: 100%;
      height: calc(100% - 45px);
      display: flex;
      .main-table {
        height: 100%;
        flex: 0;
      }
      .main-chart {
        width: 20%;
        height: 100%;
        flex: 1;
        .chart {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .right {
    right: 0.1vh;
    left: calc(76% + 18px);
    height: 55vh;
    top: 0;
    display: flex;
    flex-direction: column;
  }
  .bottom {
    position: absolute;
    bottom: 4px;
    top: calc(55vh + 24px);
    width: 100%;
  }
}
</style>
