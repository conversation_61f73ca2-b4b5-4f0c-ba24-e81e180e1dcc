<template>
  <div class="wrap">
    <a-row :gutter="[16, 16]">
      <a-col :span="18">
        <div class="box left">
          <div class="btn-wrap">
            <a-radio-group
              class="btn-tabs"
              v-model="thisTab"
              button-style="solid"
            >
              <a-radio-button
                v-for="(item, index) in tabList"
                :key="index"
                :value="item.label"
                @click="radioClick(item)"
              >
                {{ item.label }}
              </a-radio-button>
            </a-radio-group>
          </div>
          <div class="main">
            <div class="main-table-wrap">
              <ListRankWidget
                class="main-table"
                :thisTab="
                  this.thisTab === '秸秆离田利用量'
                    ? isshow
                      ? '秸秆离田利用量展开'
                      : thisTab
                    : thisTab
                "
                :data="data"
                @OnClickRow="(e, i) => OnClickRow(e, i)"
              ></ListRankWidget>
              <span
                class="jgltbtn"
                v-if="thisTab === '秸秆离田利用量'"
                @click="isshow = !isshow"
              >
                {{ isshow ? "关闭" : "展开对比" }}
              </span>
            </div>
            <Chart class="main-chart" :options="config.option[thisTab]"></Chart>
          </div>
        </div>
      </a-col>
      <a-col :span="6">
        <div class="box right">
          <TitleBar
            class="right-title"
            :title="adminName + '还田离田情况对比'"
          />
          <Contrast
            :paramKey="'return'"
            :code="code"
            :lastYear="lastYear"
            :year="year"
          />
        </div>
      </a-col>
    </a-row>
    <div class="box bottom">
      <BarChartList
        :title="thisTab"
        :field="chartField"
        :code="code"
        :year="year"
        :adminData="adminData"
        :adminName="adminName"
      />
    </div>
  </div>
</template>

<script>
import Contrast from "../../component/Contrast";
import ListRankWidget from "../../component/ListRankWidget";
import config from "../../config/echarts.config";
import Chart from "@/components/Chart/index.vue";
import TitleBar from "@/app/Components/TitleBar/";
import BarChartList from "../../component/BarChartList";
export default {
  name: "Return",
  data() {
    return {
      config,
      isshow: false,
      thisTab: "秸秆离田利用量",
      chartField: "ltly_scale",
      tabList: [
        { label: "秸秆离田利用量", key: "ltly_scale" },
        { label: "市场规模化主体利用量", key: "scztly_scale" },
        { label: "农户分散离田利用量", key: "nhfsly_scale" },
        { label: "秸秆直接还田量", key: "zjht_scale" },
      ],
      data: [],
      crop: "",
    };
  },
  components: { Chart, Contrast, ListRankWidget, TitleBar, BarChartList },
  props: {
    year: {
      type: String,
    },
    lastYear: {
      type: String,
    },
    code: {
      type: String,
    },
    adminData: {
      type: Array,
    },
    adminName: {
      type: String,
    },
  },
  watch: {
    isshow() {},
    thisTab() {
      this.getOption();
    },
    year() {
      this.getData();
    },
    code() {
      this.getData();
    },
  },
  methods: {
    OnClickRow(e, i) {
      this.crop = e.crop;
      this.getOption();
      if (e.crop == "合计") return;
      if (this.config.option[this.thisTab].series[0].type == "pie") return;
      this.$set(this.config.option[this.thisTab].series[0].data, i, {
        value:
          this.config.option[this.thisTab].series[0].data[i].value ||
          this.config.option[this.thisTab].series[0].data[i],
        itemStyle: {
          color: "#58d9f9",
        },
      });
    },
    async getData() {
      let param = {
        format: "json",
        returnGeometry: false,
        orderField: this.chartField,
        orderType: "DESC",
        filter: [
          "all",
          ["=", "qydm", this.code],
          ["=", "date", this.year],
          // ['!=', 'crop', '合计']
        ],
      };
      let url = "straw_data_gx";
      let data = await this.$apis.feature.query(url, param);
      data = data
        .filter((item) => item.crop !== "合计")
        .concat(data.filter((item) => item.crop === "合计"));
      this.data = data;
      this.getOption();
    },
    getOption() {
      let data = this.data;

      if (!this.crop) {
        this.crop = data[0].crop;
      }

      let datalist = {
        秸秆离田利用量: [],
        秸秆离田利用量展开: [],
        市场规模化主体利用量: [],
        农户分散离田利用量: [],
        秸秆直接还田量: [],
      };
      let namelist = [];

      data.forEach((item) => {
        if (item.crop == "合计") return;
        datalist["秸秆离田利用量"].push(
          parseFloat((item.ltly_scale / 10000).toFixed(2))
        );
        datalist["市场规模化主体利用量"].push(
          parseFloat((item.scztly_scale / 10000).toFixed(2))
        );
        datalist["农户分散离田利用量"].push(
          parseFloat((item.nhfsly_scale / 10000).toFixed(2))
        );
        // datalist["秸秆直接还田量"].push(parseFloat(item.zjht_scale));

        let zjht_scale = this.$utils.number.divisor(item.zjht_scale, 10000, 2);
        datalist["秸秆直接还田量"].push({
          value: parseFloat(item.zjht_rate),
          name: zjht_scale,
        });
        namelist.push(item.crop);
      });
      let jglilyList = [
        { name: "直接还田量", key: "zjht_rate" },
        { key: "nhfsly_scale", name: "分散利用量" },
        { name: "规模化利用量", key: "scztly_scale" },
      ];
      jglilyList.forEach((item) => {
        let dataItem = data.find((i) => {
          return i.crop == this.crop;
        });
        datalist["秸秆离田利用量"].push(dataItem[item.key]);
        datalist["秸秆离田利用量展开"].push(dataItem[item.key]);
      });
      if (this.thisTab == "秸秆离田利用量") {
        let selectData = data.find((item) => item.crop == this.crop);
        let nhfsly_scale = this.$utils.number.divisor(
          selectData.nhfsly_scale,
          10000,
          2
        );
        let scztly_scale = this.$utils.number.divisor(
          selectData.scztly_scale,
          10000,
          2
        );

        let seriesList = [
          {
            value: nhfsly_scale,
            name: "农户分散离田利用量",
            label: { color: "" },
          },
          {
            value: scztly_scale,
            name: "市场化规模利用量",
            label: { color: "" },
          },
        ];
        this.config.option["秸秆离田利用量"].series[0].data = seriesList;
      } else {
        this.$set(this.config.option[this.thisTab].yAxis, "data", namelist);
        this.config.option[this.thisTab].series[0].data =
          datalist[this.thisTab];
        // this.config.option[this.thisTab].yAxis.data = namelist
      }
      let name = this.adminName + "各类作物" + this.thisTab + "排名";
      console.log("tab", this.thisTab);
      if (this.thisTab == "秸秆直接还田量") name = name.replace("排名", "");
      if (this.thisTab == "秸秆离田利用量") name = name.replace("排名", "占比");
      this.config.option[this.thisTab].title.text = name;
      // if (this.thisTab == '秸秆离田利用量') {
      //   this.OnClickRow(this.data[0], 0)
      // }
    },
    getHeight() {
      let a = this.$refs.main;
      if (!a) return;
      this.mainHeight = a.offsetHeight;
    },

    radioClick(param) {
      this.chartField = param.key;
      this.getData();
    },
  },
  created() {
    this.getData();
  },
  mounted() {
    this.getHeight();
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";

/deep/.ant-radio-group-solid
  .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
  background: #4065e0;
  border-color: #4065e0;
}
.wrap {
  position: relative;
  width: 100%;
  height: 100%;

  .box {
    // position: absolute;
    background: #fff;
    padding: 12px;
    overflow: scroll;
  }
  .left {
    left: 0.1vh;
    right: calc(24% - 6px);
    height: 55vh;
    display: flex;
    flex-direction: column;
    top: 0;
    .jgltbtn {
      background-color: #449ad0;
      color: #fff;
      // font-size: 17px;
      display: block;
      position: absolute;
      padding: 2px 15px;
      right: -100px;
      top: 0px;
      z-index: 99;
    }
    .btn-wrap {
      height: 45px;

      .btn-tabs {
        .ant-radio-button-wrapper {
          padding: 0 30px;
        }
      }
    }
    .main {
      width: 100%;
      height: calc(100% - 45px);
      display: flex;
      .main-table-wrap {
        height: 100%;
        position: relative;
      }
      .main-table {
        height: 100%;
      }
      .main-chart {
        width: 20%;
        height: 100%;
        flex: 1;
      }
    }
  }
  .right {
    // right: 0.1vh;
    height: 55vh;
    top: 0;
    display: flex;
    flex-direction: column;
  }
  .bottom {
    position: absolute;
    bottom: 4px;
    top: calc(55vh + 24px);
    width: 100%;
  }
}
</style>
