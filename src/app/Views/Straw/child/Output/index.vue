<template>
  <div class="wrap">
    <!-- <div>{{ year }}年{{ lastYear }}{{ code }}</div> -->
    <a-row :gutter="[16, 16]">
      <a-col :span="18"
        ><div class="box left">
          <div class="btn-wrap">
            <a-radio-group class="btn-tabs" v-model="thisTab" button-style="solid">
              <a-radio-button v-for="(item, index) in tabList" :key="index" :value="item.label" @click="radioClick(item)">
                {{ item.label }}
              </a-radio-button>
            </a-radio-group>
          </div>

          <div class="main" ref="main">
            <ListRankWidget
              class="main-table"
              :thisTab="thisTab"
              :mainHeight="mainHeight"
              :data="data"
              @OnClickRow="(e, i) => OnClickRow(e, i)"
            ></ListRankWidget>
            <Chart class="main-chart" :options="config.option[thisTab]"></Chart>
          </div></div
      ></a-col>
      <a-col :span="6">
        <div class="box right">
          <TitleBar class="right-title jimu-widget-title-ui" :title="adminName + '产生情况对比'" />
          <Contrast :paramKey="'output'" :code="code" :lastYear="lastYear" :year="year" /></div
      ></a-col>
    </a-row>
    <div class="box bottom">
      <BarChartList :title="thisTab" :field="chartField" :code="code" :year="year" :adminData="adminData" :adminName="adminName" />
    </div>
  </div>
</template>

<script>
import Contrast from "../../component/Contrast";
import ListRankWidget from "../../component/ListRankWidget";
import config from "../../config/echarts.config";
import Chart from "@/components/Chart/index.vue";
import TitleBar from "@/app/Components/TitleBar/";
import BarChartList from "../../component/BarChartList";
export default {
  data() {
    return {
      config: JSON.parse(JSON.stringify(config)),
      data: [],
      thisTab: "秸秆产生量",
      chartField: "straw_output",
      tabList: [
        { label: "秸秆产生量", key: "straw_output" },
        { label: "秸秆可收集量", key: "straw_collect" },
        { label: "播种面积", key: "crop_sown_area" },
        { label: "粮食产量", key: "crop_output" },
      ],
    };
  },
  components: { Chart, Contrast, ListRankWidget, TitleBar, BarChartList },
  props: {
    year: {
      type: String,
    },
    lastYear: {
      type: String,
    },
    code: {
      type: String,
    },
    adminData: {
      type: Array,
    },
    adminName: {
      type: String,
    },
  },
  watch: {
    thisTab: function () {
      this.getOption();
    },
    year() {
      this.getData();
    },
    code() {
      this.getData();
    },
  },
  methods: {
    OnClickRow(e, i) {
      this.getOption();
      // 点击合计不处理
      if (i == this.config.option[this.thisTab].series[0].data.length - 1) return;
      this.$set(this.config.option[this.thisTab].series[0].data, i, {
        value: this.config.option[this.thisTab].series[0].data[i].value || this.config.option[this.thisTab].series[0].data[i],
        itemStyle: {
          color: "#58d9f9",
        },
      });
    },
    async getData() {
      let param = {
        format: "json",
        returnGeometry: false,
        orderField: this.chartField,
        orderType: "DESC",
        filter: [
          "all",
          ["=", "qydm", this.code],
          ["=", "date", this.year],
          // ['!=', 'crop', '合计']
        ],
      };
      let url = "straw_data_gx";
      let data = await this.$apis.feature.query(url, param);
      data = data.filter((item) => item.crop !== "合计").concat(data.filter((item) => item.crop === "合计"));
      this.data = data;
      this.getOption();
    },
    getOption() {
      let data = this.data;
      let datalist = {
        秸秆产生量: [],
        秸秆可收集量: [],
        播种面积: [],
        粮食产量: [],
      };
      let namelist = [];
      data.forEach((item) => {
        // datalist['秸秆产生量'].push(parseFloat(item.straw_output_rate))
        // datalist['秸秆可收集量'].push(parseFloat(item.straw_collect_rate))
        // datalist['播种面积'].push(parseFloat(item.crop_sown_area))
        // datalist['粮食产量'].push(parseFloat(item.crop_output))
        if (item.crop == "合计") return;
        let straw_output = this.$utils.number.divisor(item.straw_output, 10000, 2);
        let straw_collect = this.$utils.number.divisor(item.straw_collect, 10000, 2);
        let crop_sown_area = this.$utils.number.divisor(item.crop_sown_area, 10000, 2);
        let crop_output = this.$utils.number.divisor(item.crop_output, 10000, 2);
        datalist["秸秆产生量"].push({
          value: parseFloat(item.straw_output_rate),
          name: straw_output,
        });
        datalist["秸秆可收集量"].push({
          value: parseFloat(item.straw_collect_rate),
          name: straw_collect,
        });
        datalist["播种面积"].push(parseFloat(crop_sown_area));
        datalist["粮食产量"].push(parseFloat(crop_output));
        namelist.push(item.crop);
      });
      if (this.thisTab == "播种面积") {
        this.config.option["播种面积"].tooltip.formatter = `{b} :{c}万亩`;
      } else if (this.thisTab == "粮食产量") {
        this.config.option["粮食产量"].tooltip.formatter = `{b} :{c}万吨`;
      }
      this.config.option[this.thisTab].yAxis.data = namelist;
      this.config.option[this.thisTab].series[0].data = datalist[this.thisTab];
      this.config.option[this.thisTab].title.text = this.adminName + "各类作物" + this.thisTab + "排名";
    },
    getHeight() {
      let a = this.$refs.main;
      if (!a) return;
      this.mainHeight = a.offsetHeight;
    },
    radioClick(param) {
      this.chartField = param.key;
      this.getData();
    },
  },
  created() {
    this.getData();
  },
  mounted() {
    this.getHeight();
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";
/deep/.ant-radio-button-wrapper {
  line-height: 35px;
}
/deep/.ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
  background: #4065e0;
  border-color: #4065e0;
}
.wrap {
  position: relative;
  width: 100%;
  height: 100%;

  .box {
    background: #fff;
    padding: 12px;
    overflow: scroll;
  }
  .left {
    height: 55vh;
    display: flex;
    flex-direction: column;
    .btn-wrap {
      height: 45px;
      .btn-tabs {
        .ant-radio-button-wrapper {
          padding: 0 30px;
        }
      }
    }
    .main {
      width: 100%;
      height: calc(100% - 50px);
      display: flex;
      .main-table {
        height: 100%;
      }
      .main-chart {
        width: 20%;
        height: 100%;
        flex: 1;
      }
    }
  }
  .right {
    height: 55vh;
    top: 0;
    display: flex;
    flex-direction: column;
  }
  .bottom {
    position: absolute;
    bottom: 6px;
    top: calc(55vh + 24px);
    width: 100%;
  }
}
</style>
