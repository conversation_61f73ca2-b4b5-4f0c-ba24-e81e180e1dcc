<template>
  <div class="page">
    <div class="content">
      <strawAll
        :code="code"
        :adminData="adminData"
        :lastYear="lastYear"
        :adminName="adminName"
        :year="year"
      >
        <div class="slot-wrap">
          <DateSelect class="date-select" @change="dateChange" :years="years" />
          <AdminSelect class="admin-select" @change="adminChange" />
        </div>
      </strawAll>
    </div>
  </div>
</template>

<script>
import DateSelect from "@/app/Components/DateSelect";
import strawAll from "./child/All";
import AdminSelect from "@/app/Components/AdminSelect";
import Card from "@/app/Components/Card";
import { years } from "./config/date.config";
export default {
  name: "",
  data() {
    return {
      year: "2018",
      code: "45",
      lastYear: null,
      adminData: ["45"],
      adminName: "广西",
    };
  },
  computed: {
    years() {
      return years;
    },
  },
  components: {
    DateSelect,
    strawAll,
    AdminSelect,
  },
  methods: {
    dateChange(e) {
      this.year = e.year;
      this.lastYear = e.lastYear;
    },
    adminChange(e, i, name) {
      this.code = e;
      this.adminData = i;
      this.adminName = name;
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";
.page {
  position: relative;
  width: calc(100% - 100px);
  height: 100%;
  background-color: rgb(233, 233, 233);
  color: @primaryTextColor;
  margin-left: 100px;
  padding: 12px;
  .slot-wrap {
    height: 50px;
    background: #fff;
    display: flex;
    padding: 20px 15px 5px 20px;

    .date-select {
      flex: 1;
      margin-right: 10px;
    }
    .admin-select {
      flex: 3;
    }
  }
  .content {
    position: absolute;
    top: 12px;
    bottom: 0px;
    left: 12px;
    right: 12px;
  }
}
</style>
