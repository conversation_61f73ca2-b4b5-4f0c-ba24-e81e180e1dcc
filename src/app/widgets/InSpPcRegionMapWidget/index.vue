<!-- 该文件主要用于处理<资源保护-外来入侵物种-农业外来入侵植物普查>中筛选植物后的地图分级 -->
<template>
  <div class="jimu-widget map-widget">
    <div :id="mapUid" class="jimu-map"></div>
    <Popup ref="popup" :map="map" @close="popupCloseHandler" />
  </div>
</template>

<script>
import { uid } from "uid";
import axios from "axios";

import mapboxgl from "mapbox-gl";
import "mapbox-gl/dist/mapbox-gl.css";

import { mapState } from "vuex";
import * as turf from "@turf/turf";

import BaseWidget from "@/BaseWidget";
import LayerManager from "@/managers/LayerManager";
import Popup from "@/components/Popup";

const MAP_DEFAULT_CENTER = [107, 34];
const MAP_DEFAULT_ZOOM = 3;
const MAP_DEFAULT_MAXZOOM = 18;
const MAP_DEFAULT_MINZOOM = 1;
// const MAP_DEFAULT_BOUNDS = [
//   [104.446554, 20.902282],
//   [112.056814, 26.388535],
// ];
const MAP_DEFAULT_BOUNDSOPTIONS = {
  left: 100,
  top: 100,
  right: 400,
  bottom: 400,
};

export default {
  name: "RegionMapWidget",

  mixins: [BaseWidget],
  data() {
    return {
      data: [],
      updataParam: null,
      mapUid: `jimu-map-${uid()}`,
      highlightLayerUid: `jimu-layer-${uid()}`,
      extendsHighlightLayerUid: `jimu-layer-${uid()}`,
      map: null,
      layerManager: null,
      layerConfigMap: {},
      index: "city",
      region: "province",
      regionCode: null,
      nextRegion: "city",
      regionLayerConfig: [
        {
          region: "city",
          layer: "land_city_fill",
          nameLayer: "land_city_name",
        },
        {
          region: "county",
          layer: "land_county_fill",
          nameLayer: "land_county_name",
        },
        {
          region: "town",
          layer: "land_town_fill",
          nameLayer: "land_town_name",
        },
        {
          region: "village",
          layer: "land_village_fill",
          nameLayer: "land_village_name",
        },
      ],
      regionParam: {
        city: "CXZQDM",
        county: "FXZQDM",
        town: "TXZQDM",
        village: "XZQDM",
      },
      regionFilterParam: {
        province: {
          lelve: "",
          codeKey: "PXZQDM",
          nameKey: "qymc",
        },
        city: {
          lelve: "",
          codeKey: "CXZQDM",
          nameKey: "qymc",
        },
        county: {
          lelve: "",
          codeKey: "FXZQDM",
          nameKey: "qymc",
        },
      },
      lxregionFilterParam: {
        province: {
          lelve: "",
          codeKey: "PXZQDM",
        },
        city: {
          codeKey: "CXZQDM",
        },
        county: {
          codeKey: "FXZQDM",
        },
      },
      legendType: "",
      legendParam: {
        rate: {
          percentage: true,
        },
      },
    };
  },

  computed: {
    // 从全局配置中获取地图初始状态
    ...mapState({
      initExtent: (state) => ({
        zoom: state.config.map?.initZoom,
        center: state.config.map?.initCenter,
        maxZoom: state.config.map?.maxZoom,
        minZoom: state.config.map?.minZoom,
        bounds: state.config.map?.bounds,
        boundsOptions: state.config.map?.boundsOptions,
      }),
      basemaps: (state) => state.config?.basemaps,
      apiUrl: (state) => state.config.api,
      adminData: (state) => state.data?.admin,
    }),
  },
  components: { Popup },
  configLoaded() {
    this.init();
    this.addListeners();
  },
  mounted() {},
  activated() {
    if (this.map) {
      this.map.resize();
    }
  },
  watch: {
    index(e) {
      this.regionLayerConfig.forEach((item) => {
        if (item.region == e) {
          this.map.setLayoutProperty(item.layer, "visibility", "visible");
          this.map.setLayoutProperty(item.nameLayer, "visibility", "visible");
        } else {
          this.map.setLayoutProperty(item.layer, "visibility", "none");
          this.map.setLayoutProperty(item.nameLayer, "visibility", "none");
        }
      });
      this.mapUpdateHandler(this.updataParam);
    },
  },

  methods: {
    // 响应table事件
    async tableRowSelectHandler(payload) {
      if (!payload || !payload.key || !payload.recordId) return;
      const config = this.layerConfigMap[payload.key];
      const feature = await this.getFeature(config, payload.recordId);
      if (!feature) return;
      let location = this.getPopupLocation(feature);
      this.map.flyTo({
        center: [location.lng, location.lat],
        zoom: 11,
        speed: 1,
      });
      this.showPopup(feature, config);
      this.highlightFeature(feature, config);
    },

    tableRowUnselectHandler() {
      this.clearMap();
    },
    init() {
      // const style = this.getBasemapStyle()
      this.map = new mapboxgl.Map({
        container: this.mapUid,
        center: this.initExtent.center || MAP_DEFAULT_CENTER,
        zoom: this.initExtent.zoom || MAP_DEFAULT_ZOOM,
        minZoom:
          this.config?.minZoom ||
          this.initExtent.minZoom ||
          MAP_DEFAULT_MINZOOM,
        maxZoom: this.initExtent.maxZoom || MAP_DEFAULT_MAXZOOM,
        bounds: this.config?.bounds || this.initExtent.bounds,
        fitBoundsOptions: {
          padding: this.config?.boundsOptions || MAP_DEFAULT_BOUNDSOPTIONS,
        },
        // TODO: 提升地图体验，初始化时不加载任何在线资源
        // bounds: [
        //   [104.446554, 20.902282],
        //   [112.056814, 26.388535]
        // ],
        // fitBoundsOptions: {
        //   padding: { left: 100, top: 100, right: 400, bottom: 400 }
        // },
        style: "config/style/baseMapStyle.json",
      });
      // 初始化LayerManager
      this.layerManager = new LayerManager(this.map);
      // 分发地图实例
      this.map.on("load", () => {
        this.bus.$emit(this.$events.map.MAP_LOAD, this.map);
        window.map = this.map;
        this.map.on("click", (e) => {
          const features = this.map.queryRenderedFeatures(e.point);
          const layerConfig = this.layerConfigMap[features[0].layer.id];
          if (layerConfig.popup) {
            this.addLayerPopupHandler(layerConfig, features[0], e.lngLat);
          }
        });
      });
    },

    // 为图层添加点击事件
    async addLayerPopupHandler(layerConfig, feature, lngLat) {
      if (layerConfig.popup.type == "request") {
        let thisFeature = feature;
        let data = await this.requestPopupFeature(layerConfig, thisFeature);
        feature.properties = data[0];
      }
      if (layerConfig.popup.type == "specialRequest") {
        feature.properties = await this.getSpecialRequestData(
          feature,
          layerConfig.popup
        );
      }
      if (!feature) return;
      // 显示popup
      await this.showPopup(feature, layerConfig, lngLat);

      if (layerConfig.key == "land_aqlyjztjq") {
        this.bus.$emit("aqlyjzq_info_show", feature);
      }
      // 高亮
      this.highlightFeature(feature, layerConfig);
    },
    async getSpecialRequestData(feature, popup) {
      const value = feature.properties[popup.layerField];
      let filter = ["all"];
      if (popup.filter) {
        popup.filter.forEach((item) => {
          filter.push(item);
        });
      }
      filter.push(["=", popup.tableField, value]);
      if (filter.length == 2) {
        filter = filter[1];
      }
      let param = {
        returnGeometry: false,
        format: "json",
        filter,
      };
      const data = await this.$apis.feature.query(popup.uri, param);
      return data[0];
    },
    async requestPopupFeature(layerConfig, thisFeature) {
      const params = {
        returnGeometry: false,
        format: "json",
        filter: [
          "=",
          layerConfig.primaryKey,
          thisFeature.properties[layerConfig.primaryKey],
        ],
      };
      const data = await this.$apis.feature.query(layerConfig.name, params);
      return data;
    },

    // 高亮显示feature
    highlightFeature(feature, layerConfig) {
      if (!layerConfig.highlight) {
        this.map.removeLayer(this.highlightLayerUid);
      }
      this.addHighlightLayer(layerConfig, feature);
      // this.map.getSource(this.highlightLayerUid).setData(feature)
    },
    // 添加高亮图层
    addHighlightLayer(layerConfig, feature) {
      // const source = this.map.getSource(this.highlightLayerUid)
      // if (!source) {
      //   this.map.addSource(this.highlightLayerUid, EMPTY_SOURCE)
      // }
      // const layer = this.map.getLayer(this.highlightLayerUid)
      // if (layer) {
      //   this.map.removeLayer(this.highlightLayerUid)
      // }
      // this.map.addLayer({
      //   id: this.highlightLayerUid,
      //   source: this.highlightLayerUid,
      //   type: layerConfig.type,
      //   paint: layerConfig.paint
      // })
      const layer = this.map.getLayer(this.highlightLayerUid);
      if (layer) {
        this.map.removeLayer(this.highlightLayerUid);
      }
      const filter = [
        "==",
        layerConfig.primaryKey,
        feature.properties[layerConfig.primaryKey],
      ];
      const options = {
        id: this.highlightLayerUid,
        source: layerConfig.key,
        type: layerConfig.highlight.type,
        paint: layerConfig.highlight.paint,
        filter,
      };
      if (layerConfig.type == "vector") {
        options["source-layer"] = layerConfig["source-layer"] || "layer";
      }
      this.map.addLayer(options);
    },

    getBasemapStyle() {
      if (!this.basemaps || !Array.isArray(this.basemaps)) return;
      const defaultBasemaps = this.basemaps.filter(
        (basemap) => basemap.default
      );
      const basemap =
        defaultBasemaps.length > 0 ? defaultBasemaps[0] : this.basemaps[0];
      return this.genBasemapStyle(basemap);
    },

    // 获取或者生成底图的样式
    genBasemapStyle(basemap) {
      if (!basemap) return;
      if (basemap.type == this.$constants.layerTypes.VECTOR) {
        return basemap.url;
      } else if (basemap.type == this.$constants.layerTypes.RASTER) {
        const id = `jimu-basemap-${uid()}`;
        return {
          version: 8,
          sources: {
            [id]: {
              type: "raster",
              tiles: [basemap.url],
              tileSize: basemap.tileSize || 256,
            },
          },
          layers: [
            {
              id,
              type: "raster",
              source: id,
            },
          ],
        };
      }
    },

    addListeners() {
      this.bus.$on(this.$events.map.LAYER_SHOW, this.layerShowHandler);
      this.bus.$on(this.$events.map.LAYER_HIDE, this.layerHideHandler);
      this.bus.$on(
        this.$events.map.GEOMETRY_FILTER_UPDATE,
        this.geometryFilterUpdateHandler
      );
      this.bus.$on(this.$events.table.ROW_SELECT, this.tableRowSelectHandler);
      this.bus.$on(
        this.$events.admin.CHANGE_ADMIN,
        this.adminFilterUpdateHandler
      );
      this.map.on("click", () => {
        this.clearMap();
      });

      this.bus.$on(this.$events.map.SET_LAYER_FILTER, this.setLayerFilter);
      this.bus.$on(this.$events.map.MAP_ZOOM_INIT, this.zoomInit);
      // this.bus.$on("ChartLevelChange", this.changeLevel);

      this.bus.$on("RegionCHART_UPDATE", (e) => {
        this.legendType = e.type;
        this.updataParam = e;
        this.mapUpdateHandler(e);
      });
    },
    // changeLevel(e) {
    //   this.index = e;
    //   console.log(e, "eeee");
    //   this.mapUpdateHandler(this.updataParam);
    // },
    adminFilterUpdateHandler(adminData) {
      this.index = adminData ? adminData.nextRegion : "city";
      if (adminData) {
        if (adminData.bounds) {
          this.map.fitBounds(adminData.bounds);
        }
        if (adminData.region) {
          let region = this.config.regionWidget[adminData.region];
          let layerConfig = this.layerConfigMap[region.layer];
          if (this.map.getLayer(this.extendsHighlightLayerUid)) {
            this.map.removeLayer(this.extendsHighlightLayerUid);
          }
          if (adminData.level || adminData.level == 0) {
            let layer = {
              id: this.extendsHighlightLayerUid,
              source: region.layer,
              "source-layer": layerConfig["source-layer"],
              // filter: this.map.getFilter(highlightLayer)
            };
            layer.filter = region.slice
              ? [
                  "==",
                  ["slice", ["get", region.keyCode], 0, adminData.code.length],
                  adminData.code,
                ]
              : ["==", ["get", region.keyCode], adminData.code];
            this.map.addLayer(Object.assign(layer, layerConfig.highlight));
          }
        }
      } else {
        this.zoomInit();
        this.map.removeLayer(this.extendsHighlightLayerUid);
      }

      const layerFilter = this.getModuleData(
        this.$constants.shareDataKey.LAYER_FILTER
      );
      const drawFeature = this.getModuleData(
        this.$constants.shareDataKey.GEOMETRY_FILTER
      );
      const geometryFilter = drawFeature ? drawFeature.geometry : null;
      const regionFilter = this.getRagionFilter();
      this.layerManager.refreshAllLayers(
        geometryFilter,
        regionFilter,
        layerFilter
      );
      // this.mapUpdateHandler(this.updataParam);
    },
    zoomInit() {
      const bounds = this.config?.map?.bounds || this.initExtent.bounds;
      const padding =
        this.config?.map?.boundsOptions || this.initExtent.boundsOptions;
      if (bounds && padding) {
        this.map.fitBounds(bounds, {
          padding,
        });
        return;
      }
      this.map.flyTo({
        zoom: this.initExtent.zoom || MAP_DEFAULT_ZOOM,
        center: this.initExtent.center || MAP_DEFAULT_CENTER,
      });
    },
    getRagionFilter() {
      const regionData = this.getModuleData(
        this.$constants.shareDataKey.REGION_FILTER
      );
      if (regionData && regionData.region) {
        let region = this.config.regionWidget[regionData.region];

        const regionFilter = region.slice
          ? [
              "==",
              ["slice", ["get", region.keyCode], 0, regionData.code.length],
              regionData.code,
            ]
          : ["==", ["get", region.keyCode], regionData.code];
        return regionFilter;
      } else {
        return null;
      }
    },
    // 添加图层过滤条件
    setLayerFilter(name) {
      const layerItem = this.layerConfigMap[name];
      const filterData = this.getModuleLayerFilter(name);
      const drawFeature = this.getModuleData(
        this.$constants.shareDataKey.GEOMETRY_FILTER
      );
      const geometryFilter = drawFeature ? drawFeature.geometry : null;
      const regionFilter = this.getRagionFilter();
      if (layerItem) {
        this.layerManager.refreshLayer(
          layerItem,
          geometryFilter,
          regionFilter,
          filterData
        );
      }
    },
    async mapUpdateHandler(e) {
      if (!e) return;
      let species_bm = e.filters.find((item) => item[1] === "species_bm");
      // 当没有选中物种时不显示图例；
      if (e.filters.length === 0 || !species_bm)
        return this.bus.$emit(this.$events.map.LEGEND_HIDE, "analysis");
      if (this.index == "town") return;
      let params = {
        returnGeometry: false,
        format: "json",
      };
      params.filter = ["all", ...e.filters];
      if (this.region) {
        params.filter.push([
          "=",
          this.regionFilterParam[this.region].codeKey,
          this.regionCode ? this.regionCode : "45",
        ]);
      }

      if (params.filter.length == 2) {
        params.filter = params.filter[1];
      }
      let colors = this.config.colors
        ? this.config.colors
        : ["#fffdd0", "#ddeeab", "#b7db95", "#60a965", "#265936"];
      let data = [];

      data = await this.$apis.feature.query(
        `species_occurred_${this.index}`,
        params
      );

      data.length === 0
        ? this.bus.$emit(this.$events.map.LEGEND_HIDE, "analysis")
        : null;

      let field = `${data[0].species_name}(亩)`;
      const values = data.map((item) => item[e.field]);
      let splitList = this.splitDataByAvg(values);
      splitList = Array.from(new Set(splitList));
      let legendData = colors
        .map((item, index) => {
          if (index < splitList.length - 1) {
            return {
              min: Number(splitList[index]),
              max: Number(splitList[index + 1]),
            };
          } else if (index === splitList.length - 1) {
            // 最后一个区间显示大于最后一个数值的情况
            return {
              min: Number(splitList[index]),
              max: Infinity,
            };
          } else {
            return false;
          }
        })
        .filter((item) => {
          return item;
        });
      this.bus.$emit(this.$events.map.LEGEND_SHOW, {
        key: "analysis",
        legend: {
          title: field,
          decimal: 2,
          type: "polygon",
          items: legendData.map((item, index) => {
            let itemData = item;
            return {
              label:
                index === legendData.length - 1
                  ? `${itemData.min}以上`
                  : itemData,
              symbol: {
                color: colors[index],
                icon: "",
              },
            };
          }),
        },
      });
      if (splitList.length == 1) {
        splitList.push(splitList[0]);
      }
      let paintColor;

      paintColor = ["match", ["get", this.regionParam[this.index]]];
      let dataField = e.field;
      let applyField = this.regionFilterParam[this.index].codeKey;

      data.forEach((item) => {
        for (let i = 0; i < splitList.length; i++) {
          let lastIndex = splitList.length - 1;
          if (
            item[dataField] > splitList[i] &&
            (i == lastIndex || item[dataField] <= splitList[i + 1])
          ) {
            let color = i === lastIndex ? colors[lastIndex] : colors[i];
            paintColor.push(item[applyField], color);
            break;
          }
        }
      });

      // data.forEach((item) => {
      //   let matched = false;
      //   for (let i = 0; i < splitList.length - 1; i++) {
      //     if (item[e.field] == splitList[0] || (item[e.field] > splitList[i] && item[e.field] <= splitList[i + 1])) {
      //       paintColor.push(item[this.regionFilterParam[this.index].codeKey], colors[i]);
      //       matched = true;
      //       break;
      //     }
      //   }
      //   if (!matched) {
      //     // 处理大于最后一个区间的情况
      //     paintColor.push(item[this.regionFilterParam[this.index].codeKey], colors[colors.length - 1]);
      //   }
      // });

      // paintColor.push(colors[0])
      paintColor.push("rgba(0,0,0,.05)");

      if (data.length === 0) {
        paintColor = "rgba(0,0,0,.05)";
      }

      this.$nextTick(() => {
        this.map.setPaintProperty(
          this.regionLayerConfig.find((item) => {
            return item.region == this.index;
          }).layer,
          "fill-color",
          paintColor
        );
        if (this.index === "county") {
          this.map.setPaintProperty(
            "land_county_fill",
            "fill-outline-color",
            "rgba(175,135,255,1)"
          );
        }
      });
    },
    
    splitDataByAvg(values, splitLen = 5) {
      function getRoundOff(value, base) {
        let floor = Math.floor(value / base);
        if (floor > 10) {
          if (base > 10000) {
            let result = floor * base;
            return {
              value: result,
              floor,
              base,
            };
          }
          base = base * 10;
          return getRoundOff(value, base);
        } else {
          let result = floor * base;
          return {
            value: result,
            floor,
            base,
          };
        }
      }

      function getStepValue(value, base) {
        let length = splitLen - 2;
        if (value == 0) return 0;
        let floor = Math.floor(value / base);
        if (floor > 10) {
          base = base * 10;
          return getStepValue(value, base);
        } else {
          if (base == 10 && value < 10)
            return Number((value / length).toFixed(0));
          let average = Number(floor * base) / length;
          let result = Math.max(0.5, Math.round(average / base)) * base;
          return result;
        }
      }

      if (values.length == 0) return [];
      values.sort((a, b) => {
        return (a ? parseFloat(a) : 0) - (b ? parseFloat(b) : 0);
      });
      const _values = [];
      values.forEach((item) => {
        if (!item) return false;
        if (_values.indexOf(item) < 0) {
          _values.push(Number(item));
        }
      });
      values = _values;
      let stepValue = 0;
      let stepStart = 0;
      let lastIndex = values.length - 1;
      // 获取相应的数值
      const endIndex =
        values.length > 2 ? Math.floor(lastIndex * 0.8) : lastIndex;
      const startIndex = values.length > 3 ? Math.floor(lastIndex * 0.2) : 0;

      // 获取最大值与最小值的差距
      let valueGap = values[endIndex] - values[0];
      // 获取步骤差距
      stepValue = getStepValue(valueGap, 0.01);
      // 判断大小值差距与步骤差距
      let minResult = getRoundOff(values[startIndex], 0.01);
      stepStart =
        minResult.base > valueGap ? Math.floor(values[startIndex]) : values[0];
      if (stepStart == 0) stepStart = values[startIndex];
      stepValue = getStepValue(valueGap, 0.01);

      const stepList = [];
      for (let i = 0; i < splitLen; i++) {
        let value = i > 0 ? stepValue * i + stepStart : 0;
        value = Number(value.toFixed(2));
        if (value > 10) value = Math.floor(value);
        if (value > 10000) value = getRoundOff(value, 10000).value;
        console.log(value, "step-item");
        stepList.push(value);
      }
      return stepList;
    },

    // 显示图层
    layerShowHandler(layers) {
      if (!layers || !Array.isArray(layers)) return;
      layers.forEach(async (layer) => {
        let layerConfig = this.layerConfigMap[layer.key];
        // 获取图层配置信息
        if (!layerConfig) {
          layerConfig = this.layerConfigMap[layer.key] =
            await this.getLayerConfig(layer);
        }
        // 获取空间过滤geometry
        if (layerConfig.region == this.index) {
          layerConfig.style.layout.visibility = "visible";
        }
        // 获取空间过滤geometry
        const drawFeature = this.getModuleData(
          this.$constants.shareDataKey.GEOMETRY_FILTER
        );
        const geometryFilter = drawFeature ? drawFeature.geometry : null;
        const regionFilter = this.getRagionFilter();
        const filterData = this.getModuleLayerFilter(layer.key);
        const layerStyleData = this.getModuleLayerStyle(layer.key);
        const newLayer = await this.layerManager.showLayer(
          layerConfig,
          geometryFilter,
          regionFilter,
          filterData,
          layerStyleData
        );
        // 对新增图层添加点击事件响应
        if (
          newLayer &&
          layerConfig.type !== this.$constants.layerTypes.RASTER &&
          layerConfig.popup
        ) {
          this.addLayerClickHandler(layerConfig);
        }
        // 打开图例
        if (layerConfig.legend) {
          this.bus.$emit(this.$events.map.LEGEND_SHOW, {
            key: layerConfig.key,
            legend: layerConfig.legend,
            orderWeight: layerConfig.orderWeight,
          });
        }
      });

      this.mapUpdateHandler(this.updataParam);
      //
      this.clearMap();
    },
    showPopup(feature, layerConfig, lngLat) {
      const props = feature.properties || {};
      const popup = layerConfig.popup || {};
      // const items = this.genPopupItems(props, popup.fields)
      // if (!items) return
      // const data = {
      //   title: props[popup.title],
      //   items
      // }
      // 纠正popup显示位置
      let location = this.getPopupLocation(feature);
      location = !location ? lngLat : location;
      this.$refs.popup.show(location, props, popup, layerConfig.componentPopup);
      //
      this.tryFeatureVisibleInMap(location);
    },
    tryFeatureVisibleInMap(location) {
      const bounds = this.map.getBounds();
      const ne = bounds.getNorthEast();
      const sw = bounds.getSouthWest();
      const bbox = turf.bbox(
        turf.featureCollection([
          turf.point([ne.lng, ne.lat]),
          turf.point([sw.lng, sw.lat]),
        ])
      );
      const contains = turf.booleanContains(
        turf.bboxPolygon(bbox),
        turf.point([location.lng, location.lat])
      );
      if (!contains) {
        this.map.flyTo({
          center: [location.lng, location.lat],
        });
      }
    },

    addLayerClickHandler(layerConfig) {
      this.map.on("click", layerConfig.key, (e) => {
        if (layerConfig.popup) {
          // return
          const popupData = layerConfig.popup;
          // const res =
          this.showPopup(
            e.lngLat,
            e.features[0].properties,
            popupData,
            layerConfig.componentPopup
          );
        } else {
          let feature = e.features[0];
          const qydm = feature.properties[this.regionParam[this.index]];
          let res = this.data.find((item) => {
            return item.qydm == qydm;
          });
          const popupData = {
            title: "qymc",
            fields: [
              {
                label: this.updataParam.name,
                field: this.updataParam.field,
              },
            ],
          };
          this.showPopup(e.lngLat, res, popupData);
        }
      });
      this.map.on("dblclick", layerConfig.key, (e) => {
        let obj = e.features[0].properties;
        let list = [];
        if (obj.CXZQDM) {
          list.push({ index: 0, codeKey: "CXZQDM", code: obj.CXZQDM });
        }
        if (obj.FXZQDM) {
          list.push({ index: 1, codeKey: "FXZQDM", code: obj.FXZQDM });
        }
        if (obj.TXZQDM) {
          list.push({ index: 2, codeKey: "TXZQDM", code: obj.TXZQDM });
        }
        this.bus.$emit("emit_admin_change", list);
      });
    },

    async getLayerConfig(layer) {
      if (!layer || !layer.key || !layer.config) return;
      layer = this.$utils.object.deepCloneJson(layer);
      const resp = await axios.get(layer.config);
      if (layer.extend) {
        let extend = await axios.get(layer.extend);
        resp.data = Object.assign(resp.data, extend.data);
      }
      let config = resp.data;
      config.url = this.genLayerUrl(config);
      Object.assign(layer, config);
      return layer;
    },

    genLayerUrl(layer) {
      if (!layer || !layer.type) return;
      switch (layer.type) {
        case this.$constants.layerTypes.VECTOR:
          return `${this.apiUrl?.vector}/${layer.name}/{z}/{x}/{y}.vector.pbf`;
        case this.$constants.layerTypes.RASTER:
          return `${this.apiUrl?.raster}/${layer.name}/{z}/{x}/{y}.png`;
        case this.$constants.layerTypes.FEATURE:
          return `${this.apiUrl?.feature}`;
      }
    },

    // 隐藏图层
    layerHideHandler(layers) {
      if (!layers || !Array.isArray(layers)) return;
      layers.forEach((layer) => {
        this.layerManager.hideLayer(layer);
        // 关闭图层
        const layerConfig = this.layerConfigMap[layer.key];
        if (layerConfig.legend) {
          this.bus.$emit(this.$events.map.LEGEND_HIDE, layer.key);
        }
      });
      //
      this.clearMap();
    },
    geometryFilterUpdateHandler(feature) {
      const geometryFilter = feature ? feature.geometry : null;
      const layerFilter = this.getModuleData(
        this.$constants.shareDataKey.LAYER_FILTER
      );
      const regionFilter = this.getRagionFilter();
      this.layerManager.refreshAllLayers(
        geometryFilter,
        regionFilter,
        layerFilter
      );
    },
    // 纠正popup显示位置
    getPopupLocation(feature) {
      if (!feature || !feature.geometry?.coordinates) return;
      const coord = feature.geometry.coordinates;
      const type = feature.geometry?.type;
      let location;
      if (!coord || !type) return;
      switch (type) {
        case "Point": {
          location = coord;
          break;
        }
        case "MultiPoint": {
          // 取第一个点
          location = coord[0];
          break;
        }
        case "LineString": {
          // 取中间点
          const length = coord.length;
          const index = Math.floor(length / 2);
          location = coord[index];
          break;
        }
        case "MultiLineString": {
          // 取第一条线的中间点
          const length = coord[0].length;
          const index = Math.floor(length / 2);
          location = coord[0][index];
          break;
        }
        case "Polygon": {
          // 取多边形的中心点
          const polygon = turf.polygon(coord);
          const point = turf.centerOfMass(polygon);
          location = point.geometry.coordinates;
          break;
        }
        case "MultiPolygon": {
          // 取第一个多边形的中心点
          const polygon = turf.polygon(coord[0]);
          const point = turf.centerOfMass(polygon);
          location = point.geometry.coordinates;
          break;
        }
      }
      if (location) {
        return new mapboxgl.LngLat(location[0], location[1]);
      }
    },
    clearMap() {
      // 关闭popup
      this.$refs.popup.close();
      // 清除高亮显示
      this.removeHighlight();
    },
    // 清除高亮
    removeHighlight() {
      const layer = this.map.getLayer(this.highlightLayerUid);
      if (layer) {
        this.map.removeLayer(this.highlightLayerUid);
      }
    },
    popupCloseHandler() {
      this.removeHighlight();
      this.bus.$emit("clearInfoWidget");
    },
  },
};
</script>

<style lang="less" scoped>
.jimu-map {
  width: 100%;
  height: 100%;
}
</style>
