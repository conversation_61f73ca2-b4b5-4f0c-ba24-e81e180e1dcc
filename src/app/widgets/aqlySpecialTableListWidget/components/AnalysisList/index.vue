<template>
  <div v-show="hasData">
    <div class="filter-item">
      <template v-for="(item, i) in configL">
        <a-statistic
          class="statistic-bg"
          :key="i"
          :value="wanChange(item.value, item.decimals)"
          :valueStyle="{ color: item.color }"
        >
          <span slot="title" class="title">{{ item.label }}</span>
          <span slot="suffix" class="suffix-unit"
            >{{ Number(item.value) > 10000 ? "万" : "" }}{{ item.unit }}</span
          >
        </a-statistic>
      </template>
    </div>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
export default {
  name: "ListFilter",
  data() {
    return {
      configList: this.config,
      hasData: true,
      filter: null,
      list: [
        {
          label: "合格率",
          field: "qualifiedRate",
          value: 0,
          unit: "%",
          color: "rgb(0,0,255)",
        },
        {
          label: "合格",
          field: "qualified",
          value: 0,
          unit: "个",
          color: "rgb(0, 179, 0)",
        },
        {
          label: "不合格",
          field: "unqualified",
          value: 0,
          unit: "个",
          color: "rgb(179, 179, 0)",
        },
      ],
    };
  },
  mixins: [BaseWidget],
  props: {
    config: {
      type: Object,
    },
    adminFilter: {
      type: Array,
    },
    region: {
      type: String,
    },
    selectValue: {
      type: String,
    },
  },

  computed: {
    configL() {
      if (this.config.list.length == 0) {
        return this.list;
      } else {
        return this.configList.list;
      }
    },
  },

  methods: {
    wanChange(item, decimals) {
      if (Number(item) > 10000) return (Number(item) / 10000).toFixed(2);
      if (decimals) return Number(item).toFixed(decimals);
      return Number(item);
    },

    async getAggregation() {
      let adminFilter = this.adminFilter
        ? this.adminFilter
        : ["=", "PXZQDM", "45"];
      // 年份筛选
      let dateFilter = ["=", "date", this.selectValue];
      // 点位类型筛选
      let typeFilter = ["=", "dwlx", "自证农产品"];
      let filter = ["all", adminFilter, dateFilter, typeFilter];

      let param = {
        filter: filter,
        groupFields: this.config.aggregation.groupFields,
        aggregates: this.config.aggregation.aggregates,
      };
      let res = await this.$apis.feature.aggregate(
        this.config.aggregation.uri,
        param
      );
      if (res) {
        this.hasData = true;
        if (this.config.list.length == 0) {
          let total = 0;
          let qualified = 0;
          let unqualified = 0;
          res.forEach((item) => {
            total += item["zhn_class_count"];
            // 正式环境为数字 1、测试环境为字符串 "1"
            if (item["zhn_class"] == 1) {
              qualified += item["zhn_class_count"];
            } else {
              unqualified += item["zhn_class_count"];
            }
          });
          let qualifiedRate = parseFloat((qualified / total) * 100).toFixed(2);

          this.list.forEach((item) => {
            if (item.field === "qualifiedRate") {
              item.value = qualifiedRate;
            } else if (item.field === "qualified") {
              item.value = qualified;
            } else if (item.field === "unqualified") {
              item.value = unqualified;
            }
          });
        } else {
          this.configList.list.forEach((item) => {
            let value = res.find((i) => {
              return item.typeValue == i[item.typeField];
            });
            item.value = value ? value[item.field] : 0;
          });
        }
      } else {
        this.hasData = false;
      }
    },
  },
  mounted() {
    this.getAggregation();
  },
  watch: {
    adminFilter() {
      this.getAggregation();
    },
    selectValue() {
      this.getAggregation();
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";
.statistic-bg {
}

.filter-item {
  display: flex;
  justify-content: space-around;
  /deep/.ant-statistic-title {
    text-align: center;
  }
  /deep/.ant-statistic-content {
    // background-color: rgba(204, 202, 79, 0.05);
  }
  .suffix-unit {
    font-size: 10px;
  }
  .title {
    color: @primaryTextColor;
  }
  .btn {
    padding: 8px 10px;
    color: #fff;
  }
}
</style>
