<template>
  <div v-if="tableConfig" class="wrap">
    <div>
      <template v-if="tableConfig.elemFilter">
        <div class="search">
          <span class="search_label">时期:</span>
          <a-select
            :default-value="selectValue"
            style="width: 80%"
            @change="handleChange"
          >
            <a-select-option
              v-for="(item, index) in selectArr"
              :key="index"
              :value="item.value"
            >
              {{ item.label }}
            </a-select-option>
          </a-select>
        </div>
        <template v-if="tableConfig.analysis">
          <AnalysisList
            class="analysis-list"
            :config="tableConfig.analysis"
            :region="region"
            :adminFilter="adminFilter"
            :selectValue="selectValue"
          />
        </template>
        <ElemChange
          class="elem-filter"
          @setFilter="setElemFilter"
          :filters="tableConfig.elemFilter"
        />
      </template>
      <template v-if="tableConfig.filters">
        <ListFilter @setFilter="setTypeFilter" :filters="tableConfig.filters" />
      </template>
    </div>
    <ListTable
      v-show="loading"
      :layout="layout"
      class="list-table"
      :res="res"
      @rowItemClick="rowItemClick"
    />
    <a-icon v-show="!loading" type="loading" />
    <div class="pagination">
      <a-button class="page-btn" :disabled="pageCurrent == 1" @click="prePage"
        >上一页</a-button
      >
      <a-button class="page-btn" @click="nextPage">下一页</a-button>
    </div>
    <!-- <a-pagination
      v-model="pageCurrent"
      :page-size-options="pageSizeOptions"
      :total="total"
      show-size-changer
      :page-size="pageSize"
      @change="pageChange"
      @showSizeChange="onShowSizeChange"
    >
      <template slot="buildOptionText" slot-scope="props">
        <span>{{ props.value }}条/页</span>
      </template>
    </a-pagination> -->
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
import ListFilter from "../ListFilter/index.vue";
import ElemChange from "../ElemChange/index.vue";
import ListTable from "../ListTable/index.vue";
import AnalysisList from "../AnalysisList/index.vue";
import { mapState } from "vuex";

export default {
  name: "TabContent",
  mixins: [BaseWidget],
  components: { ListFilter, ListTable, ElemChange, AnalysisList },
  data() {
    return {
      pageSizeOptions: ["10", "20", "30", "40", "50"],
      pageCurrent: 1,
      hasNext: true,
      pageSize: 10,
      total: 50,
      res: null,
      filters: [],
      typeFilter: null,
      elemFilter: null,
      adminFilter: null,

      region: "city",
      specialFilter: null,
      regionParam: {
        city: "CXZQDM",
        county: "FXZQDM",
        town: "TXZQDM",
        village: "XZQDM",
      },
      loading: true,

      selectZzArr: [
        {
          label: "2020年自证采样农产品",
          value: "2020",
        },
        {
          label: "2021年自证采样农产品",
          value: "2021",
        },
        {
          label: "2022年自证采样农产品",
          value: "2022",
        },
        {
          label: "2023年自证采样农产品",
          value: "2023",
        },
      ],
      selectNydArr: [
        {
          label: "2020年农用地安全利用台账",
          value: "2020",
        },
        // {
        //   label: "2021年农用地安全利用台账",
        //   value: "2021",
        // },
      ],
      select: [],
      selectValue: "2020",
    };
  },
  props: {
    tableConfig: {
      type: Object,
    },
    type: {
      type: Number,
    },
  },
  async mounted() {
    this.bus.$on(`setTableFilter_${this.tableConfig.value}`, (e) => {
      if (e.layer == this.tableConfig.value) {
        this.setSpecialFilter(e);
      }
    });
    this.bus.$on(
      this.$events.admin.CHANGE_ADMIN,
      this.adminFilterUpdateHandler
    );

    // this.handleChange(this.selectValue);

    await this.getData(1);
  },
  computed: {
    // 从全局配置中获取年份配置
    ...mapState({
      years: (state) => state.config.year?.environment,
    }),
    selectArr() {
      if (this.type == 0) {
        return this.selectZzArr;
      } else {
        return this.selectNydArr;
      }
    },
    layout() {
      if (this.type == 0) {
        return this.tableConfig.layout;
      } else {
        return this.tableConfig.layoutNyd;
      }
    },
  },
  methods: {
    handleChange(e) {
      // if (this.type == 0) {
      //   if (e == "2020") {
      //     this.bus.$emit(
      //       this.$events.layer.CHECKED_LAYER,
      //       "land_lnjcd_ncp_2020"
      //     );
      //   } else if (e == "2021") {
      //     this.bus.$emit(
      //       this.$events.layer.CHECKED_LAYER,
      //       "land_lnjcd_ncp_2021"
      //     );
      //   }
      // } else {
      //   if (e == "2020") {
      //     this.bus.$emit(
      //       this.$events.layer.CHECKED_LAYER,
      //       "land_aqlytz_gx_2020"
      //     );
      //   } else if (e == "2021") {
      //     this.bus.$emit(
      //       this.$events.layer.CHECKED_LAYER,
      //       "land_aqlytz_gx_2021"
      //     );
      //   } else if (e == "2022") {
      //     this.bus.$emit(
      //       this.$events.layer.CHECKED_LAYER,
      //       "land_aqlytz_gx_2022"
      //     );
      //   }
      // }
      this.select = [];
      this.select = this.selectArr.filter((item) => {
        return item.value == e;
      });
      console.log("this.select", this.select);
      this.selectValue = e;
      // this.bus.$emit(this.$events.layer.CHECKED_LAYER, "land_aqlytz_gx_2020");
      this.getData();
    },
    setSpecialFilter(e) {
      if (!this.specialFilter) {
        this.specialFilter = {};
      }
      if (!this.specialFilter[e.layer]) {
        this.$set(this.specialFilter, e.layer, {});
      }
      const layerData = this.specialFilter[e.layer];
      Object.assign(layerData, {
        [e.key]: e.tableFilter,
      });
      this.$set(this.specialFilter, [e.layer], layerData);
      this.getData();
    },
    async getData() {
      this.loading = false;
      let countParams = {};
      let param = {
        returnGeometry: false,
        format: "json",
        // outFields,
        limit: this.pageSize,
        page: this.pageCurrent,
      };
      // let filter = ['any', ...this.typeFilter, ...this.elemFilter]
      let filter = ["all"];
      if (this.typeFilter) {
        filter.push(this.typeFilter);
      }
      if (this.elemFilter) {
        filter.push(this.elemFilter);
      }
      if (this.adminFilter) {
        filter.push(this.adminFilter);
      }
      if (this.specialFilter?.[this.tableConfig.value]) {
        Object.values(this.specialFilter[this.tableConfig.value]).forEach(
          (item) => {
            filter.push(item);
          }
        );
      }

      // 添加时间过滤
      if (this.selectValue && this.type == 0) {
        filter.push(["=", "date", this.selectValue]);
      }

      if (filter?.length == 2) {
        filter = filter[1];
      }
      if (filter?.length !== 1) {
        param.filter = filter;
        countParams.filter = filter;
      }
      // if (!hasPageNum) {
      //   const count = await this.$apis.feature.count(
      //     this.tableConfig.uri,
      //     this.tableConfig.primaryKey,
      //     countParams
      //   )
      //   this.total = count
      // }
      let nextParam = {
        returnGeometry: false,
        format: "json",
        // outFields,
        limit: this.pageSize,
        page: this.pageCurrent + 1,
      };
      let uri;
      if (this.type == 0) {
        uri = this.tableConfig.uri;
      } else {
        if (this.selectValue == "2020") {
          uri = "land_aqlytz_gx";
        } else {
          uri = "land_aqlytz_gx_2021";
        }
      }
      let ares = await this.$apis.feature.query(uri, nextParam);
      let res = await this.$apis.feature.query(uri, param);
      if (ares.length == 0 || res.length == 10) {
        this.hasNext = false;
      }
      res.map((item) => {
        item.zhn_class = Number(item.zhn_class) > 1 ? "不合格" : "合格";
      });
      this.res = res;
      if (res.length > 0 && res.length < 10) {
        this.hasNext = false;
        this.res = res;
      }
      this.loading = true;
    },
    onShowSizeChange(current, pageSize) {
      this.pageSize = pageSize;
      this.getData();
    },
    nextPage() {
      this.pageCurrent++;
      this.getData();
    },
    prePage() {
      // if (this.pageCurrent > 1) {
      this.pageCurrent--;
      this.getData(this.pageCurrent - 1);
      // }
    },
    setTypeFilter(e) {
      console.log("e", e);
      // let newFilterCopy = ["any"];
      //   newFilterCopy.push(
      //     ["==", ["get", type + "_CLASS"], 1],
      //     ["==", ["get", type + "_CLASS"], 2],
      //     ["==", ["get", type + "_CLASS"], 3]
      //   );
      //   this.setModuleLayerFilter("land_dttz_xcpjdy_gx", "pjdy", newFilterCopy);
      //   this.bus.$emit(
      //     this.$events.map.SET_LAYER_FILTER,
      //     "land_dttz_xcpjdy_gx"
      //   );
      this.typeFilter = e;
      this.getData();
    },
    setElemFilter(e) {
      this.elemFilter = e;
      this.getData();
    },
    rowItemClick(item) {
      console.log("item-------", item, this.tableConfig);

      // this.bus.$emit(this.$events.table.ROW_SELECT, {
      //   key: this.tableConfig.value,
      //   recordId: item[this.tableConfig.primaryKey],
      // });
      this.bus.$emit(this.$events.table.ROW_SELECT, {
        key: "tz_aqly_2020",
        recordId: item[this.tableConfig.primaryKey],
      });

      this.bus.$emit(this.$events.table.ROW_SELECT, {
        key: "land_aqlytz_gx_2020",
        recordId: item[this.tableConfig.primaryKey],
      });
      this.bus.$emit(this.$events.table.ROW_SELECT, {
        key: "land_aqlytz_gx_2021",
        recordId: item[this.tableConfig.primaryKey],
      });

      this.bus.$emit(this.$events.table.ROW_SELECT, {
        key: "land_lnjcd_ncp_2020",
        recordId: item[this.tableConfig.primaryKey],
      });
      this.bus.$emit(this.$events.table.ROW_SELECT, {
        key: "land_lnjcd_ncp_2021",
        recordId: item[this.tableConfig.primaryKey],
      });
      this.bus.$emit(this.$events.table.ROW_SELECT, {
        key: "land_lnjcd_ncp_2022",
        recordId: item[this.tableConfig.primaryKey],
      });
      this.bus.$emit(this.$events.table.ROW_SELECT, {
        key: "land_lnjcd_ncp_2023",
        recordId: item[this.tableConfig.primaryKey],
      });
    },
    adminFilterUpdateHandler(e) {
      if (e) {
        this.adminFilter = ["=", this.regionParam[e.region], e.code];
        this.region = e.region;
      } else {
        this.adminFilter = null;
        this.region = "city";
      }
    },
  },
  watch: {
    adminFilter() {
      this.getData();
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";
.wrap {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.search {
  // 在同一行
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-bottom: 12px;
  .search_label {
    color: @primaryTextColor;
  }
}

.dropData {
  margin: 0 20px 20px 20px;
}

.list-table {
  margin: 20px 0 40px;
  // box-sizing: border-box;
  flex: 1;
}
.elem-filter {
  margin-bottom: 20px;
}
.analysis-list {
  background-color: #f2f2f2;
  padding: 15px 0;
  margin-bottom: 20px;
}
.pagination {
  display: flex;
  justify-content: center;
  .page-btn {
    margin-right: 20px;
  }
}
</style>
