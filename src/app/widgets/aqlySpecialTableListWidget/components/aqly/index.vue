<template>
  <div>
    <div class="search">
      <span class="search_label">时期:</span>
      <a-select
        :default-value="selectValue"
        style="width: 80%"
        @change="handleChange"
      >
        <a-select-option
          v-for="(item, index) in selectArr"
          :key="index"
          :value="item.value"
        >
          {{ item.label }}
        </a-select-option>
      </a-select>
    </div>

    <div v-for="(item, index) in statisticsData" :key="index">
      <TitleBar :title="item.title"></TitleBar>
      <div class="card">
        <div class="lb">
          <p>合格率</p>
          <p class="num hgl">
            {{ parseFloat(parseFloat(item.data.rate).toFixed(2))
            }}<span class="unit">%</span>
          </p>
        </div>
        <div class="lb">
          <p>合格</p>
          <p class="num hg">
            {{ parseFloat(parseFloat(item.data.hg))
            }}<span class="unit">个</span>
          </p>
        </div>
        <div class="lb">
          <p>不合格</p>
          <p class="num bhg">
            {{ parseFloat(parseFloat(item.data.bhg))
            }}<span class="unit">个</span>
          </p>
        </div>
      </div>
    </div>

    <TitleBar :title="`${selectValue}年各地市农用地安全利用率（%）`"></TitleBar>
    <div class="card" v-if="provinceData">
      <p>{{ provinceData.CXZQMC }}</p>
      <p class="num hgl">
        {{ parseFloat(parseFloat(provinceData.aqly_rate).toFixed(2))
        }}<span class="unit">%</span>
      </p>
    </div>
    <div class="chart-box">
      <Chart :options="chartOption"></Chart>
    </div>
    <template v-if="file">
      <TitleBar :title="`文件链接`"></TitleBar>
      <div class="file-list">
        <a
          class="file"
          target="_blank"
          v-for="item in file"
          :key="item.name"
          :href="item.url"
          >🔗 {{ item.name }}</a
        >
      </div>
    </template>
  </div>
</template>
<script>
import Chart from "@/components/Chart";
import TitleBar from "@/app/Components/TitleBar/";
export default {
  props: ["file"],
  components: {
    Chart,
    TitleBar,
  },
  data() {
    return {
      selectValue: "2023",
      selectArr: [
        {
          label: "2020年",
          value: "2020",
        },
        {
          label: "2021年",
          value: "2021",
        },
        {
          label: "2022年",
          value: "2022",
        },
        {
          label: "2023年",
          value: "2023",
        },
      ],
      statisticsData: {
        dwyj: {
          title: "定位预警监测农产品合格率（%）",
          data: {
            all: 0,
            hg: 0,
            bhg: 0,
            rate: 0,
          },
        },
        zzcy: {
          title: "自证采样农产品合格率（%）",
          data: {
            all: 0,
            hg: 0,
            bhg: 0,
            rate: 0,
          },
        },
      },
      provinceData: null,
      chartOption: {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          formatter: "{b}:{c}%",
        },
        grid: {
          top: "0%",
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
        },
        yAxis: {
          type: "category",
          data: [],
        },
        series: [
          {
            name: "农用地安全利用率",
            type: "bar",
            data: [],
          },
        ],
      },
    };
  },
  mounted() {
    this.getData();
    this.getStatistics();
  },
  methods: {
    async getStatistics() {
      this.statisticsData["zzcy"].data = this.summarizeStats(
        await this.$apis.feature.aggregate(
          "land_lnjcd_ncp",
          this.setParams("自证农产品")
        )
      );

      this.statisticsData["dwyj"].data = this.summarizeStats(
        await this.$apis.feature.aggregate(
          "land_lnjcd_ncp",
          this.setParams("定位预警监测")
        )
      );
    },
    setParams(type) {
      return {
        returnGeometry: false,
        format: "json",
        filter: [
          "all",
          ["=", "date", this.selectValue],
          ["!=", "CXZQDM", "4500"],
          ["=", "dwlx", type],
        ],
        aggregates: [["_id", "count"]],
        groupFields: ["zhn_class"],
      };
    },
    summarizeStats(stats) {
      let summary = {
        all: 0,
        hg: 0,
        bhg: 0,
        rate: 0,
      };

      stats.forEach((item) => {
        summary.all += item._id_count;
        if (item.zhn_class === 1) {
          summary.hg += item._id_count;
        } else {
          summary.bhg += item._id_count;
        }
      });

      summary.rate = summary.all > 0 ? (summary.hg / summary.all) * 100 : 0;
      return summary;
    },
    async getData() {
      let params = {
        returnGeometry: false,
        orderField: "aqly_rate",
        orderType: "ASC",
        format: "json",
        filter: ["=", "date", this.selectValue],
      };
      let res = await this.$apis.feature.query(`land_aqly_rate`, params);
      let label = [];
      let data = [];
      let index = res.findIndex((item) => item.CXZQDM == "4500");
      let province = res.splice(index, 1);
      this.provinceData = province[0];
      res.forEach((item) => {
        label.push(item.CXZQMC);
        data.push(item.aqly_rate);
      });
      this.$set(this.chartOption, "yAxis", {
        type: "category",
        data: label,
      });
      this.$set(this.chartOption, "series", [
        {
          name: "农用地安全利用率",
          type: "bar",
          barWidth: 40,
          data,
          label: {
            show: true,
            position: "inside",
            formatter: "{c}%",
          },
        },
      ]);
    },
    handleChange(value) {
      this.selectValue = value;
      this.getData();
      this.getStatistics();
    },
  },
};
</script>
<style scoped lang="less">
.search {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-bottom: 12px;
}
.chart-box {
  margin-top: 20px;
  width: 100%;
  height: 1000px;
}
.card {
  width: 100%;
  margin: 10px 0;
  border-radius: 8px;
  padding: 8px;
  background-color: rgb(242, 242, 242);
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  .lb {
    width: 33%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-weight: 600;
  }
  p {
    margin-bottom: 0;
  }
  .num {
    font-size: 24px;
    font-weight: 600;
  }
  .unit {
    font-size: 17px;
  }
  .hgl {
    color: rgb(67, 138, 232);
  }
  .hg {
    color: rgb(80, 174, 51);
  }
  .bhg {
    color: rgb(181, 176, 57);
  }
}
.file-list {
  display: flex;
  flex-direction: column;
}
.file {
  display: block;
  margin: 10px 20px;
}
</style>
