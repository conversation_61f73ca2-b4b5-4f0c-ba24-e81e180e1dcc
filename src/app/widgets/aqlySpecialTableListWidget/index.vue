<template>
  <div class="jimu-widget jimu-tool-widget" v-if="configLoaded">
    <div class="tabs-content">
      <div
        :class="['tabs', index == tabIndex ? 'active' : '']"
        v-for="(item, index) in config"
        :key="index"
        @click="tabClick(item, index)"
      >
        {{ item.name }}
      </div>
    </div>

    <template v-if="tabIndex == 0">
      <Aqly :file="config[0].file" class="aqly-content"
    /></template>
    <template v-else>
      <template v-for="(item, index) in config.slice(1)">
        <div v-show="tabIndex - 1 == index" class="content" :key="index">
          <template v-if="item.selectList.length > 1">
            <div class="select-box">
              <a-select v-model="item.defaultID">
                <template v-for="i in item.selectList">
                  <a-select-option :key="i.value" :value="i.value">{{
                    i.label
                  }}</a-select-option>
                </template>
              </a-select>
            </div>
          </template>
          <template v-for="el in Object.values(item.obj)">
            <TabContent
              v-show="el.value == item.defaultID"
              :key="el.value"
              :type="index"
              class="table-content"
              :tableConfig="el"
            />
          </template>
        </div>
      </template>
    </template>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
import TabContent from "./components/TabContent/index.vue";
import Aqly from "./components/aqly";

export default {
  name: "SpecialTableListWidget",
  mixins: [BaseWidget],
  data() {
    return {
      tabIndex: 0,
      configLoaded: false,
    };
  },
  components: {
    TabContent,
    Aqly,
  },

  configLoaded() {
    this.configLoaded = true;
  },

  methods: {
    async _loadModule(uri) {
      const module = await import(`@/${uri}`);
      return this.$utils.object.deepCloneObj(module.default);
    },
    tabClick(item, index) {
      this.tabIndex = index;
      if (this.config[index].name == "农用地安全利用率") {
        this.bus.$emit("checkTZ", 2);
        this.bus.$emit(this.$events.layer.CHECKED_LAYER, [
          "land_lnjcd_ncp_2023",
        ]);
      }

      if (this.config[index].name == "自证农产品监测点") {
        this.bus.$emit("checkTZ", 1);
        this.bus.$emit(this.$events.layer.CHECKED_LAYER, [
          "land_lnjcd_ncp_2020",
        ]);
      }

      if (this.config[index].name == "农用地安全利用措施") {
        this.bus.$emit("checkTZ", 0);
        this.bus.$emit(this.$events.layer.CHECKED_LAYER, [
          "land_aqlytz_gx_2020",
        ]);
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";
.tabs-content {
  display: flex;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #e7ebf4;
  overflow: hidden;
}
.tabs {
  width: 50%;
  height: 40px;
  text-align: center;
  // border: 1px solid #ccc;
  line-height: 40px;
}

.aqly-content {
  padding-top: 20px;
  height: calc(100% - 65px);
  // display: flex;
  // flex-direction: column;
  overflow: scroll;
}
.content {
  height: calc(100% - 65px);
  display: flex;
  flex-direction: column;
}
.active {
  border-bottom: none;
  // color: rgb(62, 145, 247);
  color: #fff;
  background-color: @primaryColor;
}
.select-box {
  margin-top: 20px;
}
.table-content {
  margin-top: 20px;
  // height: calc(100% - 90px);
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
