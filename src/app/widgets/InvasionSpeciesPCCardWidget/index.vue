<template>
  <div class="jimu-widget">
    <div v-if="isLoad">
      <Card class="card" title="踏查点">
        <PlainStatistic :value="count" unit="个" :color="color" />
      </Card>
    </div>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
import PlainStatistic from "@/app/Components/PlainStatistic";
import Card from "@/app/Components/Card";

export default {
  name: "InvasionSpeciesPCCardWidget",

  mixins: [BaseWidget],

  components: {
    Card,
    PlainStatistic,
  },

  data() {
    return {
      isLoad: false,
      filters: [],
      count: 0,
      color: "#4065E0",
      region: "city",
      regionParam: {
        city: "CXZQDM",
        county: "FXZQDM",
        town: "TXZQDM",
        village: "XZQDM",
      },
    };
  },
  mounted() {
    this.bus.$on("selectPCspecies", this.setFilter);
    this.bus.$on(
      this.$events.admin.CHANGE_ADMIN,
      this.adminFilterUpdateHandler
    );
  },
  configLoaded() {
    this.isLoad = true;
    this.getData();
  },

  methods: {
    setFilter(e) {
      const filters = [];
      const speciesFilter = e.find((item) => item.id === "species_bm");
      const wzlxFilter = e.find((item) => item.id === "wzlx");
      if (speciesFilter) {
        filters.push(["=", speciesFilter.id, speciesFilter.value]);
      }

      if (wzlxFilter) {
        filters.push(["=", wzlxFilter.id, wzlxFilter.value]);
      }
      this.filters = filters;
      this.getData();
    },
    adminFilterUpdateHandler(e) {
      if (e) {
        this.adminFilter = ["=", this.regionParam[e.region], e.code];
        this.region = e.region;
      } else {
        this.adminFilter = null;
        this.region = "city";
      }
      this.getData();
    },

    async getData() {
      let filter = [];
      if (this.adminFilter !== null) {
        filter.push(this.adminFilter);
      }
      if (this.filters.length > 0) {
        filter.push(...this.filters);
      }
      console.log("filter------", filter);

      if (filter?.length === 0) {
        filter = null;
      }
      if (filter?.length > 1) {
        filter.unshift("all");
      }
      if (filter?.length == 1) {
        filter = filter[0];
      }

      console.log("this.filters---===", filter);

      let params = {
        returnGeometry: false,
        format: "json",
        aggregates: [["tcd_id", "count", null, "distinct"]],
        filter,
      };

      try {
        let res = await this.$apis.feature.aggregate(
          "species_tcd_wz_2020",
          params
        );
        this.count = res[0]["tcd_id_count"];
      } catch (error) {
        console.error("Error fetching data:", error);
        // 处理错误情况
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";

.card {
  width: 250px;
}
</style>
