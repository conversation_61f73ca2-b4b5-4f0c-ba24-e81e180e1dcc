<template>
  <div class="jimu-widget">
    <Card :title="config.name" v-if="isLoad">
      <PlainStatistic :value="count" unit="个" :color="color" />
    </Card>
    <Card class="card" title="物种数量" v-if="isLoad">
      <PlainStatistic :value="plantNum" unit="个" :color="color" />
    </Card>
  </div>
</template>

<script>
import BaseWidget from '@/BaseWidget'
import PlainStatistic from '@/app/Components/PlainStatistic'
import Card from '@/app/Components/Card'
export default {
  name: 'PlantNumAnalysisWidget',
  mixins: [BaseWidget],
  components: {
    Card,
    PlainStatistic
  },
  data() {
    return {
      isLoad: false,
      filters: [],
      levelParam: ['PXZQDM', 'CXZQDM', 'FXZQDM'],
      level: 0,
      adminCode: '45',
      adminData: null,
      count: null,
      color: '#4065E0',
      plantNum: ''
    }
  },
  mounted() {
    this.bus.$on(this.$events.admin.CHANGE_ADMIN, this.adminFilterUpdateHandler)
    this.bus.$on('setAnalysisFilterCount', this.setFilter)
  },

  configLoaded() {
    this.isLoad = true
    this.getData()
    this.getPlantNum()
  },
  methods: {
    setFilter(e) {
      this.filters = e
      if (e) {
        this.getData()
      }
    },
    async getPlantNum() {
      let params = {
        returnGeometry: false,
        format: 'json',
        aggregates: [['id', 'count']]
      }
      let res = await this.$apis.feature.aggregate('plant_list_gx', params)
      this.plantNum = res[0]['id_count']
    },
    adminFilterUpdateHandler(e) {
      if (e) {
        this.adminData = e
        this.adminCode = e.code
        this.level = e.level
      } else {
        this.adminData = null
        this.adminCode = '45'
        this.level = 0
      }
      this.getData()
    },
    async getData() {
      let filter = [
        'all',
        ['=', this.levelParam[this.level], this.adminCode],
        ...this.filters
      ]
      if (filter.length == 2) {
        filter = filter[1]
      }

      let params = {
        returnGeometry: false,
        format: 'json',
        filter,
        aggregates: [['ysc_bm', 'count', null, 'distinct']]
      }
      // let res = await this.$apis.feature.query('base_gx', params)
      let res = await this.$apis.feature.aggregate(this.config.table, params)
      this.count = res[0]['ysc_bm_count']
    }
  }
}
</script>

<style lang="less" scoped>
// .wrap {
// width: 220px;
.card {
  margin-top: 12px;
}
// }
</style>