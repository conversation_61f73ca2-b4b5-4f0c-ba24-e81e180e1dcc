<template>
  <div class="jimu-widget jimu-tool-widget">
    <StatisticsSlot style="margin-top: 20px">
      <template>
        <span slot="title" class="jimu-widget-title-ui"
          >国控监测点建设情况</span
        >
      </template>
      <template>
        <div slot="contain" class="table-container">
          <div class="table">
            <a-table
              :columns="tableColumn"
              :data-source="tableData"
              :scroll="{ x: 470 }"
              :pagination="false"
            ></a-table>
          </div>
        </div>
      </template>
    </StatisticsSlot>
    <StatisticsSlot>
      <template>
        <span slot="title" class="jimu-widget-title-ui"
          >施肥模式&减排措施处理设置</span
        >
      </template>
      <template>
        <div slot="contain" class="contain">
          <div class="data-content" style="flex-direction: column">
            <div
              class="data-item flex-row"
              style="height: 50px"
              v-for="(item, index) in sfjpList"
              :key="index"
            >
              <div class="title">{{ item.title }}:</div>
              <div class="value">{{ item.value }}</div>
            </div>
          </div>
        </div>
      </template>
    </StatisticsSlot>
    <StatisticsSlot>
      <template>
        <span slot="title" class="jimu-widget-title-ui">监测数据分析</span>
      </template>
      <template>
        <div slot="contain" class="contain">
          <template>
            <div slot="contain">
              <div class="data-content">
                <div
                  class="data-item"
                  style="width: 50%; margin-bottom: 20px"
                  v-for="(item, index) in jcsjList"
                  :key="index"
                >
                  <div class="title">{{ `${item.title}(${item.unit})` }}</div>
                  <div class="valueCon">
                    <div class="value">最高值:{{ item.max }}</div>
                    <div class="value">最低值:{{ item.min }}</div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </template>
    </StatisticsSlot>
    <StatisticsSlot>
      <template>
        <span slot="title" class="jimu-widget-title-ui">2023年监测数据</span>
      </template>
      <template>
        <div slot="contain" class="contain">
          <div
            v-for="(item, index) in jcsj23TableColumn"
            :key="index"
            class="list-table"
          >
            <div class="title" style="color: #002159">
              {{ item.title }}
            </div>
            <a-table
              :columns="item.tableColumn"
              :data-source="item.tableData"
              :scroll="{ x: 470 }"
              :pagination="false"
            ></a-table>
          </div>
        </div>
      </template>
    </StatisticsSlot>
    <StatisticsSlot>
      <template>
        <span slot="title" class="jimu-widget-title-ui">2024年监测数据</span>
      </template>
      <template>
        <div slot="contain" class="contain">
          <div class="data-content">
            <div
              class="data-item"
              v-for="(item, index) in jcsh_24"
              :key="index"
            >
              <a-card
                :title="item.qysj"
                class="cardTitleBack"
                :headStyle="{ color: '#002159', background: '#e3e8f2' }"
              >
                <a-card
                  v-for="(e, ind) in item.data"
                  :key="ind"
                  :title="`施肥模式:${e.properties.sfms}`"
                  style="margin-bottom: 10px"
                  :headStyle="{ color: '#002159' }"
                >
                  <div class="value">{{ `径流量(L):${e.properties.jll}` }}</div>
                  <div class="value">{{ `TN(mg/L):${e.properties.tn}` }}</div>
                  <div class="value">{{ `TP(mg/L):${e.properties.tp}` }}</div>
                  <div class="value">{{ `NO3:${e.properties.no}` }}</div>
                  <div class="value">{{ `NH4:${e.properties.nh}` }}</div>
                  <div class="value">{{ `面积(亩):${e.properties.mj}` }}</div>
                </a-card>
              </a-card>
            </div>
          </div>
        </div>
      </template>
    </StatisticsSlot>
  </div>
</template>

<script>
import StatisticsSlot from "@/app/StatisticsSlot";
import BaseWidget from "@/BaseWidget";
const REGION_LIST = ["PXZQDM", "CXZQDM", "FXZQDM", "TXZQDM", "XZQDM"];
export default {
  name: "ControlledMonitorAnalysisWidget",

  mixins: [BaseWidget],

  components: {
    StatisticsSlot,
  },

  data() {
    return {
      regionFilter: [],
      typeFilter: [["=", "date", "2022"]],
      region: "city",
      tableColumn: [
        {
          width: 110,
          dataIndex: "cysl",
          title: "采样次数",
          sorter: (a, b) => a.cysl - b.cysl,
        },
        {
          width: 110,
          dataIndex: "sysl",
          title: "水样数量",
          sorter: (a, b) => a.sysl - b.sysl,
        },
        {
          width: 110,
          dataIndex: "trsl",
          title: "土壤数量",
          sorter: (a, b) => a.trsl - b.trsl,
        },
        {
          width: 140,
          dataIndex: "zzysl",
          title: "植株样数量",
          sorter: (a, b) => a.zzysl - b.zzysl,
        },
      ],
      jcsjList: [
        {
          title: "TN",
          max: null,
          min: null,
          unit: "mg/L",
        },
        {
          title: "TP",
          max: null,
          min: null,
          unit: "mg/L",
        },
        {
          title: "N03-",
          max: null,
          min: null,
          unit: "mg/L",
        },
        {
          title: "NH4+",
          max: null,
          min: null,
          unit: "mg/L",
        },
      ],
      sfjpList: [
        {
          title: "常规生产模式(CK)",
          value: null,
          field: "ck_jpcscl",
        },
        {
          title: "关键减排模式(KF)",
          value: null,
          field: "fk_jpcscl",
        },
        {
          title: "综合减排模式(BMP)",
          value: null,
          field: "bmp_jpcscl",
        },
      ],
      jcsh_24: [],
      tableData: [],

      jcsj23TableColumn: [
        {
          index: 0,
          title: "养分投入量情况",
          tableColumn: [
            {
              title: "N(千克/亩)",
              dataIndex: "yftrl_n",
              key: "yftrl_n",
              width: 120,
            },
            {
              title: "P2O5(千克/亩)",
              dataIndex: "yftrl_p",
              key: "yftrl_p",
              width: 125,
            },
            {
              title: "K2O(千克/亩)",
              dataIndex: "yftrl_k",
              key: "yftrl_k",
              width: 120,
            },
            {
              title: "氮流失量(千克/亩)",
              dataIndex: "trcl_qd",
              key: "trcl_qd",
              width: 145,
            },
            {
              title: "磷流失量(千克/亩)",
              dataIndex: "trcl_zl",
              key: "trcl_zl",
              width: 145,
            },
          ],
          tableData: [
            {
              yftrl_n: "",
              yftrl_p: "",
              yftrl_k: "",
              trcl_qd: "",
              trcl_zl: "",
            },
          ],
        },
        {
          index: 1,
          title: "采收后土壤养分残留情况",
          tableColumn: [
            {
              title: "全氮(毫克/千克)",
              dataIndex: "trcl_qd",
              key: "trcl_qd",
              width: 140,
            },
            {
              title: "总磷(毫克/千克)",
              dataIndex: "trcl_zl",
              key: "trcl_zl",
              width: 140,
            },
            {
              title: "全钾(毫克/千克)",
              dataIndex: "trcl_qk",
              key: "trcl_qk",
              width: 140,
            },
            {
              title: "硝态氮(毫克/千克)",
              dataIndex: "trcl_nod",
              key: "trcl_nod",
              width: 145,
            },
            {
              title: "氨态氮(毫克/千克)",
              dataIndex: "trcl_nhd",
              key: "trcl_nhd",
              width: 145,
            },
            {
              title: "有机质(克/千克)",
              dataIndex: "trcl_yjw",
              key: "trcl_yjw",
              width: 140,
            },
            {
              title: "土壤_ph",
              dataIndex: "tr_ph",
              key: "tr_ph",
              width: 120,
            },
          ],
          tableData: [
            {
              trcl_qd: "",
              trcl_zl: "",
              trcl_qk: "",
              trcl_nod: "",
              trcl_nhd: "",
              trcl_yjw: "",
              tr_ph: "",
            },
          ],
        },
        {
          index: 2,
          title: "监测点的雨水和灌溉水养分含量",
          tableColumn: [
            {
              title: "雨水总氮",
              dataIndex: "ys_zd",
              key: "ys_zd",
              width: 110,
            },
            {
              title: "雨水_可溶性总氮",
              dataIndex: "ys_krzd",
              key: "ys_krzd",
              width: 140,
            },
            {
              title: "雨水_总磷",
              dataIndex: "ys_zl",
              key: "ys_zl",
              width: 120,
            },
            {
              title: "雨水_可溶性总磷",
              dataIndex: "ys_krzl",
              key: "ys_krzl",
              width: 150,
            },
            {
              title: "雨水_ph",
              dataIndex: "ys_ph",
              key: "ys_ph",
              width: 110,
            },
            {
              title: "灌溉水_总氮",
              dataIndex: "ggs_zd",
              key: "ggs_zd",
              width: 130,
            },
            {
              title: "灌溉水_可溶性总氮",
              dataIndex: "ggs_krzd",
              key: "ggs_krzd",
              width: 160,
            },
            {
              title: "灌溉水_总磷",
              dataIndex: "ggs_zl",
              key: "ggs_zl",
              width: 120,
            },
            {
              title: "灌溉水_可溶性总磷",
              dataIndex: "ggs_krzl",
              key: "ggs_krzl",
              width: 155,
            },
            {
              title: "灌溉水_ph",
              dataIndex: "ggs_ph",
              key: "ggs_ph",
              width: 120,
            },
          ],
          tableData: [
            {
              ys_zd: "",
              ys_krzd: "",
              ys_zl: "",
              ys_krzl: "",
              ys_ph: "",
              ggs_zd: "",
              ggs_krzd: "",
              ggs_zl: "",
              ggs_krzl: "",
              ggs_ph: "",
            },
          ],
        },
        {
          index: 3,
          title: "投入产出统计情况",
          tableColumn: [
            {
              title: "种植季",
              dataIndex: "zzj",
              key: "zzj",
              width: 110,
            },
            {
              title: "经济产量(千克/亩)",
              dataIndex: "jjcl",
              key: "jjcl",
              width: 150,
            },
            {
              title: "秸秆(树枝)(千克/亩)",
              dataIndex: "jg",
              key: "jg",
              width: 155,
            },
            {
              title: "还田量(千克/亩)",
              dataIndex: "htl",
              key: "htl",
              width: 130,
            },
            {
              title: "经济收入(元/亩)",
              dataIndex: "jjsr",
              key: "jjsr",
              width: 130,
            },
            {
              title: "肥料成本(元/亩)",
              dataIndex: "fl",
              key: "fl",
              width: 130,
            },
            {
              title: "产投比",
              dataIndex: "ctb",
              key: "ctb",
              width: 110,
            },
          ],
          tableData: [
            {
              zzj: "",
              jjcl: "",
              jg: "",
              htl: "",
              jjsr: "",
              fl: "",
              ctb: "",
            },
          ],
        },
        {
          index: 4,
          title: "监测点的氮磷流矢量",
          tableColumn: [
            {
              title: "TN",
              dataIndex: "tn",
              key: "tn",
              width: 100,
            },
            {
              title: "可溶性总氮",
              dataIndex: "krx_zd",
              key: "krx_zd",
              width: 110,
            },
            {
              title: "硝态氮",
              dataIndex: "nod",
              key: "nod",
              width: 110,
            },
            {
              title: "氨态氮",
              dataIndex: "nhd",
              key: "nhd",
              width: 110,
            },
            {
              title: "TP",
              dataIndex: "tp",
              key: "tp",
              width: 100,
            },
            {
              title: "可溶性总磷",
              dataIndex: "krx_zl",
              key: "krx_zl",
              width: 110,
            },
            {
              title: "径流量(升/亩)",
              dataIndex: "jll",
              key: "jll",
              width: 120,
            },
          ],
          tableData: [
            {
              tn: "",
              krx_zd: "",
              nod: "",
              nhd: "",
              tp: "",
              krx_zl: "",
              jll: "",
            },
          ],
        },
      ],

      jcsh_23: [
        {
          title: "养分投入量情况",
          data: [
            {
              field: "yftrl_n",
              lable: "N(千克/亩)",
              value: "",
            },
            {
              field: "yftrl_p",
              lable: "P2O5(千克/亩)",
              value: "",
            },
            {
              field: "yftrl_k",
              lable: "K2O(千克/亩)",
              value: "",
            },
            {
              field: "trcl_qd",
              lable: "氮流失量(千克/亩)",
              value: "",
            },
            {
              field: "trcl_zl",
              lable: "磷流失量(千克/亩)",
              value: "",
            },
          ],
        },
        {
          title: "采收后土壤养分残留情况",
          data: [
            {
              field: "trcl_qd",
              lable: "全氮(毫克/千克)",
              value: "",
            },
            {
              field: "trcl_zl",
              lable: "总磷(毫克/千克)",
              value: "",
            },
            {
              field: "trcl_qk",
              lable: "全钾(毫克/千克)",
              value: "",
            },
            {
              field: "trcl_nod",
              lable: "硝态氮(毫克/千克)",
              value: "",
            },
            {
              field: "trcl_nhd",
              lable: "氨态氮(毫克/千克)",
              value: "",
            },
            {
              field: "trcl_yjw",
              lable: "有机质(克/千克)",
              value: "",
            },
            {
              field: "tr_ph",
              lable: "土壤_ph",
              value: "",
            },
          ],
        },
        {
          title: "监测点的雨水和灌溉水养分含量",
          data: [
            {
              field: "ys_zd",
              lable: "雨水总氮",
              value: "",
            },
            {
              field: "ys_krzd",
              lable: "雨水_可溶性总氮",
              value: "",
            },
            {
              field: "ys_zl",
              lable: "雨水_总磷",
              value: "",
            },
            {
              field: "ys_krzl",
              lable: "雨水_可溶性总磷",
              value: "",
            },
            {
              field: "ys_ph",
              lable: "雨水_ph",
              value: "",
            },
            {
              field: "ggs_zd",
              lable: "灌溉水_总氮",
              value: "",
            },
            {
              field: "ggs_krzd",
              lable: "灌溉水_可溶性总氮",
              value: "",
            },
            {
              field: "ggs_zl",
              lable: "灌溉水_总磷",
              value: "",
            },
            {
              field: "ggs_krzl",
              lable: "灌溉水_可溶性总磷",
              value: "",
            },
            {
              field: "ggs_ph",
              lable: "灌溉水_ph",
              value: "",
            },
          ],
        },
        {
          title: "投入产出统计情况",
          data: [
            {
              field: "zzj",
              lable: "种植季",
              value: "",
            },
            {
              field: "jjcl",
              lable: "经济产量(千克/亩)",
              value: "",
            },
            {
              field: "jg",
              lable: "秸秆(树枝)(千克/亩)",
              value: "",
            },
            {
              field: "htl",
              lable: "还田量(千克/亩)",
              value: "",
            },
            {
              field: "jjsr",
              lable: "经济收入(元/亩)",
              value: "",
            },
            {
              field: "fl",
              lable: "肥料成本(元/亩)",
              value: "",
            },
            {
              field: "ctb",
              lable: "产投比",
              value: "",
            },
          ],
        },
        {
          title: "监测点的氮磷流矢量",
          data: [
            {
              field: "tn",
              lable: "TN",
              value: "",
            },
            {
              field: "krx_zd",
              lable: "可溶性总氮",
              value: "",
            },
            {
              field: "nod",
              lable: "硝态氮",
              value: "",
            },
            {
              field: "nhd",
              lable: "氨态氮",
              value: "",
            },
            {
              field: "tp",
              lable: "TP",
              value: "",
            },
            {
              field: "krx_zl",
              lable: "可溶性总磷",
              value: "",
            },
            {
              field: "jll",
              lable: "径流量(升/亩)",
              value: "",
            },
          ],
        },
      ],

      wybm: "",
      date: "",
    };
  },
  methods: {
    adminChange(admin) {
      let region = "city";
      let filter = [];
      if (admin) {
        region = admin.region;
        filter = ["=", REGION_LIST[admin.level], admin.code];
      }
      this.region = region;
      this.regionFilter = filter;
    },
    getPopupData(data) {
      console.log(data);
      this.wybm = data.wybm;
      this.date = data.date;
      this.getTableData();
      this.get23Data();
      this.get24Data();
    },

    async getTableData() {
      const wybmFilter = ["=", "wybm", this.wybm];
      const dateFilter = ["=", "date", this.date];
      let filter = ["all", wybmFilter, dateFilter, this.regionFilter];
      filter.forEach((item) => {
        if (item == null || item == undefined || item == "") {
          filter.splice(filter.indexOf(item), 1);
        }
      });
      const param = {
        filter,
      };

      const { features } = await this.$apis.feature.query(
        "dlls_jcd_point",
        param
      );
      this.tableData = features.map((item) => {
        return {
          cysl: item["properties"].cycs,
          sysl: item["properties"].sysl,
          trsl: item["properties"].trsl,
          zzysl: item["properties"].zzysl,
        };
      });
      this.sfjpList.forEach((item) => {
        if (item.field === "ck_jpcscl") {
          item.value = features[0]["properties"].ck_jpcscl;
        }
        if (item.field === "fk_jpcscl") {
          item.value = features[0]["properties"].fk_jpcscl;
        }
        if (item.field === "bmp_jpcscl") {
          item.value = features[0]["properties"].bmp_jpcscl;
        }
      });
      this.jcsjList.forEach((item) => {
        if (item.title === "TN") {
          item.max = features[0]["properties"].tnmax;
          item.min = features[0]["properties"].tnmin;
        }
        if (item.title === "TP") {
          item.max = features[0]["properties"].tpmax;
          item.min = features[0]["properties"].tpmin;
        }
        if (item.title === "N03-") {
          item.max = features[0]["properties"].nomax;
          item.min = features[0]["properties"].nomin;
        }
        if (item.title === "NH4+") {
          item.max = features[0]["properties"].nh4max;
          item.min = features[0]["properties"].nh4min;
        }
      });
    },
    async get23Data() {
      const wybmFilter = ["=", "wybm", this.wybm];
      const dateFilter = ["=", "date", "2023"];
      let filter = ["all", wybmFilter, dateFilter, this.regionFilter];
      filter.forEach((item) => {
        if (item == null || item == undefined || item == "") {
          filter.splice(filter.indexOf(item), 1);
        }
      });
      const param = {
        filter,
      };

      const { features } = await this.$apis.feature.query(
        "2023_dlls_jcd_data",
        param
      );
      // 如果为空，则null
      const group23 = features[0]["properties"];

      this.jcsj23TableColumn.forEach((item) => {
        if (item.index === 0) {
          item.tableData[0] = {
            yftrl_n: group23.yftrl_n,
            yftrl_p: group23.yftrl_p,
            yftrl_k: group23.yftrl_k,
            trcl_qd: group23.trcl_qd,
            trcl_zl: group23.trcl_zl,
          };
        } else if (item.index === 1) {
          item.tableData[0] = {
            trcl_qd: group23.trcl_qd,
            trcl_zl: group23.trcl_zl,
            trcl_qk: group23.trcl_qk,
            trcl_nod: group23.trcl_nod,
            trcl_nhd: group23.trcl_nhd,
            trcl_yjw: group23.trcl_yjw,
            tr_ph: group23.tr_ph,
          };
        } else if (item.index === 2) {
          item.tableData[0] = {
            ys_zd: group23.ys_zd,
            ys_krzd: group23.ys_krzd,
            ys_zl: group23.ys_zl,
            ys_krzl: group23.ys_krzl,
            ys_ph: group23.ys_ph,
            ggs_zd: group23.ggs_zd,
            ggs_krzd: group23.ggs_krzd,
            ggs_zl: group23.ggs_zl,
            ggs_krzl: group23.ggs_krzl,
            ggs_ph: group23.ggs_ph,
          };
        } else if (item.index === 3) {
          item.tableData[0] = {
            zzj: group23.zzj,
            jjcl: group23.jjcl,
            jg: group23.jg,
            htl: group23.htl,
            jjsr: group23.jjsr,
            fl: group23.fl,
            ctb: group23.ctb,
          };
        } else if (item.index === 4) {
          item.tableData[0] = {
            tn: group23.tn,
            krx_zd: group23.krx_zd,
            nod: group23.nod,
            nhd: group23.nhd,
            tp: group23.tp,
            krx_zl: group23.krx_zl,
            jll: group23.jll,
          };
        }
      });
    },

    async get24Data() {
      const wybmFilter = ["=", "wybm", this.wybm];
      const dateFilter = ["=", "date", "2024"];
      let filter = ["all", wybmFilter, dateFilter, this.regionFilter];
      filter.forEach((item) => {
        if (item == null || item == undefined || item == "") {
          filter.splice(filter.indexOf(item), 1);
        }
      });
      const param = {
        filter,
      };

      const { features } = await this.$apis.feature.query(
        "2024_dlls_jcd_data",
        param
      );
      let jcsh_24 = [];

      features.forEach((item) => {
        // 如果item.properties.qysj相同，则分组为同一天的数据
        let index = jcsh_24.findIndex((g) => g.qysj === item.properties.qysj);
        if (index === -1) {
          jcsh_24.push({
            qysj: item.properties.qysj,
            data: [item],
          });
        } else {
          jcsh_24[index].data.push(item);
        }
      });
      console.log("jcsh_24", jcsh_24);
      this.jcsh_24 = jcsh_24;
    },
  },
  mounted() {
    this.bus.$on("popupData", this.getPopupData);
    this.bus.$on(this.$events.admin.CHANGE_ADMIN, this.adminChange);
  },
  watch: {
    regionFilter() {
      this.getTableData();
      this.get23Data();
      this.get24Data();
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";
.table-container {
  height: 220px; /* 你想要的固定高度 */
}
.data-content {
  display: flex;

  flex-wrap: wrap;
  justify-content: space-between;
  padding: 0 30px;
  .flex-row {
    display: flex;
    flex-direction: row;
  }
  .data-item {
    width: 100%;
    // margin-bottom: 20px;
    .title {
      font-size: 16px;
      color: #002159;
      margin-bottom: 10px;
    }
    .value {
      font-size: 16px;
      color: #002159;
    }
  }
}

.diff-chart {
  width: 100%;
  height: 350px;
}
.garden-chart {
  width: 370px;
  height: 300px;
  margin: 0 auto;
}

.icon-box {
  border-radius: 50%;
  background-color: rgba(64, 101, 224, 0.1);
  float: left;
  height: 50px;
  width: 50px;
  margin: 10px 15px 0px 0px;
  text-align: center;
  line-height: 50px;
  .icon {
    font-size: 30px;
  }
}

.jimu-widget {
  overflow: scroll;
}

// /deep/ .ant-empty-normal {
//   margin: 140px 0 !important;
// }

.list-table {
  margin: 0 20px 20px 20px;
}

/deep/ .ant-table-thead > tr > th {
  background-color: #e3e8f2;
  color: #002159;
}

.cardTitleBack {
  margin-bottom: 10px;
}
</style>
