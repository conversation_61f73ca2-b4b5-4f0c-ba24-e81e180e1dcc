<template>
  <div class="jimu-widget wrap" v-if="isLoad">
    <template v-for="(item, index) in config">
      <template v-if="item.type == 'cascaderFilter'">
        <div :key="index" class="select-item">
          <a-cascader
            :allowClear="false"
            :options="item.list"
            :placeholder="item.placeholder ? item.placeholder : '请选择'"
            @change="onChange"
            v-model="item.value"
          />
        </div>
      </template>
      <template v-else-if="item.type == 'cascaderField'">
        <div :key="index" class="select-item">
          <a-cascader
            :allowClear="false"
            :options="item.list"
            :placeholder="item.placeholder ? item.placeholder : '请选择'"
            @change="onChange"
            v-model="item.value"
            change-on-select
          />
        </div>
      </template>
      <template v-else>
        <div :key="index" class="select-item">
          <a-select
            v-model="item.value"
            @change="onChange"
            :placeholder="item.placeholder ? item.placeholder : '请选择'"
          >
            <template v-for="(i, n) in item.list">
              <a-select-option :key="n" :value="i.value">
                {{ i.label }}
              </a-select-option>
            </template>
          </a-select>
        </div>
      </template>
    </template>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
export default {
  name: "GKMapFilterWidget",
  mixins: [BaseWidget],
  data() {
    return {
      isLoad: false,
    };
  },
  mounted() {
    // this.onChange()
  },
  configLoaded() {
    this.isLoad = true;
    setTimeout(() => {
      this.onChange();
    }, 500);
  },
  computed: {},
  methods: {
    onChange() {
      let fieldObj = this.config.find((item) => {
        return item.type == "field" || item.type == "filter";
      });
      const field =
        fieldObj.type == "cascaderField"
          ? fieldObj.value[fieldObj.value.length - 1]
          : fieldObj.value;

      let thisField = fieldObj.list.find((item) => {
        return item.value == field;
      });
      const type = thisField?.type;
      let filters = this.config
        .filter((item) => {
          return item.type == "filter" && item.value;
        })
        .map((item) => {
          return ["=", item.field, item.value];
        });
      let cascaderFilterData = [];
      const cascaderFilter = this.config.filter((item) => {
        return item.type == "cascaderFilter";
      });
      cascaderFilter.forEach((elem) => {
        elem.field.forEach((fieldItem, index) => {
          cascaderFilterData.push([
            "=",
            fieldItem,
            elem.value[index] ? elem.value[index] : "合计",
          ]);
        });
      });
      filters = [...filters, ...cascaderFilterData];

      console.log("...cascaderFilterData", filters[0][2]);

      const year = filters[0][2];
      this.setModuleLayerFilter("dl_dlls_jcd_point", "date", [
        "==",
        ["get", "date"],
        year,
      ]);
      this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "dl_dlls_jcd_point");

      this.setModuleLayerFilter("dxdkdc", "date", [
        "==",
        ["get", "年份"],
        year,
      ]);
      this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "dxdkdc");

      this.bus.$emit("RegionCHART_UPDATE", {
        field,
        filters,
        type,
        name: thisField ? thisField.label : field,
      });
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";
.wrap {
  display: flex;

  .select-item {
    background: #ffffff;
    border-radius: 4px;
    padding: 3px;
    box-shadow: 2px 2px 3px rgb(0 0 0 / 20%);
    margin-right: 12px;
  }
  /deep/.ant-select-selection--single {
    border: none;
  }
  /deep/.ant-cascader-picker {
    min-width: 200px;
  }
  /deep/.ant-cascader-input {
    border: none;
  }
  /deep/.ant-select {
    min-width: 120px;
  }
}
</style>
