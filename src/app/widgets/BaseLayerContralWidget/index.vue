<template>
  <div class="jimu-widget wrap" v-if="isLoad">
    <template v-for="(item, index) in config">
      <template v-if="item.method == 'like'">
        <div :key="index" class="select-item">
          <a-select v-model="item.value" @change="e => onLikeChange(e, item)">
            <template v-for="(i, n) in item.list">
              <a-select-option :key="n" :value="i.value">
                {{ i.label }}
              </a-select-option>
            </template>
          </a-select>
        </div>
      </template>
      <template v-else>
        <div :key="index" class="select-item">
          <a-select v-model="item.value" @change="e => onChange(e, item)">
            <template v-for="(i, n) in item.list">
              <a-select-option :key="n" :value="i.value">
                {{ i.label }}
              </a-select-option>
            </template>
          </a-select>
        </div>
      </template>
    </template>
  </div>
</template>

<script>
import BaseWidget from '@/BaseWidget'
export default {
  name: 'BaseLayerContralWidget',
  mixins: [BaseWidget],
  data() {
    return {
      isLoad: false
    }
  },

  configLoaded() {
    this.isLoad = true
    // this.onChange()
  },
  methods: {
    async onLikeChange(e, item) {
      let res = await this.$apis.feature.query('base_gx', {
        filter: e ? ['like', item.field, e] : null
      })
      this.map.getSource('ygqz_base_point').setData(res)

      const filterData = this.config
        .filter(item => {
          return item.value
        })
        .map(item => {
          return [item.tableMethod, item.field, item.value]
        })
      this.bus.$emit('setAnalysisFilterCount', filterData)
    },
    async onChange(e, item) {
      if (e) {
        this.setModuleLayerFilter('ygqz_base_point', item.field, [
          item.method,
          ['get', item.field],
          item.value
        ])
      } else {
        this.setModuleLayerFilter('ygqz_base_point', item.field, null)
      }
      this.bus.$emit(this.$events.map.SET_LAYER_FILTER, 'ygqz_base_point')
      if (item.field == 'river') {
        if (e && e !== '无') {
          let params = {
            returnGeometry: false,
            format: 'json',
            outFields: ['FXZQDM'],
            filter: ['=', 'ygqz_river', e]
          }
          let data = await this.$apis.feature.query(
            `gx_land_region_county`,
            params
          )

          let paintColor = [
            'match',
            ['get', 'FXZQDM'],
            data.map(item => {
              return item.FXZQDM
            }),
            'rgba(115, 201, 145,.5)',
            'rgba(0,0,0,0)'
          ]
          this.setModuleLayerStyle(
            'land_river_county_fill',
            'fill-color',
            paintColor
          )
        } else {
          this.setModuleLayerStyle(
            'land_river_county_fill',
            'fill-color',
            'rgba(0,0,0,.0)'
          )
        }
        this.bus.$emit(
          this.$events.map.SET_LAYER_STYLE,
          'land_river_county_fill'
        )
      }
      const filterData = this.config
        .filter(item => {
          return item.value
        })
        .map(item => {
          return [item.tableMethod, item.field, item.value]
        })
      this.bus.$emit('setAnalysisFilterCount', filterData)
    }
  }
}
</script>

<style lang="less" scoped>
@import '@/assets/styles/common.less';

.wrap {
  display: flex;
  .select-item {
    background: #ffffff;
    border-radius: 4px;
    padding: 3px;
    box-shadow: 2px 2px 3px rgb(0 0 0 / 20%);
    margin-right: 12px;
  }
  /deep/.ant-select {
    min-width: 120px;
  }
  /deep/.ant-select-selection--single {
    border: none;
  }
}
</style>