<template>
  <div class="jimu-widget jimu-tool-widget box">
    <div class="tabs-content">
      <div
        :class="['tabs', item == currentTab ? 'active' : '']"
        v-for="(item, index) in tabs"
        :key="index"
        @click="currentTab = item"
      >
        {{ item }}
      </div>
    </div>

    <div class="content scrollbar" v-show="currentTab == '项目简介'">
      <div v-for="(item, index) in profile" :key="index">
        <div class="content_title">{{ item.title }}</div>
        <div
          class="content_font"
          style="text-align: justify; text-indent: 2em"
          v-for="(i, index) in item.info"
          :key="index"
        >
          <p v-html="i"></p>
        </div>
      </div>
    </div>

    <div class="content" v-show="currentTab == '普查成果'">
      <div style="margin-bottom: 20px">
        <div class="box-container">
          <TitleBar :title="'目标物种数情况'"></TitleBar>
          <a-icon
            v-if="hasMbwz && this_year_num"
            class="showEchart"
            @click="showEchart(1)"
            type="bar-chart"
            :style="{ color: 'rgb(64, 101, 224)', fontSize: '20px' }"
          />
        </div>

        <div class="contrast-box">
          <div class="contrast-item">
            <div class="label">本年度情况</div>
            <div class="num-box">
              <div>
                <span class="value">{{
                  this_year_num != null ? this_year_num : "暂无数据"
                }}</span>
                <span class="unit">苗</span>
              </div>

              <div
                v-if="
                  difference_num && difference_num != 0 && last_year_num != null
                "
                class="difference"
              >
                <a-icon
                  :type="difference_num > 0 ? 'arrow-up' : 'arrow-down'"
                  :style="{
                    color: difference_num > 0 ? '#f5222d' : '#52c41a',
                    fontSize: '20px',
                  }"
                />
                <span
                  :style="{
                    color: difference_num > 0 ? '#f5222d' : '#52c41a',
                    fontSize: '20px',
                  }"
                  >{{ difference_num }}</span
                >
                <span style="font-size: 16px">苗</span>
              </div>
            </div>
          </div>
          <div class="contrast-item">
            <div class="label">上一年度情况</div>
            <div class="num-box">
              <div>
                <span class="value">{{
                  last_year_num != null ? last_year_num : "暂无数据"
                }}</span>
                <span class="unit">苗</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="time-select">
        <div style="color: #000">时期：</div>
        <a-select
          :default-value="semi_annual"
          style="flex: 1; border-color: #4065e0 !important"
          @change="handleChange"
        >
          <a-select-option value="上半年"> 上半年 </a-select-option>
          <a-select-option value="下半年"> 下半年 </a-select-option>
        </a-select>
      </div>
      <div style="margin-bottom: 0px">
        <TitleBar :title="`保护点监测结果`"></TitleBar>
        <div class="info-cell" v-if="detailInfo">
          <div>调查时间：{{ detailInfo.month }}</div>
          <div>目标物种：{{ detailInfo.mbwz }}</div>
          <div>生长状况：{{ detailInfo.szzk }}</div>
          <div>物种丰富度(%)：{{ mbwz_ffd }}</div>
        </div>
        <div v-else class="no-data">暂无数据</div>
      </div>

      <div>
        <div class="box-container">
          <TitleBar :title="'调查监测结果记录表'"></TitleBar>
          <a-icon
            v-if="tableData.length > 0"
            class="showEchart"
            @click="showEchart(2)"
            type="bar-chart"
            :style="{ color: 'rgb(64, 101, 224)', fontSize: '20px' }"
          />
        </div>
        <a-table
          v-if="tableData.length > 0"
          :pagination="false"
          :scroll="{ y: 395 }"
          :columns="tableConfig"
          :data-source="tableData"
        >
        </a-table>
        <div v-else class="no-data">暂无数据</div>
      </div>
      <div>
        <TitleBar title="监测数据"></TitleBar>
        <p v-if="detailInfo">
          附件链接: <a-icon type="link" />
          <a
            target="_blank"
            :href="'http://localhost/document/plantword/' + detailInfo.jcbg"
            >信息采集表</a
          >
        </p>
        <div v-else class="no-data">暂无数据</div>
      </div>
      <div>
        <TitleBar title="相关照片"></TitleBar>
        <div
          class="contain cleardiv"
          v-if="detailInfo"
          style="margin-bottom: 10px"
        >
          <div class="imgs">
            <a-carousel arrows>
              <div
                slot="prevArrow"
                class="custom-slick-arrow"
                style="left: 10px; zindex: 1"
              >
                <a-icon type="left-circle" style="color: #fff" />
              </div>
              <div
                slot="nextArrow"
                class="custom-slick-arrow"
                style="right: 10px"
              >
                <a-icon type="right-circle" style="color: #fff" />
              </div>
              <div v-for="(item, index) in detailInfo.zpbm" :key="index">
                <a
                  class="img-item"
                  style="width: 100%"
                  :href="`${imgUrl}${item}`"
                  target="_blank"
                >
                  <img style="width: 100%" :src="`${imgUrl}${item}`" alt="" />
                </a>
              </div>
            </a-carousel>
          </div>
        </div>
        <div v-else class="no-data">暂无数据</div>
      </div>
    </div>
    <a-modal
      v-model="modalVisible"
      title="调查监测结果记录表曲线图"
      :maskClosable="false"
      destroyOnClose
      width="80%"
      class="form-modal-box"
      :footer="null"
    >
      <Echart :options="echartOptions" style="height: 600px"></Echart>
    </a-modal>
  </div>
</template>

<script>
import TitleBar from "../../Components/TitleBar";
import BaseWidget from "@/BaseWidget";
import { mapState } from "vuex";

import Echart from "@/components/Chart";

export default {
  name: "PlantSpeciesDetailEchart",

  mixins: [BaseWidget],

  components: {
    TitleBar,
    Echart,
  },

  computed: {
    ...mapState({
      document: (state) => state.config.api.document,
    }),
    mbwz_ffd() {
      let result = (this.detailInfo.mbwz_ffd * 100).toFixed(2);
      return result;
    },
  },
  data() {
    return {
      currentTab: "项目简介",
      tabs: ["项目简介", "普查成果"],
      profile: [],
      detailInfo: null,
      tableConfig: [
        {
          title: "样地编码",
          dataIndex: "ydbm",
          align: "center",
        },
        {
          title: "目标物种数(苗)",
          dataIndex: "yd_mbwz_num",
          align: "center",
        },
        {
          title: "丰富度(%)",
          dataIndex: "ff_rate",
          align: "center",
        },
      ],
      imgUrl: "http://localhost/img/plant/plantimg/",
      filters: [],
      semi_annual: "上半年",
      tableData: [],
      isShowEchart: false,
      modalVisible: false,
      option: {
        xAxis: {
          type: "category",
          data: [],
          name: "样地编码",
        },
        yAxis: {
          type: "value",
          name: "目标物种数(苗)",
        },
        series: [
          {
            data: [],
            type: "line",
            name: "目标物种数(苗)",
          },
        ],
        // 坐标提示
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            label: {
              backgroundColor: "#6a7985",
            },
          },
          formatter: function (param) {
            console.log("param", param);
            param = param[0].data;
            return [
              "年份: " + param.date + "<br/>",
              "时期: " + param.semi_annual + "<br/>",
              "目标物种数(亩): " + param.value + "<br/>",
              "丰富度(%): " + param.ff_rate + "<br/>",
            ].join("");
          },
        },
      },
      option1: {
        xAxis: {
          type: "category",
          data: [],
          name: "年份",
        },
        yAxis: {
          type: "value",
          name: "目标物种数(苗)",
        },
        series: [
          {
            data: [],
            type: "line",
            name: "目标物种数(苗)",
          },
        ],
        // 坐标提示
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            label: {
              backgroundColor: "#6a7985",
            },
          },
          formatter: function (param) {
            return [
              "年份: " + param[0].axisValue + "<br/>",
              "目标物种数(亩): " + param[0].value + "<br/>",
            ].join("");
          },
        },
      },
      last_year_num: null,
      this_year_num: null,
      difference_num: null,

      hasMbwz: true,
      echartOptions: null,
      analysisData: null,
    };
  },
  methods: {
    handleChange(val) {
      this.semi_annual = val;
      this.getData();
    },
    filterSpecies(filter) {
      if (filter.length == 0) return (this.hasMbwz = true);
      let filters = [];
      filter.forEach((item) => {
        if (item.id == "date") {
          filters.push(["=", item.id, item.value]);
        }
        this.hasMbwz = false;
      });
      this.filters = filters;
    },
    getParams() {
      let filters = [];
      if (this.semi_annual)
        filters.push(["=", "semi_annual", this.semi_annual]);
      console.log(this.filters.length, "this.filters");

      if (this.filters.length > 0 && !this.jcd_date) {
        filters.push(...this.filters);
      }
      if (this.filters.length == 0 && this.jcd_date) {
        filters.push(["=", "date", this.jcd_date]);
      }
      if (this.filters.length > 0 && this.jcd_date) {
        filters.push(["=", "date", this.jcd_date]);
      }
      if (this.jcd_bm) filters.push(["=", "jcd_bm", this.jcd_bm]);
      if (filters.length > 0)
        filters = filters.length > 1 ? ["all", ...filters] : filters[0];
      return filters;
    },
    async getData() {
      let filters = this.getParams();
      if (!this.jcd_bm) return;
      let params = {
        returnGeometry: false,
        format: "json",
      };
      if (filters.length > 0) params.filter = filters;
      let resData = await this.$apis.feature.query(
        "plant_point_ysj_dc",
        params
      );
      let tableData = await this.$apis.feature.query(
        "plant_point_ysj_dcyd",
        params
      );
      let info = resData.length > 0 ? resData[0] : null;
      if (info) info.zpbm = info.zpbm ? info.zpbm.split("、") : [];
      this.detailInfo = info;
      this.$set(this, "detailInfo", info);
      const newtableData = tableData
        .map((item) => ({
          ...item,
          ydbm: Number(item.ydbm),
        }))
        .sort((a, b) => a.ydbm - b.ydbm);

      this.tableData = newtableData;
      this.option.xAxis.data = newtableData.map((item) => item.ydbm);
      this.option.series[0].data = newtableData.map((item) => ({
        value: item.yd_mbwz_num,
        ff_rate: item.ff_rate,
        date: item.date,
        semi_annual: item.semi_annual,
      }));
    },
    async getAnalysisData() {
      if (!this.jcd_bm && !this.jcd_date) return;
      let params = {
        aggregates: [["yd_mbwz_num", "sum"]],
        groupFields: ["date"],
        filter: ["=", "jcd_bm", this.jcd_bm],
      };
      const res = await this.$apis.feature.aggregate(
        "plant_point_ysj_dcyd",
        params
      );
      if (res) this.analysisData = res;
    },
    // 获取对比数据
    async getCompareData() {
      if (this.analysisData) {
        const deepCopyData = JSON.parse(JSON.stringify(this.analysisData));
        this.this_year_num = null;
        this.last_year_num = null;
        deepCopyData.forEach((item) => {
          if (item.date == this.jcd_date) {
            this.this_year_num = parseFloat(item.yd_mbwz_num_sum).toFixed(0);
          }
          if (item.date == this.jcd_date - 1) {
            this.last_year_num = parseFloat(item.yd_mbwz_num_sum).toFixed(0);
          }
        });
      }
      if (this.last_year_num !== null) {
        this.difference_num = parseFloat(
          this.this_year_num - this.last_year_num
        ).toFixed(0);
      }
    },

    async pointClick({ jcd_bm, date }) {
      this.jcd_bm = jcd_bm;
      this.jcd_date = date;
      this.getData();
      await this.getAnalysisData();
      this.getCompareData();
    },

    async showEchart(e) {
      if (e == 1) {
        if (this.analysisData) {
          const deepCopyData = JSON.parse(JSON.stringify(this.analysisData));
          deepCopyData.sort((a, b) => a.date - b.date);
          this.option1.xAxis.data = deepCopyData
            .filter((item) => Number(item.date) >= 2019)
            .map((item) => Number(item.date));
          this.option1.series[0].data = deepCopyData
            .filter((item) => Number(item.date) >= 2019)
            .map((item) => item.yd_mbwz_num_sum);
        }
        this.echartOptions = this.option1;
      } else {
        this.echartOptions = this.option;
      }
      this.modalVisible = true;
    },
  },
  mounted() {
    this.bus.$on("onBaseClick", this.pointClick);
    this.bus.$on("plantSelectSpecies", this.filterSpecies);
  },
  allLoaded() {
    // 定时为了等待其他组件先加载完毕
    setTimeout(async () => {
      if (Array.isArray(this.config.profile)) {
        this.profile = this.config.profile;
      }
    }, 500);
  },
};
</script>

<style lang="less" scoped>
.tabs-content {
  display: flex;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #e7ebf4;
  overflow: hidden;
  margin-bottom: 1vh;
  .tabs {
    width: 50%;
    height: 40px;
    text-align: center;
    // border: 1px solid #ccc;
    line-height: 40px;
    cursor: pointer;
  }

  .active {
    border-bottom: none;
    // color: rgb(62, 145, 247);
    color: #fff;
    background-color: #4065e0;
  }
}
.scrollbar {
  height: 100%;
  overflow-y: scroll;
  // 隐藏滚动条
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.content {
  height: calc(100% - 65px);
  display: flex;
  flex-direction: column;
  // overflow-y: scroll;
  .content_title {
    font-size: 22px;
    font-weight: bold;
    color: #333;
    margin: 10px 0;
    line-height: 1.5;
  }
  .content_font {
    font-size: 18px;
    color: #555;
    margin: 10px 0;
    line-height: 1.6;
  }
}
.time-select {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
}
.info-cell {
  padding: 10px;
  div {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
  }
}
.box {
  padding: 12px;
  overflow-x: hidden;
  overflow-y: scroll;
  .echart {
    width: 100%;
    height: 30vh;
  }
  .info {
    width: 100%;
    padding: 12px;
    font-weight: 500;
    .contain {
      width: 100%;
      border: 1px solid #f2f2f2;
      padding: 12px;
      margin-bottom: 10px;
      font-size: 16px;
      // img{
      //   width: 35%;
      // }
      // .imgs{
      //   float: left;
      //   width: 35%;
      //   img{
      //     width: 100%;
      //   }
      // }
      .item {
        height: 100%;
        // width: calc(60%);
        width: 100%;
        float: right;
        .nc-title {
          font-size: 24px;
          font-weight: 600;
        }
      }
    }
  }
}
.ant-carousel .slick-slide {
  text-align: center;
  height: 160px;
  line-height: 160px;
  background: #364d79;
  overflow: hidden;
}

.ant-carousel .custom-slick-arrow {
  width: 25px;
  height: 25px;
  font-size: 25px;
  color: #fff;
  background-color: rgba(31, 45, 61, 0.11);
  opacity: 0.3;
}
.ant-carousel .custom-slick-arrow:before {
  display: none;
}
.ant-carousel .custom-slick-arrow:hover {
  opacity: 0.5;
}

.ant-carousel .slick-slide h3 {
  color: #fff;
}

.box-container {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .showEchart {
    margin-right: 20px;
    margin-bottom: 10px;
  }
}

.contrast-box {
  display: flex;
  .contrast-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;

    justify-content: center;
    color: #002159;
    .label {
      font-size: 18px;
    }

    .num-box {
      display: flex;
      align-items: flex-end;
      .value {
        font-size: 24px;
      }
      .unit {
        margin-left: 3px;
        font-size: 16px;
      }
    }
  }
}

/deep/.ant-select-selection--single {
  border-color: #000;
}

.difference {
  margin-left: 10px;
  line-height: 0px;
}

.no-data {
  font-size: 20px;
  color: #000;
  padding: 10px;
  margin-bottom: 5px;
}

.title-box {
  display: flex;
}
</style>
