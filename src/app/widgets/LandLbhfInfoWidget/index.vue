<template>
  <div class="jimu-widget jimu-tool-widget box">
    <TitleBar :title="`${adminName}土壤耕地质量类别划分面积`"></TitleBar>
    <div class="allArea cleardiv">
      <div class="lb">
        <p>优先保护</p>
        <p class="num yxbh">
          <!-- {{ formatAreaData(allAreaAqly).value}} -->
          {{
            this.$utils.number.bigDataFormat(allAreaYxbh, 10000, 1, [
              "亩",
              "万亩",
            ]).value
          }}
          <span class="unit">{{
            this.$utils.number.bigDataFormat(allAreaYxbh, 10000, 1, [
              "亩",
              "万亩",
            ]).unit
          }}</span>
        </p>
      </div>
      <div class="lb">
        <p>安全利用</p>
        <p class="num aqly">
          <!-- {{ formatAreaData(allAreaAqly).value}} -->
          {{
            this.$utils.number.bigDataFormat(allAreaAqly, 10000, 1, [
              "亩",
              "万亩",
            ]).value
          }}
          <span class="unit">{{
            this.$utils.number.bigDataFormat(allAreaAqly, 10000, 1, [
              "亩",
              "万亩",
            ]).unit
          }}</span>
        </p>
      </div>
      <div class="lb">
        <p>严格管控</p>
        <p class="num yggk">
          {{
            this.$utils.number.bigDataFormat(allAreaYggk, 10000, 1, [
              "亩",
              "万亩",
            ]).value
          }}
          <span class="unit">{{
            this.$utils.number.bigDataFormat(allAreaYggk, 10000, 1, [
              "亩",
              "万亩",
            ]).unit
          }}</span>
        </p>
      </div>
    </div>
    <div class="filter">
      质量类别:
      <a-button
        type="primary"
        v-for="(item, index) in btn"
        :key="index"
        :class="[item.active ? 'checked' : '']"
        :style="{ backgroundColor: item.color, borderColor: item.color }"
        @click="filterLayer(item)"
      >
        {{ item.label }}
      </a-button>
    </div>
    <template>
      <div class="toggleTable" @click="toggleTable = !toggleTable">
        <span v-show="toggleTable" class="gt-icon gt-icon-table icon"></span>
        <span v-show="!toggleTable" class="gt-icon gt-icon-echarts icon"></span>
      </div>
      <TitleBar
        :title="`${adminName}土壤耕地质量类别划分面积统计`"
        style="margin-bottom: 30px"
      ></TitleBar>
      <div class="chart-wrap" v-show="toggleTable">
        <Chart ref="chart" :options="option" />
      </div>
      <div v-show="!toggleTable">
        <div>
          <a-table
            :columns="columns"
            :data-source="columnsData"
            @change="handleChange"
            :pagination="false"
          />
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import TitleBar from "../../Components/TitleBar";
import BaseWidget from "@/BaseWidget";
import Chart from "@/components/Chart";
export default {
  name: "LandLbhfInfoWidget",

  components: {
    TitleBar,
    Chart,
  },

  mixins: [BaseWidget],

  data() {
    return {
      adminName: "广西",
      sortedInfo: null,
      btn: [
        {
          label: "优先保护",
          value: 1,
          active: false,
          color: "rgb(0,179,0)",
          field: "land_yxbh",
        },
        {
          label: "安全利用",
          value: 2,
          active: false,
          color: "rgb(179,179,0)",
          field: "land_aqly",
        },
        {
          label: "严格管控",
          value: 3,
          active: false,
          color: "rgb(179,0,0)",
          field: "land_yggk",
        },
      ],
      level: ["province", "city", "county", "town", "village", "village"],
      code: ["PXZQDM", "CXZQDM", "FXZQDM", "TXZQDM", "XZQDM", "XZQDM"],
      name: ["PXZQMC", "CXZQMC", "FXZQMC", "TXZQMC", "XZQMC", "XZQMC"],
      start: {
        level: 0,
        code: 45,
      },
      tableData: [],
      tableDataNameLevel: 0,
      allAreaYxbh: "",
      allAreaAqly: "",
      allAreaYggk: "",
      toggleTable: true,
      active: true,
      columnsData: [],
      levelNum: "",
    };
  },
  methods: {
    handleChange(pagination, filters, sorter) {
      // this.filteredInfo = filters;
      this.sortedInfo = sorter;
    },
    filterLayer(item) {
      if (item) {
        item.active = !item.active;
        let layerFilter = ["all"];
        this.btn.forEach((item) => {
          if (!item.active) return;
          layerFilter.push([
            "!=",
            ["get", "zh_class"],
            item.value ? item.value : 1,
          ]);
        });
        layerFilter =
          layerFilter.length == 1
            ? null
            : layerFilter.length == 2
            ? layerFilter[1]
            : layerFilter;
        this.setModuleLayerFilter("land_nyd_lbhf", "zh_class", layerFilter);
        this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "land_nyd_lbhf");
      }
      let thisType = this.btn.filter((item) => {
        return !item.active;
      });
      // 根据选中标签排序
      this.tableData = this.tableData.sort((a, b) => {
        let a_sum = 0;
        let b_sum = 0;
        thisType.forEach((item) => {
          a_sum += Number(a[item.field]);
          b_sum += Number(b[item.field]);
        });
        return a_sum - b_sum;
      });

      setTimeout(() => {
        this.btn.forEach((item) => {
          this.$refs.chart.chart.dispatchAction({
            type: item.active ? "legendUnSelect" : "legendSelect",
            // 图例名称
            name: item.label,
          });
        });
      }, 100);
    },
    async reset(value) {
      if (!value) {
        this.adminName = "广西";
        this.getData(this.start);
        this.allArea(this.start);
      }
    },
    async getData(value) {
      if (!value) return;
      if (!value.name) this.adminName = "广西";
      else this.adminName = value.name;
      this.levelNum = value.level;
      if (!value) this.getData(this.start);
      this.tableDataNameLevel = value.level + 1;
      this.columnsData = [];

      let filter = [
        "all",
        ["=", this.code[value.level], value.code],
        ["=", "date", "2020"],
      ];
      let param = {
        returnGeometry: false,
        format: "json",
        orderField: "land_num",
        orderType: "DESC",
        filter,
      };
      let res = await this.$apis.feature.query(
        `land_plough_${this.level[value.level + 1]}`,
        param
      );
      if (value.level == 0)
        this.tableData = res.filter((item) => {
          return parseInt(item.CXZQDM) != 4500;
        });
      else this.tableData = res;

      this.tableData.forEach((item) => {
        this.$set(
          item,
          "sum",
          parseInt(item.land_aqly) + parseInt(item.land_yggk)
        );
      });

      var key = 0;
      if (this.levelNum > 1) {
        this.tableData.forEach((item) => {
          key++;
          let tableObj = {};
          this.$set(tableObj, "MC", item[this.name[this.tableDataNameLevel]]);
          this.$set(
            tableObj,
            "land_yxbh",
            parseFloat(parseFloat(item.land_yxbh).toFixed(1))
          );
          this.$set(
            tableObj,
            "land_aqly",
            parseFloat(parseFloat(item.land_aqly).toFixed(1))
          );
          this.$set(
            tableObj,
            "land_yggk",
            parseFloat(parseFloat(item.land_yggk).toFixed(1))
          );
          this.$set(
            tableObj,
            "land_all",
            parseFloat(parseFloat(item.land_aqly).toFixed(1)) +
              parseFloat(parseFloat(item.land_yggk).toFixed(1))
          );
          this.$set(tableObj, "key", key);
          this.columnsData.push(tableObj);
        });
      } else if (this.levelNum <= 1) {
        this.tableData.forEach((item) => {
          key++;
          let tableObj = {};
          this.$set(tableObj, "MC", item[this.name[this.tableDataNameLevel]]);
          this.$set(
            tableObj,
            "land_yxbh",
            parseFloat(parseFloat(item.land_yxbh / 1000).toFixed(1))
          );
          this.$set(
            tableObj,
            "land_aqly",
            parseFloat(parseFloat(item.land_aqly / 10000).toFixed(1))
          );
          this.$set(
            tableObj,
            "land_yggk",
            parseFloat(parseFloat(item.land_yggk / 10000).toFixed(1))
          );
          this.$set(
            tableObj,
            "land_all",
            parseFloat(parseFloat(item.land_aqly / 10000).toFixed(1)) +
              parseFloat(parseFloat(item.land_yggk / 10000).toFixed(1))
          );
          this.$set(tableObj, "key", key);
          this.columnsData.push(tableObj);
        });
      }

      this.columnsData.sort((a, b) => {
        return b.land_all - a.land_all;
      });

      this.tableData.sort((a, b) => {
        return a.sum - b.sum;
      });
      this.filterLayer();
    },
    async allArea(value) {
      let filter = [
        "all",
        ["=", this.code[value.level], value.code],
        ["=", "date", "2020"],
      ];
      let param = {
        returnGeometry: false,
        format: "json",
        filter,
      };
      let res = await this.$apis.feature.query(
        `land_plough_${this.level[value.level]}`,
        param
      );
      this.allAreaYxbh = res[0].land_yxbh;
      this.allAreaAqly = res[0].land_aqly;
      this.allAreaYggk = res[0].land_yggk;
    },
  },
  computed: {
    option() {
      return {
        legend: {
          show: false,
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        grid: {
          left: "1%",
          right: "12%",
          bottom: "3%",
          top: "5%",
          containLabel: true,
        },
        xAxis: {
          name: this.levelNum > 1 ? "亩" : "万亩",
          type: "value",
          position: "top",
          axisLabel: {
            formatter: "{value}",
          },
        },
        yAxis: {
          type: "category",
          data: this.tableData.map((item) => {
            return item[this.name[this.tableDataNameLevel]];
          }),
          axisLabel: {
            show: true,
            textStyle: {
              fontSize: 15, //更改坐标轴文字大小
            },
          },
          formatter: function (params) {
            return params.slice(0, 3) + "...";
          },
        },
        series: [
          {
            name: "优先保护",
            type: "bar",
            stack: "total",
            data:
              this.levelNum > 1
                ? this.tableData.map((item) => {
                    return parseFloat(item.land_yxbh).toFixed(1);
                  })
                : this.tableData.map((item) => {
                    return parseFloat(item.land_yxbh / 10000).toFixed(1);
                  }),
            color: "rgb(0,179,0)",
            barWidth: 15,
          },
          {
            name: "安全利用",
            type: "bar",
            stack: "total",
            data:
              this.levelNum > 1
                ? this.tableData.map((item) => {
                    return parseFloat(item.land_aqly).toFixed(1);
                  })
                : this.tableData.map((item) => {
                    return parseFloat(item.land_aqly / 10000).toFixed(1);
                  }),
            color: "rgb(179,179,0)",
            barWidth: 15,
          },
          {
            name: "严格管控",
            type: "bar",
            stack: "total",
            data:
              this.levelNum <= 1
                ? this.tableData.map((item) => {
                    return parseFloat(item.land_yggk / 10000).toFixed(1);
                  })
                : this.tableData.map((item) => {
                    return parseFloat(item.land_yggk).toFixed(1);
                  }),
            color: "rgb(179, 0, 0)",
            barWidth: 15,
          },
        ],
      };
    },
    columns() {
      let { sortedInfo } = this;
      sortedInfo = sortedInfo || {};
      const columnsData = [
        {
          title: "区划",
          dataIndex: "MC",
          key: "MC",
          width: "25%",
        },
        {
          title: "优先保护(万亩)",
          dataIndex: "land_yxbh",
          key: "land_yxbh",
          sorter: (a, b) => a.land_yxbh - b.land_yxbh,
          sortOrder: sortedInfo.columnKey === "land_yxbh" && sortedInfo.order,
          width: "25%",
        },
        {
          title: "安全利用(万亩)",
          dataIndex: "land_aqly",
          key: "land_aqly",
          sorter: (a, b) => a.land_aqly - b.land_aqly,
          sortOrder: sortedInfo.columnKey === "land_aqly" && sortedInfo.order,
          width: "25%",
        },
        {
          title: "严格管控(万亩)",
          dataIndex: "land_yggk",
          key: "land_yggk",
          sorter: (a, b) => a.land_yggk - b.land_yggk,
          sortOrder: sortedInfo.columnKey === "land_yggk" && sortedInfo.order,
          width: "25%",
        },
      ];

      if (this.levelNum > 1) {
        columnsData[1].title = "优先保护(亩)";
        columnsData[2].title = "安全利用(亩)";
        columnsData[3].title = "严格管控(亩)";
      } else if (this.levelNum > 0 && this.levelNum <= 2) {
        columnsData[1].title = "优先保护(万亩)";
        columnsData[2].title = "安全利用(万亩)";
        columnsData[3].title = "严格管控(万亩)";
      }

      return columnsData;
    },
  },
  mounted() {
    console.log(BaseWidget);
    this.allArea({ level: 0, code: 45 });
    this.getData(this.start);

    this.bus.$on(this.$events.admin.CHANGE_ADMIN, this.reset);
    this.bus.$on(this.$events.admin.CHANGE_ADMIN, this.getData);
    this.bus.$on(this.$events.admin.CHANGE_ADMIN, this.allArea);
    this.setModuleLayerFilter("nyd_cdhj_lbhf", "base-stylet", [
      "!=",
      ["get", "class"],
      "其他",
    ]);
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";
.box {
  font-size: 21px;
  padding: 14px;
  overflow-y: auto;
  overflow-x: hidden;
  .allArea {
    width: 100%;
    padding: 8px;
    background-color: rgb(242, 242, 242);
    margin-bottom: 20px;
    .lb {
      float: left;
      width: 33%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      font-size: 20px;
      font-weight: 500;
      p {
        margin-bottom: 5px;
      }
      .num {
        font-size: 27px;
        font-weight: 550;
      }
      .unit {
        font-size: 17px;
        color: rgb(50, 50, 50);
      }
      .yxbh {
        color: rgb(0, 179, 0);
      }
      .aqly {
        color: rgb(181, 176, 57);
      }
      .yggk {
        color: rgb(164, 41, 22);
      }
    }
  }
  .filter {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    cursor: pointer;
    .checked {
      background: gray !important;
      border-color: gray !important;
      color: white;
    }
  }
  .toggleTable {
    z-index: 1;
    cursor: pointer;
    height: 25px;
    width: 25px;
    position: absolute;
    right: 25px;
    transform: translateY(-7px);
    .icon {
      font-size: 20px;
      font-weight: 800;
    }
  }
  .tableData {
    height: calc(70% - 12px);
    overflow-y: auto;
    overflow-x: hidden;
  }
  .chart-wrap {
    width: 100%;
    height: 70%;
  }
}
td,
th {
  text-align: center;
}
::-webkit-scrollbar {
  display: none; /* Chrome Safari */
}
</style>
