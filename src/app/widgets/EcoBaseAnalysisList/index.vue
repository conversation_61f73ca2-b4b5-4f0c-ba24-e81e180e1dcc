<template>
  <div class="jimu-widget jimu-tool-widget box">
    <div class="title">数据统计</div>
    <div class="item">
      <TitleBar title="市级统计" />
      <a-table
        :columns="cTableColumn"
        :data-source="cTableData"
        :pagination="false"
        :scroll="{ y: viewportHeight * 33.5 }"
      ></a-table>
    </div>
    <div class="item">
      <TitleBar title="县级统计" />
      <a-table
        :columns="fTableColumn"
        :data-source="fTableData"
        :pagination="false"
        :scroll="{ y: viewportHeight * 33.5 }"
      ></a-table>
    </div>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
import TitleBar from "@/app/Components/TitleBar";
export default {
  name: "EcoAnalysisList",
  mixins: [BaseWidget],
  components: {
    TitleBar,
  },
  data() {
    return {
      cTableColumn: [
        {
          title: "市名称",
          dataIndex: "CXZQMC",
          key: "CXZQMC",

          align: "center",
        },
        {
          title: "示范基地数量（个）",
          dataIndex: "_id_count",
          key: "_id_count",

          align: "center",
          sorter: (a, b) => a._id_count - b._id_count,
        },
      ],
      fTableColumn: [
        {
          title: "市名称",
          dataIndex: "CXZQMC",
          key: "CXZQMC",
          align: "center",
        },
        {
          title: "县名称",
          dataIndex: "FXZQMC",
          key: "FXZQMC",
          align: "center",
        },
        {
          title: "示范基地数量（个）",
          dataIndex: "_id_count",
          key: "_id_count",
          align: "center",

          sorter: (a, b) => a._id_count - b._id_count,
        },
      ],
      region: "city",
      adminFilter: null,
      regionParam: {
        city: "CXZQDM",
        county: "FXZQDM",
        town: "TXZQDM",
        village: "XZQDM",
      },
      filters: [],
      cTableData: [],
      fTableData: [],
    };
  },
  computed: {
    viewportHeight() {
      return window.innerHeight / 100; // 获取1vh对应的像素数
    },
  },
  mounted() {
    this.bus.$on("setAnalysisFilterCount", this.getFilter);
    this.bus.$on(
      this.$events.admin.CHANGE_ADMIN,
      this.adminFilterUpdateHandler
    );
    this.getCTableData();
    this.getFTableData();
  },
  methods: {
    adminFilterUpdateHandler(e) {
      if (e) {
        this.adminFilter = ["=", this.regionParam[e.region], e.code];
        this.region = e.region;
      } else {
        this.adminFilter = null;
        this.region = "city";
      }
      this.getCTableData();
      this.getFTableData();
    },
    getFilter(value) {
      this.filters = value;
      this.getCTableData();
      this.getFTableData();
    },
    async getCTableData() {
      let filter = [];
      if (this.adminFilter !== null) {
        let code = this.adminFilter[2];
        if (code.length == 6) {
          code = code.slice(0, 4);
        }
        filter.push(["=", "CXZQDM", code]);
      }
      if (this.filters.length > 0) {
        filter.push(...this.filters);
      }
      if (filter?.length === 0) {
        filter = null;
      }
      if (filter?.length > 1) {
        filter.unshift("all");
      }
      if (filter?.length == 1) {
        filter = filter[0];
      }
      let params = {
        returnGeometry: false,
        format: "json",
        groupFields: ["CXZQMC"],
        aggregates: [["_id", "count"]],
        filter,
      };

      let data = await this.$apis.feature.aggregate("base_gx", params);
      data.sort((a, b) => b._id_count - a._id_count);
      this.cTableData = data;
    },
    async getFTableData() {
      let filter = [];

      if (this.adminFilter !== null) {
        filter.push(this.adminFilter);
      }
      if (this.filters.length > 0) {
        filter.push(...this.filters);
      }
      if (filter?.length === 0) {
        filter = null;
      }
      if (filter?.length > 1) {
        filter.unshift("all");
      }
      if (filter?.length == 1) {
        filter = filter[0];
      }
      let params = {
        returnGeometry: false,
        format: "json",
        groupFields: ["CXZQMC", "FXZQMC"],
        aggregates: [["_id", "count"]],
        filter,
      };
      let data = await this.$apis.feature.aggregate("base_gx", params);
      data.sort((a, b) => b._id_count - a._id_count);
      this.fTableData = data;
    },
  },
};
</script>

<style lang="less" scoped>
.box {
  padding: 0px;
  height: 100%;
  display: flex;
  flex-direction: column;
  .title {
    text-align: center;
    font-size: 24px;
    color: #fff;
    padding: 10px 20px;
    background-color: #45a6ea;
  }

  .item {
    flex: 1;
    padding: 12px;
  }
}
</style>
