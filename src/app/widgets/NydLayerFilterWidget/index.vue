<template>
  <div v-show="show" class="jimu-widget">
    <a-select
      class="select-wrap"
      v-model="checkedTz"
      @change="tzNcpChangeHandler"
    >
      <template v-for="(item, index) in tzncpmc">
        <a-select-option :value="item.type" :key="index">
          {{ item.name }}
        </a-select-option>
      </template>
    </a-select>
    <a-select
      class="select-wrap"
      v-model="checkedJsfa"
      @change="jsfaChangeHandler"
      style="margin-left: 20px"
    >
      <template v-for="(item, index) in jsfa">
        <a-select-option :value="item.type" :key="index">
          {{ item.name }}
        </a-select-option>
      </template>
    </a-select>
  </div>
</template>

<script>
import BaseWidget from '@/BaseWidget'
export default {
  data() {
    return {
      show: false,
      tzncpmc: [
        {
          name: '全部农产品',
          type: ''
        },
        {
          name: '谷物',
          type: 'GW'
        },
        {
          name: '豆类蔬菜',
          type: 'DLSC'
        },
        {
          name: '豆类',
          type: 'DL'
        },
        {
          name: '茶叶',
          type: 'CY'
        },
        {
          name: '坚果及籽类',
          type: 'JG'
        },
        {
          name: '块根蔬菜',
          type: 'KGSC'
        },
        {
          name: '玉米',
          type: 'YM'
        },
        {
          name: '叶菜类',
          type: 'YCL'
        },
        {
          name: '水稻',
          type: 'SD'
        },
        {
          name: '大米',
          type: 'DM'
        },
        {
          name: '水果',
          type: 'SG'
        },
        {
          name: '稻谷',
          type: 'DG'
        },
        {
          name: '新鲜蔬菜',
          type: 'XXSC'
        }
      ],
      jsfa: [
        {
          name: '技术方案',
          type: ''
        },
        {
          name: '石灰调节',
          type: 'shtj'
        },
        {
          name: '优化施肥',
          type: 'yhsf'
        },
        {
          name: '品种(结构)调整',
          type: 'pztz'
        },
        {
          name: '水分调控',
          type: 'sftk'
        },
        {
          name: '叶面调控',
          type: 'ymtk'
        },
        {
          name: '轮作休耕',
          type: 'lzxg'
        },
        {
          name: '休耕',
          type: 'xg'
        },
        {
          name: '其他',
          type: 'qt'
        }
      ],
      checkedJsfa: '',
      checkedTz: ''
    }
  },
  mounted() {
    this.bus.$on(this.$events.map.LAYER_SHOW, this.layerShowHandler)
    this.bus.$on(this.$events.map.LAYER_HIDE, this.layerHideHandler)
  },
  mixins: [BaseWidget],
  methods: {
    jsfaChangeHandler(e) {
      if (!e) {
        this.setModuleLayerFilter('nyd_tz_layer', 'jsfa', null)
        this.bus.$emit(this.$events.map.SET_LAYER_FILTER, 'nyd_tz_layer')
      } else {
        let filter = ['==', ['get', e], '1']
        this.setModuleLayerFilter('nyd_tz_layer', 'jsfa', filter)
        this.bus.$emit(this.$events.map.SET_LAYER_FILTER, 'nyd_tz_layer')
      }
    },
    tzNcpChangeHandler(e) {
      if (!e) {
        this.setModuleLayerFilter('nyd_tz_layer', 'ncplx', null)
        this.bus.$emit(this.$events.map.SET_LAYER_FILTER, 'nyd_tz_layer')
      } else {
        let filter = ['==', ['get', 'ncplx'], e]
        this.setModuleLayerFilter('nyd_tz_layer', 'ncplx', filter)
        this.bus.$emit(this.$events.map.SET_LAYER_FILTER, 'nyd_tz_layer')
      }
    },
    layerShowHandler(e) {
      let layer = e.find(item => {
        return item.key == 'nyd_tz_layer'
      })
      if (layer) {
        this.show = true
      }
    },
    layerHideHandler(e) {
      let layer = e.find(item => {
        return item.key == 'nyd_tz_layer'
      })
      if (layer) {
        this.show = false
      }
    }
  }
}
</script>

<style lang="less" scoped>
.select-wrap {
  background: #ffffff;
  border-radius: 4px;
  padding: 3px;
  box-shadow: 2px 2px 3px rgb(0 0 0 / 20%);
}
/deep/.ant-select-selection--single {
  border: none;
}
/deep/.ant-select {
  min-width: 120px;
}
</style>