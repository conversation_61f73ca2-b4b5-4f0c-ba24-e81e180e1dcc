<template>
  <div class="jimu-widget wrap" v-if="isLoad">
    <template v-for="(item, index) in config">
      <template v-if="item.type == 'cascaderFilter'">
        <div :key="index" class="select-item">
          <a-cascader
            :allowClear="false"
            :options="item.list"
            :placeholder="item.placeholder ? item.placeholder : '请选择'"
            @change="onChange"
            v-model="item.value"
          />
        </div>
      </template>
      <template v-else-if="item.type == 'cascaderField'">
        <div :key="index" class="select-item">
          <a-cascader
            :allowClear="false"
            :options="item.list"
            :placeholder="item.placeholder ? item.placeholder : '请选择'"
            @change="onChange"
            v-model="item.value"
            change-on-select
          />
        </div>
      </template>
      <template v-else>
        <div :key="index" class="select-item">
          <a-select
            v-model="item.value"
            @change="onChange"
            :placeholder="item.placeholder ? item.placeholder : '请选择'"
          >
            <template v-for="(i, n) in item.list">
              <a-select-option :key="n" :value="i.value">
                {{ i.label }}
              </a-select-option>
            </template>
          </a-select>
        </div>
      </template>
    </template>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
export default {
  name: "nDefaultRegionMapFilterWidget",
  mixins: [BaseWidget],
  data() {
    return {
      isLoad: false,
    };
  },
  mounted() {
    this.init();
  },
  configLoaded() {
    this.isLoad = true;
    setTimeout(() => {
      this.onChange();
    }, 500);
  },

  methods: {
    async init() {
      let res = await this.$apis.feature.query("species_list_2020", {
        format: "json",
      });
      this.list = res;
      this.config[0].list.forEach((item) => {
        res.forEach((i) => {
          if (item.value === i.wzlx) {
            item.children.push({ label: i.species_name, value: i.species_bm });
          }
        });
      });
    },
    async onChange() {
      let fieldObj = this.config.find((item) => {
        return item.type == "field" || item.type == "cascaderField";
      });

      const wzBm =
        fieldObj.type == "cascaderField"
          ? fieldObj.value[fieldObj.value.length - 1]
          : fieldObj.value;

      let filters = [];
      let obj = {};
      this.bus.$emit("setPCTableType", fieldObj.value[0]);

      let filterTables = [
        {
          table: "species_yd_wz_2020",
          field: "species_bm",
          mainTableField: "ydid",
          mainTable: "species_yd_2020",
        },
        {
          table: "species_tcd_wz_2020",
          field: "species_bm",
          mainTableField: "tcd_id",
          mainTable: "species_tcd_2020",
        },
      ];

      for (let i = 0; i < filterTables.length; i++) {
        const f = filterTables[i];

        const dwQuery = wzBm !== "全部" ? ["=", f.field, wzBm] : null;
        let res = await this.$apis.feature.query(f.table, {
          format: "json",
          filter: dwQuery,
        });

        const arr = [...new Set(res.map((item) => item[f.mainTableField]))];
        let filter = ["in", ["get", f.mainTableField], ["literal", arr]];
        const tableFilter = ["in", f.mainTableField, arr];

        if (wzBm === "全部") {
          this.setModuleLayerFilter(f.mainTable, "wz", null);
          this.bus.$emit(this.$events.map.SET_LAYER_FILTER, f.mainTable);
          obj[f.mainTable] = null;
          this.bus.$emit("selectPCspeciesNull");
        } else {
          obj[f.mainTable] = tableFilter;
          this.setModuleLayerFilter(f.mainTable, "wz", filter);
          this.bus.$emit(this.$events.map.SET_LAYER_FILTER, f.mainTable);
        }
      }
      this.bus.$emit("selectPCspecies", wzBm);
      this.bus.$emit("setPCTableFilter", obj);

      let bmFilter = ["=", "species_bm", wzBm];
      let lxFilter = ["=", "wzlx", fieldObj.value[0]];
      if (wzBm === "全部") {
        filters = [lxFilter];
      } else if (wzBm == "重点管理物种" || wzBm == "非重点管理物种") {
        filters = [lxFilter];
      } else {
        filters = [bmFilter, lxFilter];
      }

      this.bus.$emit("RegionCHART_UPDATE", {
        field: "occurrence_area",
        filters,
        type: "cascaderField",
        name: wzBm,
      });

      // 只要修改选项都恢复原始图层
      this.map.setPaintProperty(
        "land_city_fill",
        "fill-color",
        "rgba(255,255,255,1)"
      );
      this.map.setPaintProperty(
        "land_city_fill",
        "fill-outline-color",
        "rgba(175,135,255,1)"
      );
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";
.wrap {
  display: flex;

  .select-item {
    background: #ffffff;
    border-radius: 4px;
    padding: 3px;
    box-shadow: 2px 2px 3px rgb(0 0 0 / 20%);
    margin-right: 12px;
  }
  /deep/.ant-select-selection--single {
    border: none;
  }
  /deep/.ant-cascader-picker {
    min-width: 200px;
  }
  /deep/.ant-cascader-input {
    border: none;
  }
  /deep/.ant-select {
    min-width: 120px;
  }
}
</style>
