<template>
  <div class="jimu-widget">
    <div class="select-wrap" v-show="isTz == 0">
      <a-select v-model="checkedTz" @change="tzNcpChangeHandler">
        <template v-for="(item, index) in tzncpmc">
          <a-select-option :value="item.type" :key="index">
            {{ item.name }}
          </a-select-option>
        </template>
      </a-select>

      <a-select
        v-model="checkedJsfa"
        @change="jsfaChangeHandler"
        style="margin-left: 20px"
      >
        <template v-for="(item, index) in jsfa">
          <a-select-option :value="item.type" :key="index">
            {{ item.name }}
          </a-select-option>
        </template>
      </a-select>
    </div>
    <div class="select-wrap" v-show="isTz == 1">
      <a-select v-model="checkedNcp" @change="ncpChangeHandler">
        <template v-for="(item, index) in ncpmc">
          <a-select-option :value="item.type" :key="index">
            {{ item.name }}
          </a-select-option>
        </template>
      </a-select>
    </div>
    <div v-show="isTz == 2"></div>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget.js";

export default {
  name: "DropListWidget",

  mixins: [BaseWidget],

  data() {
    return {
      isTz: 2,
      tzncpmc: [
        {
          name: "全部农产品",
          type: "",
        },
        {
          name: "谷物",
          type: "GW",
        },
        {
          name: "豆类蔬菜",
          type: "DLSC",
        },
        {
          name: "豆类",
          type: "DL",
        },
        {
          name: "茶叶",
          type: "CY",
        },
        {
          name: "坚果及籽类",
          type: "JG",
        },
        {
          name: "块根蔬菜",
          type: "KGSC",
        },
        {
          name: "玉米",
          type: "YM",
        },
        {
          name: "叶菜类",
          type: "YCL",
        },
        {
          name: "水稻",
          type: "SD",
        },
        {
          name: "大米",
          type: "DM",
        },
        {
          name: "水果",
          type: "SG",
        },
        {
          name: "稻谷",
          type: "DG",
        },
        {
          name: "新鲜蔬菜",
          type: "XXSC",
        },
      ],
      ncpmc: [
        {
          name: "全部农产品",
          type: "",
        },
        {
          name: "小麦",
          type: "XM",
        },
        {
          name: "水稻",
          type: "SD",
        },
        {
          name: "叶菜类",
          type: "YCL",
        },
        {
          name: "水果",
          type: "SG",
        },
        {
          name: "玉米",
          type: "YM",
        },
        {
          name: "茶叶",
          type: "CY",
        },
        {
          name: "茄果类",
          type: "QGL",
        },
        {
          name: "糖类",
          type: "TL",
        },
        {
          name: "油料",
          type: "YL",
        },
        {
          name: "根茎类",
          type: "GJL",
        },
        {
          name: "大豆",
          type: "DD",
        },
        {
          name: "豆类",
          type: "DL",
        },
        {
          name: "稻谷",
          type: "DG",
        },
        {
          name: "豆类蔬菜",
          type: "DLSC",
        },
        {
          name: "块根蔬菜",
          type: "KGSC",
        },
        {
          name: "果汁",
          type: "GZ",
        },
        {
          name: "新鲜蔬菜",
          type: "XXSC",
        },
        {
          name: "花生",
          type: "HS",
        },
      ],
      jsfa: [
        {
          name: "技术方案",
          type: "",
        },
        {
          name: "石灰调节",
          type: "shtj",
        },
        {
          name: "优化施肥",
          type: "yhsf",
        },
        {
          name: "品种(结构)调整",
          type: "pztz",
        },
        {
          name: "水分调控",
          type: "sftk",
        },
        {
          name: "叶面调控",
          type: "ymtk",
        },
        {
          name: "轮作休耕",
          type: "lzxg",
        },
        {
          name: "休耕",
          type: "xg",
        },
        {
          name: "其他",
          type: "qt",
        },
      ],
      checkedTz: "",
      checkedNcp: "",
      checkedJsfa: "",
    };
  },
  methods: {
    ncpChangeHandler(e) {
      if (!e) {
        this.setModuleLayerFilter(
          "guangxi_2020naqlyncpjcd_name",
          "ncplx",
          null
        );
        this.bus.$emit(
          this.$events.map.SET_LAYER_FILTER,
          "guangxi_2020naqlyncpjcd_name"
        );
        this.bus.$emit(`setTableFilter_guangxi_2020naqlyncpjcd_name`, {
          layer: "guangxi_2020naqlyncpjcd_name",
          key: "ncplx",
          filter: null,
        });

        this.setModuleLayerFilter("guangxi_2017njcncpd", "ncplx", null);
        this.bus.$emit(
          this.$events.map.SET_LAYER_FILTER,
          "guangxi_2017njcncpd"
        );
        this.setModuleLayerFilter("guangxi_2018njcncpd", "ncplx", null);
        this.bus.$emit(
          this.$events.map.SET_LAYER_FILTER,
          "guangxi_2018njcncpd"
        );
        this.setModuleLayerFilter("guangxi_2019njcncpd", "ncplx", null);
        this.bus.$emit(
          this.$events.map.SET_LAYER_FILTER,
          "guangxi_2019njcncpd"
        );
        this.setModuleLayerFilter("guangxi_2020njcncpd", "ncplx", null);
        this.bus.$emit(
          this.$events.map.SET_LAYER_FILTER,
          "guangxi_2020njcncpd"
        );
        this.setModuleLayerFilter("guangxi_2021njcncpd", "ncplx", null);
        this.bus.$emit(
          this.$events.map.SET_LAYER_FILTER,
          "guangxi_2021njcncpd"
        );
        this.setModuleLayerFilter("guangxi_2022njcncpd", "ncplx", null);
        this.bus.$emit(
          this.$events.map.SET_LAYER_FILTER,
          "guangxi_2022njcncpd"
        );

        this.bus.$emit(`setTableFilter_land_lnjcd_ncp`, {
          layer: "land_lnjcd_ncp",
          key: "ncplx",
          filter: null,
        });
      } else {
        let filter = ["==", ["get", "ncplx"], e];
        let tableFilter = ["=", "ncplx", e];
        this.bus.$emit("setTableFilter_guangxi_2020naqlyncpjcd_name", {
          layer: "guangxi_2020naqlyncpjcd_name",
          key: "ncplx",
          tableFilter,
        });
        this.setModuleLayerFilter(
          "guangxi_2020naqlyncpjcd_name",
          "ncplx",
          filter
        );
        this.bus.$emit(
          this.$events.map.SET_LAYER_FILTER,
          "guangxi_2020naqlyncpjcd_name"
        );

        this.bus.$emit("setTableFilter_land_lnjcd_ncp", {
          layer: "land_lnjcd_ncp",
          key: "ncplx",
          tableFilter,
        });
        this.setModuleLayerFilter("guangxi_2017njcncpd", "ncplx", filter);
        this.bus.$emit(
          this.$events.map.SET_LAYER_FILTER,
          "guangxi_2017njcncpd"
        );
        this.setModuleLayerFilter("guangxi_2018njcncpd", "ncplx", filter);
        this.bus.$emit(
          this.$events.map.SET_LAYER_FILTER,
          "guangxi_2018njcncpd"
        );
        this.setModuleLayerFilter("guangxi_2019njcncpd", "ncplx", filter);
        this.bus.$emit(
          this.$events.map.SET_LAYER_FILTER,
          "guangxi_2019njcncpd"
        );
        this.setModuleLayerFilter("guangxi_2020njcncpd", "ncplx", filter);
        this.bus.$emit(
          this.$events.map.SET_LAYER_FILTER,
          "guangxi_2020njcncpd"
        );
        this.setModuleLayerFilter("guangxi_2021njcncpd", "ncplx", filter);
        this.bus.$emit(
          this.$events.map.SET_LAYER_FILTER,
          "guangxi_2021njcncpd"
        );
        this.setModuleLayerFilter("guangxi_2022njcncpd", "ncplx", filter);
        this.bus.$emit(
          this.$events.map.SET_LAYER_FILTER,
          "guangxi_2022njcncpd"
        );
      }
    },
    tzNcpChangeHandler(e) {
      if (!e) {
        this.setModuleLayerFilter("guangxi_2020ndksstz_name", "ncplx", null);
        this.bus.$emit(
          this.$events.map.SET_LAYER_FILTER,
          "guangxi_2020ndksstz_name"
        );
        this.bus.$emit("setTableFilter_guangxi_2020ndksstz_name", {
          layer: "guangxi_2020ndksstz_name",
          key: "ncplx",
          filter: null,
        });
      } else {
        let filter = ["==", ["get", "ncplx"], e];
        let tableFilter = ["like", "ncplx", e];
        this.bus.$emit("setTableFilter_guangxi_2020ndksstz_name", {
          layer: "guangxi_2020ndksstz_name",
          key: "ncplx",
          tableFilter,
        });
        this.setModuleLayerFilter("guangxi_2020ndksstz_name", "ncplx", filter);
        this.bus.$emit(
          this.$events.map.SET_LAYER_FILTER,
          "guangxi_2020ndksstz_name"
        );
      }
    },
    jsfaChangeHandler(e) {
      if (!e) {
        this.setModuleLayerFilter("guangxi_2020ndksstz_name", "jsfa", null);
        this.bus.$emit(
          this.$events.map.SET_LAYER_FILTER,
          "guangxi_2020ndksstz_name"
        );
        this.bus.$emit("setTableFilter_guangxi_2020ndksstz_name", {
          layer: "guangxi_2020ndksstz_name",
          key: "jsfa",
          filter: null,
        });
      } else {
        let filter = ["==", ["get", e], "1"];
        let tableFilter = ["=", e, "1"];
        this.bus.$emit("setTableFilter_guangxi_2020ndksstz_name", {
          layer: "guangxi_2020ndksstz_name",
          key: "jsfa",
          tableFilter,
        });
        this.setModuleLayerFilter("guangxi_2020ndksstz_name", "jsfa", filter);
        this.bus.$emit(
          this.$events.map.SET_LAYER_FILTER,
          "guangxi_2020ndksstz_name"
        );
      }
    },
  },
  mounted() {
    this.bus.$on("checkTZ", (e) => {
      this.isTz = e;
      console.log(e);
    });
  },
};
</script>

<style lang="less" scoped>
.select-wrap {
  background: #ffffff;
  border-radius: 4px;
  padding: 3px;
  box-shadow: 2px 2px 3px rgb(0 0 0 / 20%);
}
/deep/.ant-select-selection--single {
  border: none;
}
/deep/.ant-select {
  min-width: 120px;
}
</style>
