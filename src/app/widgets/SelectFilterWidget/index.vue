<template>
	<div class="jimu-widget wrap">
		<a-select class="select-widget" placeholder="请选择要筛选的年份" @change="handleChange" default-value="全部">
			<a-select-option v-for="item in config.options" :key="item.value" :value="item.value">
				{{ item.label }}
			</a-select-option>
		</a-select>
	</div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
export default {
	name: "DateFilterWidget",
	mixins: [BaseWidget],
	data() {
		return {};
	},
	configLoaded() {
		this.setModuleLayerFilter(this.config.layer, this.config.field);
		this.bus.$emit(this.$events.map.SET_LAYER_FILTER, this.config.layer);
		this.bus.$emit("stncDate", "全部");
	},
	mounted() {},
	methods: {
		handleChange(value) {
			if (value !== "全部") {
				this.setModuleLayerFilter(this.config.layer, this.config.field, ["==", ["get", this.config.field], value]);
			} else {
				this.setModuleLayerFilter(this.config.layer, this.config.field);
			}
			this.bus.$emit(this.$events.map.SET_LAYER_FILTER, this.config.layer);
			this.bus.$emit("stncDate", value);
		},
	},
};
</script>

<style lang="less" scoped>
.wrap {
	display: flex;
}
.select-widget {
	width: 100%;
	display: flex;
	position: relative;
	align-items: center;
	padding: 7px 14px;
	background: #fff;
	border-radius: 4px;
	border: 1px solid #e7ebf4;
	height: 40px;
	box-shadow: 2px 2px 3px rgba(0, 0, 0, 0.2);
	font-size: 17px;
}
</style>
<style>
.ant-select-selection {
	border: none;
	width: 100%;
}
.ant-select-focused .ant-select-selection,
.ant-select-selection:focus,
.ant-select-selection:active {
	border: none;
	box-shadow: none;
}
</style>
