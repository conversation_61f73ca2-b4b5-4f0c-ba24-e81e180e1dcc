<template>
  <div class="jimu-widget jimu-tool-widget" v-if="configLoaded">
    <div class="tabs-content">
      <div :class="['tabs', index == tabIndex ? 'active' : '']" v-for="(item, index) in config" :key="index" @click="tabClick(index)">
        {{ item.name }}
      </div>
    </div>
    <template v-for="(item, index) in config">
      <template>
        <div v-show="tabIndex == index" class="content" :key="index">
          <template v-if="item.selectList && item.selectList.length > 1">
            <div class="select-box">
              <a-select v-model="item.defaultID">
                <a-select-option v-for="i in item.selectList" :key="i.value" :value="i.value">{{ i.label }}</a-select-option>
              </a-select>
            </div>
          </template>
          <template v-if="item.component">
            <component :is="item.component" :config="item"></component>
          </template>
          <template v-else>
            <div class="table-content" v-for="el in Object.values(item.obj)" :key="el.value">
              <TabContent v-show="el.value == item.defaultID" :tableConfig="el" />
            </div>
          </template>
        </div>
      </template>
    </template>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
import TabContent from "./components/TabContent/index.vue";
import RiskAreaPage from "./views/RiskAreaPage/index";
export default {
  name: "SpecialTableListWidget",
  mixins: [BaseWidget],
  data() {
    return {
      tabIndex: 0,
      configLoaded: false,
    };
  },
  components: {
    TabContent,
    RiskAreaPage,
  },

  configLoaded() {
    this.configLoaded = true;
  },
  methods: {
    async _loadModule(uri) {
      const module = await import(`@/${uri}`);
      return this.$utils.object.deepCloneObj(module.default);
    },
    tabClick(index) {
      console.log("this.config", this.config);
      this.tabIndex = index;
      if (this.config[index].name == "地块实施台账") {
        this.bus.$emit("checkTZ", true);
      }
      if (this.config[index].name == "自证农产品监测点") {
        this.bus.$emit("checkTZ", false);
      }
      if (this.config[index].name == "动态调整清单列表" || this.config[index].name == "评价单元清单列表") {
        this.bus.$emit(this.$events.layer.CHECKED_LAYER, this.config[index].defaultID);
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";
.tabs-content {
  display: flex;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #e7ebf4;
  overflow: hidden;
}
.tabs {
  width: 50%;
  height: 40px;
  text-align: center;
  // border: 1px solid #ccc;
  line-height: 40px;
}
.content {
  height: calc(100% - 65px);
  display: flex;
  flex-direction: column;
}
.active {
  border-bottom: none;
  // color: rgb(62, 145, 247);
  color: #fff;
  background-color: @primaryColor;
}
.select-box {
  margin-top: 20px;
}
.table-content {
  margin-top: 20px;
  // height: calc(100% - 90px);
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
