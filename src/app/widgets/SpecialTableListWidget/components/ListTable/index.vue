<template>
  <div class="scroll" :ref="scrollRef">
    <div class="empty" v-if="res && res.length == 0">
      <a-empty description="暂无数据" />
    </div>
    <div v-for="(item, index) in res" :key="index" @click="rowItemClick(item, index)">
      <ChildBar :obj="layout" class="child-bar" @click="rowItemClick(item, index)" :data="item" />
    </div>
  </div>
</template>

<script>
import ChildBar from "../ChildBar";
export default {
  name: "ListTable",
  components: { ChildBar },
  props: {
    res: {
      type: Array,
    },
    layout: {
      type: Object,
    },
    // 可选的唯一标识符，用于在for循环中区分不同的实例
    instanceId: {
      type: [String, Number],
      default: () => Math.random().toString(36).substring(2, 11),
    },
    // 滚动事件防抖延迟时间（毫秒）
    debounceDelay: {
      type: Number,
      default: 300,
    },
  },
  data() {
    return {
      scrollRef: `scroll-content-${this.instanceId}`,
      scrollHandler: null,
      debounceTimer: null,
    };
  },
  mounted() {
    this.initScrollListener();
  },
  beforeDestroy() {
    this.removeScrollListener();
    this.clearDebounceTimer();
  },
  methods: {
    initScrollListener() {
      const scrollElement = this.$refs[this.scrollRef];
      if (scrollElement) {
        this.scrollHandler = () => {
          // 清除之前的防抖定时器
          this.clearDebounceTimer();

          // 设置防抖定时器，根据配置的延迟时间执行
          this.debounceTimer = setTimeout(() => {
            // 判断是否滚动到底部（允许一点误差）
            if (scrollElement.scrollHeight - scrollElement.scrollTop <= scrollElement.clientHeight + 10) {
              // 触发滚动到底部的回调事件
              this.$emit("scrollToBottom", {
                instanceId: this.instanceId,
                scrollElement: scrollElement,
              });
            }
          }, this.debounceDelay);
        };

        scrollElement.addEventListener("scroll", this.scrollHandler);
      }
    },

    removeScrollListener() {
      const scrollElement = this.$refs[this.scrollRef];
      if (scrollElement && this.scrollHandler) {
        scrollElement.removeEventListener("scroll", this.scrollHandler);
        this.scrollHandler = null;
      }
    },

    clearDebounceTimer() {
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
        this.debounceTimer = null;
      }
    },

    rowItemClick(item) {
      this.$emit("rowItemClick", item);
    },
  },
};
</script>

<style lang="less" scoped>
.scroll {
  padding: 0 5px;
  overflow-y: scroll;
  .empty {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .child-bar {
    margin-top: 20px;
    &:hover {
      background-color: #e7ebf480;
      cursor: pointer;
    }
  }
}
</style>
