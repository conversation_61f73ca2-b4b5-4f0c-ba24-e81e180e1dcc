<template>
  <div v-show="hasData">
    <div class="filter-item">
      <template v-for="(item, i) in configList.list">
        <a-statistic class="statistic-bg" :key="i" :value="wanChange(item.value, item.decimals)" :valueStyle="{ color: item.color }">
          <span slot="title" class="title">{{ item.label }}</span>
          <span slot="suffix" class="suffix-unit">{{ Number(item.value) > 10000 ? "万" : "" }}{{ item.unit }}</span>
        </a-statistic>
      </template>
    </div>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
export default {
  name: "ListFilter",
  data() {
    return {
      configList: this.tableConfig,
      hasData: true,
    };
  },
  mixins: [BaseWidget],
  props: {
    tableConfig: {
      type: Object,
    },
    adminFilter: {
      type: Array,
    },
    region: {
      type: String,
    },
    filters: {
      type: Array,
    },
  },
  computed: {
    dataFilters() {
      let filters = [];
      // 处理添加行政区的筛选
      if (this.adminFilter) filters.push(this.adminFilter);
      // 基础的筛选
      if (this.filters && this.filters.length > 0) filters.push(...this.filters);
      // 筛选处理
      if (filters.length > 1) {
        filters.unshift("and");
      } else if (filters.length === 1) {
        filters = filters[0];
      } else {
        filters = null;
      }

      return filters;
    },
  },
  methods: {
    getData() {
      if (this.tableConfig.aggregation) {
        this.getAggregation();
      } else {
        this.getFormData();
      }
    },

    wanChange(item, decimals) {
      if (Number(item) > 10000) return (Number(item) / 10000).toFixed(2);
      if (decimals) return Number(item).toFixed(decimals);
      return Number(item);
    },

    async getFormData() {
      let param = {
        returnGeometry: false,
        format: "json",
      };
      if (this.dataFilters) param.filter = this.dataFilters;
      let formName;
      if (typeof this.tableConfig.uri === "string") formName = this.tableConfig.uri;
      else formName = this.tableConfig.uri[this.region];
      let res = await this.$apis.feature.query(formName, param);
      this.configList.list.forEach((item) => {
        item.value = res[0][item.field];
      });
    },
    async getAggregation() {
      let param = {
        groupFields: this.tableConfig.aggregation.groupFields,
        aggregates: this.tableConfig.aggregation.aggregates,
      };
      if (this.dataFilters) param.filter = this.dataFilters;
      let res = await this.$apis.feature.aggregate(this.tableConfig.aggregation.uri, param);
      if (res) {
        this.hasData = true;
        this.configList.list.forEach((item) => {
          let value = res.find((i) => {
            return item.typeValue == i[item.typeField];
          });

          item.value = value ? value[item.field] : 0;
        });
      } else {
        this.hasData = false;
      }
    },
  },
  async mounted() {
    this.getData();
  },
  watch: {
    adminFilter() {
      this.getData();
    },
    filters() {
      this.getData();
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";

.filter-item {
  display: flex;
  justify-content: space-around;
  /deep/.ant-statistic-title {
    text-align: center;
  }
  /deep/.ant-statistic-content {
    // background-color: rgba(204, 202, 79, 0.05);
  }
  .suffix-unit {
    font-size: 10px;
  }
  .title {
    color: @primaryTextColor;
  }
  .btn {
    padding: 8px 10px;
    color: #fff;
  }
}
</style>
