<template>
  <div>
    <div class="filter-item">
      <span class="label">评价标准：</span>
      <template v-for="(item, i) in config.list">
        <div
          :class="['btn', item.active ? 'active' : '']"
          :key="i"
          @click="btnClick(item)"
        >
          {{ item.label }}
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
export default {
  name: "ListFilter",
  data() {
    return {
      config: this.filters,
    };
  },
  mixins: [BaseWidget],
  props: {
    filters: {
      type: Object,
    },
  },

  methods: {
    btnClick(item) {
      this.config.list.forEach((item) => {
        item.active = false;
      });
      item.active = !item.active;
      let filter = ["all"];
      this.config.list.forEach((item) => {
        if (!item.active) return;
        filter.push(["!=", item.field, "1"]);
      });
      filter =
        filter.length == 1 ? null : filter.length == 2 ? filter[1] : filter;
      this.$emit("setFilter", filter);
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";
.filter-item {
  display: flex;
  justify-content: space-around;
  .label {
    color: @primaryTextColor;
  }
  .btn {
    padding: 3px 10px;
    border: solid 1px #ccc;
    color: #333;
    border-radius: 4px;
    background-color: "#fff";
  }
  .active {
    padding: 3px 10px;
    border: solid 1px #ccc;
    color: #fff;
    background-color: #4065e0;
    border: solid 1px rgb(240, 240, 240);
  }
}
</style>
