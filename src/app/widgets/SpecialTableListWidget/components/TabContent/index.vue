<template>
  <div v-if="tableConfig" class="wrap">
    <div>
      <template v-if="tableConfig.bzFilter">
        <FilterChange class="elem-filter" @setFilter="setBzFilter" :filters="tableConfig.bzFilter" />
      </template>
      <template v-if="tableConfig.elemFilter">
        <ElemChange class="elem-filter" @setFilter="setElemFilter" :filters="tableConfig.elemFilter" />
      </template>
      <template v-if="tableConfig.pjdyelemFilter">
        <pjdyElemChange class="elem-filter" @setFilter="setElemFilter" :filters="tableConfig.pjdyelemFilter" />
      </template>
      <template v-if="tableConfig.filters">
        <ListFilter @setFilter="setTypeFilter" :filters="tableConfig.filters" />
      </template>
      <template v-if="tableConfig.pjdyfilters">
        <pjdyListFilter @setFilter="setTypeFilter" :filters="tableConfig.pjdyfilters" />
      </template>
      <template v-if="tableConfig.analysis">
        <AnalysisList class="analysis-list" :tableConfig="tableConfig.analysis" :region="region" :adminFilter="adminFilter" />
      </template>
    </div>
    <ListTable
      v-show="loading"
      :layout="tableConfig.layout"
      class="list-table"
      :res="res"
      :instanceId="tableConfig.value"
      @rowItemClick="rowItemClick"
      @scrollToBottom="handleScrollToBottom"
    />
    <a-icon v-show="!loading" type="loading" />
    <div class="pagination">
      <a-button class="page-btn" :disabled="pageCurrent == 1" @click="prePage">上一页</a-button>
      <a-button class="page-btn" @click="nextPage">下一页</a-button>
    </div>
    <!-- <a-pagination
      v-model="pageCurrent"
      :page-size-options="pageSizeOptions"
      :total="total"
      show-size-changer
      :page-size="pageSize"
      @change="pageChange"
      @showSizeChange="onShowSizeChange"
    >
      <template slot="buildOptionText" slot-scope="props">
        <span>{{ props.value }}条/页</span>
      </template>
    </a-pagination> -->
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
import ListFilter from "../ListFilter/index.vue";
import pjdyListFilter from "../pjdyListFilter/index.vue";
import ElemChange from "../ElemChange/index.vue";
import pjdyElemChange from "../pjdyElemChange/index.vue";
import FilterChange from "../FilterChange/index.vue";
import ListTable from "../ListTable/index.vue";
import AnalysisList from "../AnalysisList/index.vue";
const ST_FILL = ["match", ["get", "ST_CLASS"], 1, "rgb(0, 179, 0)", 2, "rgb(179, 179, 0)", 3, "rgb(179, 0, 0)", "rgb(0, 179, 0)"];
const HD_FILL = ["match", ["get", "HD_CLASS"], 1, "rgb(0, 179, 0)", 2, "rgb(179, 179, 0)", 3, "rgb(179, 0, 0)", "rgb(0, 179, 0)"];
export default {
  name: "TabContent",
  mixins: [BaseWidget],
  components: {
    ListFilter,
    ListTable,
    ElemChange,
    pjdyElemChange,
    AnalysisList,
    FilterChange,
    pjdyListFilter,
  },
  data() {
    return {
      pageSizeOptions: ["10", "20", "30", "40", "50"],
      pageCurrent: 1,
      hasNext: true,
      pageSize: 10,
      total: 50,
      res: null,
      filters: [],
      typeFilter: [],
      elemFilter: null,
      adminFilter: null,
      bzFilter: ["!=", "ST_CLASS", null],
      region: "city",
      specialFilter: null,
      regionParam: {
        city: "CXZQDM",
        county: "FXZQDM",
        town: "TXZQDM",
        village: "XZQDM",
      },
      loading: true,

      eleLayerFilter: ["all"],

      STLayerFilter: [
        ["==", ["get", "ST_CLASS"], 1],
        ["==", ["get", "ST_CLASS"], 2],
        ["==", ["get", "ST_CLASS"], 3],
      ],
      HDLayerFilter: [
        ["==", ["get", "HD_CLASS"], 1],
        ["==", ["get", "HD_CLASS"], 2],
        ["==", ["get", "HD_CLASS"], 3],
      ],
      bzAndTypeFilter: [],
    };
  },
  props: {
    tableConfig: {
      type: Object,
    },
  },
  mounted() {
    this.bus.$on(`setTableFilter_${this.tableConfig.value}`, (e) => {
      if (e.layer == this.tableConfig.value) {
        this.setSpecialFilter(e);
      }
    });
    this.bus.$on(this.$events.admin.CHANGE_ADMIN, this.adminFilterUpdateHandler);

    this.bzAndTypeFilter = [...this.STLayerFilter];
    this.setModuleLayerStyle("land_dttz_xcpjdy_gx", "fill-color", ST_FILL);
    this.setModuleLayerFilter("land_dttz_xcpjdy_gx", "pjdy", ["any", ...this.bzAndTypeFilter]);
    this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "land_dttz_xcpjdy_gx");

    this.getData(1);
  },
  methods: {
    setSpecialFilter(e) {
      if (!this.specialFilter) {
        this.specialFilter = {};
      }
      if (!this.specialFilter[e.layer]) {
        this.$set(this.specialFilter, e.layer, {});
      }
      const layerData = this.specialFilter[e.layer];
      Object.assign(layerData, {
        [e.key]: e.tableFilter,
      });
      this.$set(this.specialFilter, [e.layer], layerData);
      this.getData();
    },
    async getData() {
      this.loading = false;
      let countParams = {};
      let param = {
        returnGeometry: false,
        format: "json",

        limit: this.pageSize,
        page: this.pageCurrent,
      };
      let filter = ["all"];
      if (this.tableConfig.bzFilter) {
        if (this.bzFilter && !this.elemFilter) {
          const type = this.bzFilter[1].slice(0, 2);
          let elemFilters = ["any"];
          elemFilters.push(["=", type + "_CLASS", 1], ["=", type + "_CLASS", 2], ["=", type + "_CLASS", 3]);
          filter.push(elemFilters);
        }

        if (this.bzFilter && this.elemFilter) {
          if (this.bzFilter[0] !== "all") {
            const type = this.bzFilter[1].slice(0, 2);
            if (this.elemFilter[0] !== "all") {
              const elemFilter = ["=", this.elemFilter[1] + type, "1"];
              filter.push(elemFilter);
              filter.push(["any", ["=", this.bzFilter[1], 1], ["=", this.bzFilter[1], 2], ["=", this.bzFilter[1], 3]]);
            } else {
              let elemFilters = ["all"];
              this.elemFilter.forEach((item) => {
                if (Array.isArray(item)) {
                  elemFilters.push(["=", item[1] + type, 1]);
                }
              });

              filter.push(elemFilters);
              filter.push(["any", ["=", this.bzFilter[1], 1], ["=", this.bzFilter[1], 2], ["=", this.bzFilter[1], 3]]);
            }
          }
        }

        if (this.typeFilter) {
          if (this.typeFilter[0] !== "all") {
            let value = this.typeFilter[2];
            filter.forEach((item) => {
              if (Array.isArray(item)) {
                if (item[0] == "any") {
                  item.forEach((i, index) => {
                    if (Array.isArray(i)) {
                      if (i[2] == value) {
                        item.splice(index, 1);
                      }
                    }
                  });
                }
              }
            });
          } else {
            this.typeFilter.forEach((item) => {
              if (Array.isArray(item)) {
                filter.forEach((i) => {
                  if (Array.isArray(i)) {
                    if (i[0] == "any") {
                      i.forEach((ii, index) => {
                        if (Array.isArray(ii)) {
                          if (ii[2] == item[2]) {
                            i.splice(index, 1);
                          }
                        }
                      });
                    }
                  }
                });
              }
            });
          }

          filter.forEach((item) => {
            if (Array.isArray(item)) {
              if (item[0] == "any" && item.length == 2) {
                filter.splice(filter.indexOf(item), 1);
                filter.push(item[1]);
              }
            }
          });
        }
      } else {
        if (this.elemFilter) {
          filter.push(this.elemFilter);
        }
        if (this.typeFilter) {
          filter.push(this.typeFilter);
        }
      }

      if (this.adminFilter) {
        filter.push(this.adminFilter);
      }
      filter.forEach((item) => {
        // 如果为空数组，删除
        if (Array.isArray(item) && item.length === 0) {
          filter.splice(filter.indexOf(item), 1);
        }
      });

      if (this.specialFilter?.[this.tableConfig.value]) {
        Object.values(this.specialFilter[this.tableConfig.value]).forEach((item) => {
          filter.push(item);
        });
      }

      if (filter?.length == 2) {
        filter = filter[1];
      }
      if (filter?.length == 1 && filter[0] == "all") {
        filter = null;
      }

      if (filter?.length !== 0) {
        param.filter = filter;
        countParams.filter = filter;
      }

      // if (!hasPageNum) {
      //   const count = await this.$apis.feature.count(
      //     this.tableConfig.uri,
      //     this.tableConfig.primaryKey,
      //     countParams
      //   )
      //   this.total = count
      // }
      let nextParam = {
        returnGeometry: false,
        format: "json",
        // outFields,
        limit: this.pageSize,
        page: this.pageCurrent + 1,
      };
      let ares = await this.$apis.feature.query(this.tableConfig.uri, nextParam);
      let res = await this.$apis.feature.query(this.tableConfig.uri, param);
      if (ares.length == 0 || res.length == 10) {
        this.hasNext = false;
      }

      if (res.length > 0 && res.length < 10) {
        this.hasNext = false;
      }
      this.res = res;

      console.log(res);

      this.loading = true;
    },
    onShowSizeChange(current, pageSize) {
      this.pageSize = pageSize;
      this.getData();
    },
    nextPage() {
      this.pageCurrent++;
      this.getData();
    },
    prePage() {
      this.pageCurrent--;
      this.getData(this.pageCurrent - 1);
    },
    setTypeFilter(e) {
      this.typeFilter = e;
      if (!e) {
        const type = this.bzFilter[1].slice(0, 2);
        let newFilterCopy = ["any"];
        newFilterCopy.push(["==", ["get", type + "_CLASS"], 1], ["==", ["get", type + "_CLASS"], 2], ["==", ["get", type + "_CLASS"], 3]);

        this.setModuleLayerFilter("land_dttz_xcpjdy_gx", "pjdy", newFilterCopy);
        this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "land_dttz_xcpjdy_gx");
      } else {
        if (e[0] == "=" && e[2] == null) {
          let newFilterCopy = [
            "all",
            ["!=", ["get", "ST_CLASS"], 1],
            ["!=", ["get", "ST_CLASS"], 2],
            ["!=", ["get", "ST_CLASS"], 3],
            ["!=", ["get", "HD_CLASS"], 1],
            ["!=", ["get", "HD_CLASS"], 2],
            ["!=", ["get", "HD_CLASS"], 3],
          ];

          this.setModuleLayerFilter("land_dttz_xcpjdy_gx", "pjdy", newFilterCopy);
          this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "land_dttz_xcpjdy_gx");
        } else {
          if (this.bzFilter && e) {
            let newFilterCopy = JSON.parse(JSON.stringify(this.bzAndTypeFilter));
            if (e[0] != "all") {
              const type = this.bzFilter[1].slice(0, 2);
              const newFilter = ["!=", ["get", type + e[1]], e[2]];
              newFilterCopy.forEach((item) => {
                if (Array.isArray(item)) {
                  if (item[1][1] == newFilter[1][1] && item[2] == newFilter[2]) {
                    newFilterCopy.splice(newFilterCopy.indexOf(item), 1);
                  }
                }
              });
              this.setModuleLayerFilter("land_dttz_xcpjdy_gx", "pjdy", ["any", ...newFilterCopy]);
              this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "land_dttz_xcpjdy_gx");
            } else {
              let newFilterCopy = JSON.parse(JSON.stringify(this.bzAndTypeFilter));
              const newArr = [];
              const type = this.bzFilter[1].slice(0, 2);
              e.forEach((item) => {
                if (Array.isArray(item)) {
                  newArr.push(["!=", ["get", type + item[1]], item[2]]);
                }
              });
              // 按照上面的形式去过滤
              newArr.forEach((item) => {
                newFilterCopy.forEach((i) => {
                  if (Array.isArray(i)) {
                    if (i[1][1] == item[1][1] && i[2] == item[2]) {
                      newFilterCopy.splice(newFilterCopy.indexOf(i), 1);
                    }
                  }
                });
              });

              this.setModuleLayerFilter("land_dttz_xcpjdy_gx", "pjdy", ["any", ...newFilterCopy]);
              this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "land_dttz_xcpjdy_gx");
            }
          }
        }
      }

      this.getData();
    },
    setBzFilter(e) {
      this.bzFilter = e;

      if (e[1] === "ST_CLASS") {
        this.bzAndTypeFilter = [...this.STLayerFilter];
        this.setModuleLayerStyle("land_dttz_xcpjdy_gx", "fill-color", ST_FILL);
      } else {
        this.bzAndTypeFilter = [...this.HDLayerFilter];
        this.setModuleLayerStyle("land_dttz_xcpjdy_gx", "fill-color", HD_FILL);
      }

      this.setModuleLayerFilter("land_dttz_xcpjdy_gx", "pjdy", ["any", ...this.bzAndTypeFilter]);
      this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "land_dttz_xcpjdy_gx");
      this.bus.$emit(this.$events.map.SET_LAYER_STYLE, "land_dttz_xcpjdy_gx");

      this.getData();
    },
    setElemFilter(e) {
      if (!e) {
        this.eleLayerFilter = null;
        this.setModuleLayerFilter("land_dttz_xcpjdy_gx", "elem", this.eleLayerFilter);
        this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "land_dttz_xcpjdy_gx");
      }
      this.elemFilter = e;
      if (this.bzFilter && e) {
        if (e[0] != "all") {
          const type = this.bzFilter[1].slice(0, 2);
          const newFilter = ["==", ["get", e[1] + type], 1];
          this.eleLayerFilter = ["any"];
          this.eleLayerFilter.push(newFilter);
          this.setModuleLayerFilter("land_dttz_xcpjdy_gx", "elem", this.eleLayerFilter);
          this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "land_dttz_xcpjdy_gx");
        } else {
          const newArr = [];
          const type = this.bzFilter[1].slice(0, 2);
          e.forEach((item) => {
            if (Array.isArray(item)) {
              newArr.push(["==", ["get", item[1] + type], 1]);
            }
          });
          this.eleLayerFilter = ["any"];
          this.eleLayerFilter.push(...newArr);
          this.setModuleLayerFilter("land_dttz_xcpjdy_gx", "elem", this.eleLayerFilter);
          this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "land_dttz_xcpjdy_gx");
        }
      }
      this.getData();
    },
    handleScrollToBottom(data) {
      // 滚动到底部时的处理逻辑
      console.log(`表格 ${data.instanceId} 滚动到底部了！`);

      // 如果还有下一页且不在加载中，自动加载下一页
      if (this.hasNext && this.loading) {
        this.nextPage();
      }
    },

    rowItemClick(item) {
      console.log(item, this.tableConfig);
      // this.bus.$emit(this.$events.table.ROW_SELECT, {
      //   key: this.tableConfig.value,
      //   recordId: item[this.tableConfig.primaryKey],
      // });

      // 23年动态调整成果
      this.bus.$emit(this.$events.table.ROW_SELECT, {
        key: "land_dttz_lbhf_2023",
        recordId: item[this.tableConfig.primaryKey],
      });
      this.bus.$emit(this.$events.table.ROW_SELECT, {
        key: "land_lbhf_aqly_2023",
        recordId: item[this.tableConfig.primaryKey],
      });
      this.bus.$emit(this.$events.table.ROW_SELECT, {
        key: "land_lbhf_yggk_2023",
        recordId: item[this.tableConfig.primaryKey],
      });

      // 2023年动态调整评价单元
      this.bus.$emit(this.$events.table.ROW_SELECT, {
        key: "land_dttz_st_2023",
        recordId: item[this.tableConfig.primaryKey],
      });
      this.bus.$emit(this.$events.table.ROW_SELECT, {
        key: "land_dttz_hd_2023",
        recordId: item[this.tableConfig.primaryKey],
      });

      // 2020年类别划分成果
      this.bus.$emit(this.$events.table.ROW_SELECT, {
        key: "guangxi_lbhf_yxbh_2020",
        recordId: item[this.tableConfig.primaryKey],
      });
      this.bus.$emit(this.$events.table.ROW_SELECT, {
        key: "guangxi_lbhf_aqly_2020",
        recordId: item[this.tableConfig.primaryKey],
      });
      this.bus.$emit(this.$events.table.ROW_SELECT, {
        key: "guangxi_lbhf_yggk_2020",
        recordId: item[this.tableConfig.primaryKey],
      });

      // 详查评价单元
      this.bus.$emit(this.$events.table.ROW_SELECT, {
        key: "land_xcpjdy",
        recordId: item[this.tableConfig.primaryKey],
      });
    },
    adminFilterUpdateHandler(e) {
      if (e) {
        this.adminFilter = ["=", this.regionParam[e.region], e.code];
        this.region = e.region;
      } else {
        this.adminFilter = null;
        this.region = "city";
      }
    },
  },
  watch: {
    adminFilter() {
      this.getData();
    },
    bzFilter() {
      this.setElemFilter(this.elemFilter);
      this.setTypeFilter(this.typeFilter);
    },
  },
};
</script>

<style lang="less" scoped>
.wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.list-table {
  margin: 20px 0 40px;
  // box-sizing: border-box;
  flex: 1;
}
.elem-filter {
  margin-bottom: 20px;
}
.analysis-list {
  margin-top: 20px;
}
.pagination {
  display: flex;
  justify-content: center;
  .page-btn {
    margin-right: 20px;
  }
}
</style>
