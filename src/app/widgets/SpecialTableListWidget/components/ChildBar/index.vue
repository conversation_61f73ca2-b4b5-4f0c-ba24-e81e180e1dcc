<template>
  <div class="box">
    <div class="head">
      <div class="head-l">
        <template v-for="(item, index) in obj.left">
          <template v-if="item.multi">
            <div :key="index">
              <span class="label"> {{ item.title }}: </span>
              <template v-for="i in item.key">
                {{ data[i] }}
              </template>
            </div>
          </template>
          <template v-else>
            <div :key="index">
              <span class="label"> {{ item.title }}:</span> {{ data[item.key] }}
            </div>
          </template>
        </template>
      </div>
      <div class="head-r" :style="{ 'background-color': obj.right.colors[data[obj.right.key]] }">
        {{ obj.right.dict[data[obj.right.key]] }}
      </div>
    </div>
    <div>
      <a-descriptions layout="vertical" bordered :column="obj.bottom.length">
        <a-descriptions-item v-for="(item, index) in obj.bottom" :key="index" :label="item.title">
          <template v-if="item.multi">
            <template v-for="i in item.key">
              {{ data[i] }}
            </template>
          </template>
          <template v-else>
            {{ showValue(item) }}
          </template>
        </a-descriptions-item>
      </a-descriptions>
    </div>
  </div>
</template>

<script>
export default {
  name: "ListFilter",
  data() {
    return {
      list: this.filters,
    };
  },
  props: {
    listData: {
      type: Object,
    },
    data: {
      type: Object,
    },
    obj: {
      type: Object,
    },
  },
  methods: {
    btnClick(item) {
      item.active = !item.active;
    },
    showValue(item) {
      let value;
      if (item.calc) {
        const [operator, num] = item.calc;
        switch (operator) {
          case "+":
            value = Number(this.data[item.key] + num);
            break;
          case "-":
            value = Number(this.data[item.key] - num);
            break;
          case "/":
            value = Number(this.data[item.key] / num);
            break;
          case "*":
            value = Number(this.data[item.key] * num);
            break;
          default:
            value = this.data[item.key];
            break;
        }
      }
      if (item.decimal) {
        value = parseFloat(Number(value).toFixed(item.decimal));
      }
      value = value ? value : this.data[item.key];
      return value;
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";
.box {
  margin-bottom: 10px;
  box-shadow: 0 2px 6px 0 rgb(0 0 0 / 5%);
  padding: 5px;
  width: 100%;
}
.head {
  display: flex;
  width: 100%;
  justify-content: space-between;
  margin-bottom: 8px;
  .head-l {
    color: @primaryTextColor;
    .label {
      font-weight: 600;
    }
  }
  .head-r {
    white-space: nowrap;
    height: 20px;
    line-height: 20px;
    padding: 0 13px;
    color: #fff;
    font-size: 12px;
    border-radius: 4px;
  }
}
/deep/.ant-descriptions-item-label {
  padding: 5px;
  text-align: center;
  font-weight: 600;
  color: @primaryTextColor;
  background: #e7ebf4;
  line-height: 28px;
}

/deep/.ant-descriptions-item-content {
  text-align: center;
  padding: 5px;
  font-size: 12px;
  font-weight: 400;
  color: @primaryTextColor;
  line-height: 28px;
}
</style>
