<template>
  <div class="debounce-test">
    <h2>防抖功能测试</h2>
    
    <div class="controls">
      <label>
        防抖延迟时间: 
        <select v-model="debounceDelay">
          <option value="100">100ms (快速响应)</option>
          <option value="300">300ms (默认)</option>
          <option value="500">500ms (中等)</option>
          <option value="1000">1000ms (慢速)</option>
        </select>
      </label>
      
      <button @click="clearLogs">清空日志</button>
    </div>
    
    <div class="test-container">
      <div class="table-wrapper">
        <h3>测试表格 (防抖延迟: {{ debounceDelay }}ms)</h3>
        <ListTable 
          :layout="tableConfig.layout" 
          :res="tableData"
          instanceId="debounce-test-table"
          :debounceDelay="parseInt(debounceDelay)"
          @scrollToBottom="handleScrollToBottom"
        />
      </div>
      
      <div class="log-panel">
        <h3>事件日志</h3>
        <div class="log-content">
          <div v-for="(log, index) in logs" :key="index" class="log-item">
            <span class="timestamp">{{ log.timestamp }}</span>
            <span class="message">{{ log.message }}</span>
          </div>
          <div v-if="logs.length === 0" class="no-logs">
            滚动表格到底部来测试防抖功能...
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ListTable from "../components/ListTable/index.vue";

export default {
  name: "DebounceTest",
  components: {
    ListTable
  },
  data() {
    return {
      debounceDelay: 300,
      logs: [],
      tableConfig: {
        layout: {
          columns: [
            { field: 'id', title: 'ID' },
            { field: 'name', title: '名称' },
            { field: 'description', title: '描述' }
          ]
        }
      },
      tableData: []
    };
  },
  mounted() {
    this.generateTestData();
  },
  methods: {
    generateTestData() {
      // 生成足够多的测试数据，确保可以滚动
      const data = [];
      for (let i = 1; i <= 50; i++) {
        data.push({
          id: i,
          name: `测试项目 ${i}`,
          description: `这是第 ${i} 个测试项目的描述信息，用于测试滚动功能。`
        });
      }
      this.tableData = data;
    },
    
    handleScrollToBottom(data) {
      const now = new Date();
      const timestamp = now.toLocaleTimeString() + '.' + now.getMilliseconds().toString().padStart(3, '0');
      
      this.logs.unshift({
        timestamp,
        message: `滚动到底部事件触发 (实例ID: ${data.instanceId})`
      });
      
      // 限制日志数量，避免内存占用过多
      if (this.logs.length > 100) {
        this.logs = this.logs.slice(0, 100);
      }
      
      console.log(`[${timestamp}] 滚动到底部事件触发`, data);
    },
    
    clearLogs() {
      this.logs = [];
    }
  },
  
  watch: {
    debounceDelay() {
      this.clearLogs();
      this.addLog(`防抖延迟时间已更改为 ${this.debounceDelay}ms`);
    }
  }
};
</script>

<style lang="less" scoped>
.debounce-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  
  h2 {
    color: #333;
    margin-bottom: 20px;
  }
  
  .controls {
    margin-bottom: 20px;
    padding: 15px;
    background: #f5f5f5;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 20px;
    
    label {
      font-weight: 500;
      
      select {
        margin-left: 10px;
        padding: 5px 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
      }
    }
    
    button {
      padding: 5px 15px;
      background: #1890ff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      
      &:hover {
        background: #40a9ff;
      }
    }
  }
  
  .test-container {
    display: flex;
    gap: 20px;
    height: 600px;
    
    .table-wrapper {
      flex: 1;
      border: 1px solid #e8e8e8;
      border-radius: 4px;
      padding: 15px;
      
      h3 {
        margin-bottom: 15px;
        color: #666;
      }
      
      :deep(.scroll) {
        height: 500px;
        border: 1px solid #f0f0f0;
        border-radius: 4px;
      }
    }
    
    .log-panel {
      flex: 1;
      border: 1px solid #e8e8e8;
      border-radius: 4px;
      padding: 15px;
      
      h3 {
        margin-bottom: 15px;
        color: #666;
      }
      
      .log-content {
        height: 500px;
        overflow-y: auto;
        background: #fafafa;
        border: 1px solid #f0f0f0;
        border-radius: 4px;
        padding: 10px;
        
        .log-item {
          display: flex;
          margin-bottom: 8px;
          font-size: 12px;
          
          .timestamp {
            color: #999;
            margin-right: 10px;
            min-width: 100px;
          }
          
          .message {
            color: #333;
          }
        }
        
        .no-logs {
          color: #999;
          text-align: center;
          margin-top: 50px;
          font-style: italic;
        }
      }
    }
  }
}
</style>
