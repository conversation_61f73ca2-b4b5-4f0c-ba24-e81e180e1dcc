<template>
  <div class="list-table-example">
    <h2>ListTable 组件使用示例</h2>
    
    <!-- 单个组件使用 -->
    <div class="single-example">
      <h3>单个组件使用</h3>
      <ListTable
        :layout="singleTableConfig.layout"
        :res="singleTableConfig.data"
        instanceId="single-table"
        :debounceDelay="200"
        @rowItemClick="handleRowClick"
        @scrollToBottom="handleScrollToBottom"
      />
    </div>
    
    <!-- for循环中使用多个组件 -->
    <div class="multiple-example">
      <h3>for循环中使用多个组件</h3>
      <div v-for="(table, index) in multipleTableConfigs" :key="table.id" class="table-container">
        <h4>{{ table.title }}</h4>
        <ListTable
          :layout="table.layout"
          :res="table.data"
          :instanceId="table.id"
          :debounceDelay="500"
          @rowItemClick="(item) => handleRowClick(item, table.id)"
          @scrollToBottom="(data) => handleScrollToBottom(data, table.id)"
        />
      </div>
    </div>
  </div>
</template>

<script>
import ListTable from "../components/ListTable/index.vue";

export default {
  name: "ListTableExample",
  components: {
    ListTable
  },
  data() {
    return {
      // 单个表格配置
      singleTableConfig: {
        layout: {
          // 这里放置你的布局配置
          columns: [
            { field: 'name', title: '名称' },
            { field: 'value', title: '值' }
          ]
        },
        data: [
          { name: '项目1', value: '值1' },
          { name: '项目2', value: '值2' },
          { name: '项目3', value: '值3' },
          // ... 更多数据
        ]
      },
      
      // 多个表格配置
      multipleTableConfigs: [
        {
          id: 'table-1',
          title: '表格1',
          layout: {
            columns: [
              { field: 'name', title: '名称' },
              { field: 'status', title: '状态' }
            ]
          },
          data: [
            { name: '数据1-1', status: '正常' },
            { name: '数据1-2', status: '异常' },
            // ... 更多数据
          ]
        },
        {
          id: 'table-2', 
          title: '表格2',
          layout: {
            columns: [
              { field: 'code', title: '编码' },
              { field: 'description', title: '描述' }
            ]
          },
          data: [
            { code: 'A001', description: '描述A001' },
            { code: 'A002', description: '描述A002' },
            // ... 更多数据
          ]
        },
        {
          id: 'table-3',
          title: '表格3', 
          layout: {
            columns: [
              { field: 'id', title: 'ID' },
              { field: 'type', title: '类型' }
            ]
          },
          data: [
            { id: 1, type: '类型1' },
            { id: 2, type: '类型2' },
            // ... 更多数据
          ]
        }
      ]
    };
  },
  methods: {
    // 处理行点击事件
    handleRowClick(item, tableId = 'single-table') {
      console.log(`表格 ${tableId} 的行被点击:`, item);
      // 在这里处理行点击逻辑
      // 可以根据 tableId 来区分不同表格的处理逻辑
    },
    
    // 处理滚动到底部事件
    handleScrollToBottom(data, tableId) {
      console.log(`表格 ${tableId || data.instanceId} 滚动到底部了！`);
      
      // 根据不同的表格ID执行不同的逻辑
      switch(tableId || data.instanceId) {
        case 'single-table':
          this.loadMoreDataForSingleTable();
          break;
        case 'table-1':
          this.loadMoreDataForTable1();
          break;
        case 'table-2':
          this.loadMoreDataForTable2();
          break;
        case 'table-3':
          this.loadMoreDataForTable3();
          break;
        default:
          console.log('未知表格ID:', tableId || data.instanceId);
      }
    },
    
    // 为单个表格加载更多数据
    loadMoreDataForSingleTable() {
      console.log('为单个表格加载更多数据...');
      // 模拟异步加载数据
      setTimeout(() => {
        const newData = [
          { name: '新项目1', value: '新值1' },
          { name: '新项目2', value: '新值2' }
        ];
        this.singleTableConfig.data.push(...newData);
      }, 1000);
    },
    
    // 为表格1加载更多数据
    loadMoreDataForTable1() {
      console.log('为表格1加载更多数据...');
      setTimeout(() => {
        const newData = [
          { name: '新数据1-3', status: '正常' },
          { name: '新数据1-4', status: '异常' }
        ];
        const table1 = this.multipleTableConfigs.find(t => t.id === 'table-1');
        if (table1) {
          table1.data.push(...newData);
        }
      }, 1000);
    },
    
    // 为表格2加载更多数据
    loadMoreDataForTable2() {
      console.log('为表格2加载更多数据...');
      setTimeout(() => {
        const newData = [
          { code: 'A003', description: '描述A003' },
          { code: 'A004', description: '描述A004' }
        ];
        const table2 = this.multipleTableConfigs.find(t => t.id === 'table-2');
        if (table2) {
          table2.data.push(...newData);
        }
      }, 1000);
    },
    
    // 为表格3加载更多数据
    loadMoreDataForTable3() {
      console.log('为表格3加载更多数据...');
      setTimeout(() => {
        const newData = [
          { id: 3, type: '类型3' },
          { id: 4, type: '类型4' }
        ];
        const table3 = this.multipleTableConfigs.find(t => t.id === 'table-3');
        if (table3) {
          table3.data.push(...newData);
        }
      }, 1000);
    }
  }
};
</script>

<style lang="less" scoped>
.list-table-example {
  padding: 20px;
  
  .single-example, .multiple-example {
    margin-bottom: 40px;
    
    h3 {
      color: #333;
      margin-bottom: 20px;
    }
  }
  
  .table-container {
    margin-bottom: 30px;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    padding: 15px;
    
    h4 {
      color: #666;
      margin-bottom: 15px;
    }
  }
}
</style>
