<template>
  <div class="wrap">
    <!-- 筛选 -->
    <div class="filter-item">
      <span class="label">年份:</span>
      <a-checkbox-group :options="yearColumns" :value="yearSelected" @change="changeYear" />
    </div>
    <div class="filter-item">
      <span class="label">风险等级:</span>
      <div
        v-for="(item, i) in typeColumns"
        class="btn"
        :style="{ 'background-color': item.active ? item.color : '#ccc' }"
        :key="i"
        @click="btnClick(i)"
      >
        {{ item.label }}
      </div>
    </div>
    <div class="filter-item">
      <template v-if="config.analysis">
        <AnalysisList class="analysis-list" :tableConfig="config.analysis" :region="region" :adminFilter="adminFilter" />
      </template>
    </div>
    <!-- 表格 -->
    <ListTable
      v-show="loading"
      :layout="config.layout"
      class="list-table"
      :res="tableData"
      @scrollToBottom="handleScrollBottom"
      @rowItemClick="rowItemClick"
    />
    <a-drawer
      title="详情信息"
      placement="right"
      :visible="detailVisible"
      :get-container="false"
      width="100%"
      :wrap-style="{ position: 'absolute' }"
      @close="detailVisible = false"
    >
      <a-descriptions v-for="item in detailData" :column="3" :key="item._id" :title="item.year + '年'" layout="vertical">
        <a-descriptions-item label="不超标">
          <span style="color: #3f8600; font-size: 30px; font-weight: bold">{{ item.bcb }}</span>
          <span style="color: #3f8600; font-size: 16px; font-weight: bold; margin-left: 4px">个</span>
        </a-descriptions-item>
        <a-descriptions-item label="超标">
          <span style="color: #ff3030; font-size: 30px; font-weight: bold"> {{ item.cb }}</span>
          <span style="color: #ff3030; font-size: 16px; font-weight: bold; margin-left: 4px">个</span>
        </a-descriptions-item>
        <a-descriptions-item label="总数">
          <span style="color: #3f64de; font-size: 30px; font-weight: bold"> {{ item.total }}</span>
          <span style="color: #3f64de; font-size: 16px; font-weight: bold; margin-left: 4px">个</span>
        </a-descriptions-item>
      </a-descriptions>
    </a-drawer>
    <a-icon v-show="!loading" type="loading" />
  </div>
</template>

<script>
import ListTable from "../../components/ListTable/index.vue";
import AnalysisList from "../../components/AnalysisList/index.vue";
export default {
  components: { ListTable, AnalysisList },
  inject: ["bus", "module"],
  props: {
    config: {
      type: Object,
    },
  },
  data() {
    return {
      loading: true,
      originData: [],

      pageCurrent: 1,
      noMore: false,

      yearSelected: [2021],
      yearColumns: [],

      typeColumns: [
        {
          label: "无风险",
          value: 1,
          active: true,
          color: "rgb(0, 179, 0)",
        },
        {
          label: "低风险",
          value: 2,
          active: true,
          color: "rgb(179, 179, 0)",
        },
        {
          label: "中风险",
          value: 3,
          active: true,
          color: "rgb(243, 152, 0)",
        },
        {
          label: "高风险",
          value: 4,
          active: true,
          color: "rgb(179, 0, 0)",
        },
      ],

      detailVisible: false,
      detailData: [],
      // 筛选条件
      region: "city",
      adminFilter: null,
      regionParam: {
        city: "CXZQDM",
        county: "FXZQDM",
        town: "TXZQDM",
        village: "XZQDM",
      },
    };
  },
  async mounted() {
    console.log("config", this.config);

    await this.getYearDict();
    this.init();
    this.bus.$on(this.$events.layer.CLICK_LAYER, this.layerClickHandler);
    this.bus.$on(this.$events.admin.CHANGE_ADMIN, this.adminFilterUpdateHandler);
  },
  computed: {
    tableData() {
      let list = this.originData;
      // 剔除相关风险等级数据
      this.typeColumns.forEach((item) => {
        if (!item.active) {
          list = list.filter((i) => i.level !== item.value);
        }
      });

      return list;
    },
  },
  methods: {
    async layerClickHandler({ data }) {
      if (!data.XCDYBM) return;
      await this.getDetailInfo(data);
      this.detailVisible = true;
    },
    // 表格数据点击
    async rowItemClick(data) {
      console.log("table data", data);
      this.bus.$emit(this.$events.table.ROW_SELECT, {
        key: "land_dttz_hd_2023",
        recordId: data.XCDYBM,
      });
      this.bus.$emit(this.$events.table.ROW_SELECT, {
        key: "land_dttz_st_2023",
        recordId: data.XCDYBM,
      });
      await this.getDetailInfo(data);
      this.detailVisible = true;
    },
    async getDetailInfo(data) {
      let params = {
        returnGeometry: false,
        format: "json",
        filter: ["=", "XCDYBM", data.XCDYBM],
      };
      let res = await this.$apis.feature.query("land_dttz_pjdy_cbln", params);
      this.detailData = res;
      console.log(res);
    },
    changeYear(val) {
      this.yearSelected = val;
      this.init();
    },
    btnClick(index) {
      this.typeColumns[index].active = !this.typeColumns[index].active;
    },
    handleScrollBottom() {
      if (this.noMore) return;
      this.pageCurrent += 1;
      this.getData();
    },
    // 获取年份字典
    async getYearDict() {
      let params = {
        aggregates: [["_id", "count"]],
        groupFields: ["year"],
      };
      let res = await this.$apis.feature.aggregate("land_dttz_pjdy_cbln", params);
      let ary = [];
      res.forEach((item) => ary.push(Number(item.year)));

      let max = Math.max(...ary);
      let min = Math.min(...ary);
      for (let i = min; i <= max; i++) {
        this.yearColumns.push({
          label: i,
          value: i,
        });
      }
    },

    init() {
      this.loading = false;
      this.originData = [];
      this.pageCurrent = 1;
      this.noMore = false;
      this.getData();
    },

    async getData() {
      let params = {
        aggregates: [
          ["cb", "sum"],
          ["bcb", "sum"],
          ["total", "sum", "desc"],
        ],
        groupFields: ["XCDYBM"],
        filter: ["in", "year", this.yearSelected],
        limit: 100,
        page: this.pageCurrent,
      };
      let res = await this.$apis.feature.aggregate("land_dttz_pjdy_cbln", params);

      let data = [];
      res.forEach((item) => {
        let rate = (item.cb_sum / item.total_sum) * 100;
        let obj = {
          cb: item.cb_sum,
          bcb: item.bcb_sum,
          total: item.total_sum,
          XCDYBM: item.XCDYBM,
        };
        obj.rate = rate ? Number(rate.toFixed(2)) : 0;

        if (obj.rate < 10) {
          obj.level = 1;
        } else if (obj.rate < 20) {
          obj.level = 2;
        } else if (obj.rate < 30) {
          obj.level = 3;
        } else {
          obj.level = 4;
        }
        data.push(obj);
      });

      if (data.length == 0) {
        this.noMore = true;
        return;
      }
      this.joinSearchData(data);
    },

    async joinSearchData(data) {
      let ids = data.map((item) => item.XCDYBM);
      let params = {
        returnGeometry: false,
        format: "json",
        filter: ["in", "XCDYBM", ids],
      };
      let ary = [];
      let res = await this.$apis.feature.query("land_dttz_xcpjdy_gx", params);
      res.forEach((item) => {
        // let info = data.find((items) => item.XCDYBM === items.XCDYBM);
        let info = data[0];
        if (info) {
          let obj = {
            ...info,
            sum_nydtbmj: item.sum_nydtbmj,
          };
          ary.push(obj);
        }
      });

      this.originData.push(...ary);
      this.loading = true;
      console.log("请求结果", res);
    },
    adminFilterUpdateHandler(e) {
      if (e) {
        this.adminFilter = ["=", this.regionParam[e.region], e.code];
        this.region = e.region;
      } else {
        this.adminFilter = null;
        this.region = "city";
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";
.filter-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  .label {
    color: @primaryTextColor;
    flex-shrink: 0;
    width: 80px;
  }
  .btn {
    padding: 4px 10px;
    color: #fff;
    border-radius: 4px;
    margin-right: 20px;
  }
}
.wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
}
.list-table {
  margin: 20px 0 40px;
  flex: 1;
}
.pagination {
  display: flex;
  justify-content: center;
  .page-btn {
    margin-right: 20px;
  }
}
.analysis-list {
  width: 100%;
}
</style>
