<template>
  <div class="jimu-widget">
    <template v-for="(item, index) in list">
      <template v-if="type == '' || type == item.xmlx">
        <div class="count-box" :key="index">
          <!-- <img :src="`images/legend/map-pin-fill${index==0?'':'_2'}.png`" alt=""> -->
          <span class="title">{{ item.xmlx }}</span>
          <span class="count">{{ item.xmlx_count }}</span>
        </div>
      </template>
    </template>
  </div>
</template>

<script>
import BaseWidget from '@/BaseWidget'

export default {
  name: 'GGQNumAnalysisWiddget',
  mixins: [BaseWidget],
  data() {
    return {
      value: null,
      adminFilter: null,
      regionParam: {
        city: 'CXZQDM',
        county: 'FXZQDM',
        town: 'TXZQDM',
        village: 'XZQDM'
      },
      list: [],
      type: ''
    }
  },
  mounted() {
    this.bus.$on(this.$events.admin.CHANGE_ADMIN, this.adminFilterUpdateHandler)
    this.bus.$on('setLhggqType', this.setLhggqType)

    this.getData()
  },
  watch: {
    adminFilter() {
      this.getData()
    }
  },
  methods: {
    setLhggqType(e) {
      this.type = e
    },
    async getData() {
      let param = {
        filter: this.adminFilter,
        groupFields: ['xmlx'],
        aggregates: [['xmlx', 'count']]
      }
      let res = await this.$apis.feature.aggregate('land_lhggq_gx', param)
      this.list = res
    },
    adminFilterUpdateHandler(e) {
      if (e) {
        this.adminFilter = ['=', this.regionParam[e.region], e.code]
        this.region = e.region
      } else {
        this.adminFilter = null
        this.region = 'city'
      }
    }
  }
}
</script>

<style lang="less" scoped>
@import '@/assets/styles/common.less';
.count-box {
  width: 200px;
  height: 80px;

  display: flex;
  font-size: 18px;
  align-items: center;
  justify-content: space-around;
  color: #002159;
  margin-bottom: 18px;
  background: white;
  border-radius: 3px;
  box-shadow: 0px 3px 10px 0px rgba(56,139,201,0.3);
  .title{
    font-size: 18px;
  }
  img{
    height: 30px;
  }
  .count{
    font-size: 20px;
  }
}
</style>