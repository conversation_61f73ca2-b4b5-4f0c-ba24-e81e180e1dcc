<template>
  <div>
    <div class="filter-item">
      <span class="label">超标污染物：</span>
      <template v-for="(item, i) in config.list">
        <div
          :class="['btn', item.active ? 'active' : '']"
          :key="i"
          @click="btnClick(item)"
        >
          {{ item.label }}
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
export default {
  name: "ListFilter",
  data() {
    return {
      config: this.filters,
    };
  },
  mixins: [BaseWidget],
  props: {
    filters: {
      type: Object,
    },
  },
  methods: {
    btnClick(item) {
      item.active = !item.active;
      let filter = ["all"];
      let layerFilter = ["all"];
      this.config.list.forEach((item) => {
        if (!item.active) return;
        filter.push(["!=", item.field, "1"]);
        layerFilter.push([
          "!=",
          ["get", item.field],
          item.value ? item.value : 1,
        ]);
      });
      filter =
        filter.length == 1 ? null : filter.length == 2 ? filter[1] : filter;
      this.$emit("setFilter", filter);
      layerFilter =
        layerFilter.length == 1
          ? null
          : layerFilter.length == 2
          ? layerFilter[1]
          : layerFilter;

      this.setModuleLayerFilter("land_aqlytz_gx_2020", "elem", layerFilter);
      this.setModuleLayerFilter("land_aqlytz_gx_2021", "elem", layerFilter);

      this.setModuleLayerFilter("land_lnjcd_ncp_2020", "elem", layerFilter);
      this.setModuleLayerFilter("land_lnjcd_ncp_2021", "elem", layerFilter);
      this.setModuleLayerFilter("land_lnjcd_ncp_2022", "elem", layerFilter);

      this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "land_aqlytz_gx_2020");
      this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "land_aqlytz_gx_2021");

      this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "land_lnjcd_ncp_2020");
      this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "land_lnjcd_ncp_2021");
      this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "land_lnjcd_ncp_2022");
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";
.filter-item {
  display: flex;
  justify-content: space-around;
  .label {
    color: @primaryTextColor;
  }
  .btn {
    padding: 3px 10px;
    border: solid 1px #ccc;
    color: #333;
    border-radius: 4px;
    background-color: "#fff";
  }
  .active {
    padding: 3px 10px;
    border: solid 1px #ccc;
    color: #fff;
    background-color: #4065e0;
    border: solid 1px rgb(240, 240, 240);
  }
}
</style>
