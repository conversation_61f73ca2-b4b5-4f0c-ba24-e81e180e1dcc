<template>
  <div>
    <div class="filter-item">
      <span class="label">类别：</span>
      <template v-for="(item, i) in list.list">
        <div
          class="btn"
          :key="i"
          :style="{ 'background-color': item.active ? item.color : '#ccc' }"
          @click="btnClick(item)"
        >
          {{ item.label }}
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
export default {
  name: "ListFilter",
  data() {
    return {
      list: this.filters,
    };
  },
  mixins: [BaseWidget],
  props: {
    filters: {
      type: Object,
    },
  },
  methods: {
    btnClick(item) {
      item.active = !item.active;
      let filter = ["all"];
      let layerFilter = ["all"];
      this.list.list.forEach((item) => {
        if (item.active) return;
        filter.push(["!=", item.field, item.value]);
        layerFilter.push(["!=", ["get", item.field], item.layerValue]);
      });
      if (filter?.length == this.list.list.length + 1) {
        filter = ["=", this.list.list[0].field, ""];
      }
      filter =
        filter.length == 1 ? null : filter.length == 2 ? filter[1] : filter;
      this.$emit("setFilter", filter);
      layerFilter =
        layerFilter.length == 1
          ? null
          : layerFilter.length == 2
          ? layerFilter[1]
          : layerFilter;

      this.setModuleLayerFilter("land_aqlytz_gx_2020", "class", layerFilter);
      this.setModuleLayerFilter("land_aqlytz_gx_2021", "class", layerFilter);

      this.setModuleLayerFilter("land_lnjcd_ncp_2020", "class", layerFilter);
      this.setModuleLayerFilter("land_lnjcd_ncp_2021", "class", layerFilter);
      this.setModuleLayerFilter("land_lnjcd_ncp_2022", "class", layerFilter);

      this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "land_aqlytz_gx_2020");
      this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "land_aqlytz_gx_2021");

      this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "land_lnjcd_ncp_2020");
      this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "land_lnjcd_ncp_2021");
      this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "land_lnjcd_ncp_2022");
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";
.filter-item {
  display: flex;
  justify-content: space-around;
  .label {
    color: @primaryTextColor;
  }
  .btn {
    padding: 4px 10px;
    color: #fff;
    border-radius: 4px;
  }
}
</style>
