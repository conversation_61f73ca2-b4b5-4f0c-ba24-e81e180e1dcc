<template>
  <div class="scroll">
    <div
      v-for="(item, index) in res"
      :key="index"
      @click="rowItemClick(item, index)"
    >
      <ChildBar
        :obj="layout"
        class="child-bar"
        @click="rowItemClick(item, index)"
        :data="item"
      />
    </div>
  </div>
</template>

<script>
import ChildBar from '../ChildBar'
export default {
  name: 'ListTable',
  components: { ChildBar },
  props: {
    res: {
      type: Array
    },
    layout: {
      type: Object
    }
  },
  data() {
    return {}
  },
  methods: {
    rowItemClick(item) {
      this.$emit('rowItemClick', item)
    }
  }
}
</script>

<style lang="less" scoped>
.scroll {
  padding: 0 5px;
  overflow-y: scroll;

  .child-bar {
    margin-top: 20px;
    &:hover{
      background-color: #e7ebf480;
      cursor: pointer;
    }
  }
}
</style>