<template>
	<div v-show="hasData">
		<div class="filter-item">
			<template v-for="(item, i) in configList.list">
				<a-statistic class="statistic-bg" :key="i" :value="wanChange(item.value, item.decimals)" :valueStyle="{ color: item.color }">
					<span slot="title" class="title">{{ item.label }}</span>
					<span slot="suffix" class="suffix-unit">{{ Number(item.value) > 10000 ? "万" : "" }}{{ item.unit }}</span>
				</a-statistic>
			</template>
		</div>
	</div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
import { mapState } from "vuex";
export default {
	name: "ListFilter",
	data() {
		return {
			configList: this.config,
			hasData: true,
			filter: null,
		};
	},
	mixins: [BaseWidget],
	props: {
		config: {
			type: Object,
		},
		adminFilter: {
			type: Array,
		},
		region: {
			type: String,
		},
		selectValue: {
			type: String,
		},
		speFilter: {
			type: Array,
		},
		type: {
			type: Number,
		},
	},

	methods: {
		wanChange(item, decimals) {
			if (Number(item) > 10000) return (Number(item) / 10000).toFixed(2);
			if (decimals) return Number(item).toFixed(decimals);
			return Number(item);
		},
		getData() {
			if (this.config.index == 0) {
				this.getNcpAggregation();
			} else {
				this.getTrAggregation();
			}
		},
		async getNcpAggregation() {
			const p = {
				province: {
					filterField: "CXZQDM",
					valueField: "PXZQDM",
					table: "land_ncp_dw_province",
				},
				city: {
					filterField: "CXZQDM",
					valueField: "CXZQDM",
					table: "land_ncp_dw_city",
				},
				county: {
					filterField: "FXZQDM",
					valueField: "CXZQDM",
					table: "land_ncp_dw_county",
				},
				town: {
					filterField: "CXZQDM",
					valueField: "TXZQDM",
					table: "land_ncp_dw_town",
				},
				village: {
					filterField: "TXZQDM",
					valueField: "VXZQDM",
					table: "land_ncp_dw_village",
				},
			};
			const f = p[this.adminData ? this.adminData.region : "province"];
			let adminFilter = this.adminData ? ["=", f.filterField, this.adminData[f.filterField]] : null;
			let dateFilter = ["=", "date", this.selectValue];
			let res;
			//  农产品tab类型
			if (this.type != 0) {
				let dwlxFilter = ["=", "dwlx", "定位预警监测"];
				//  let filter = ["all", adminFilter, dateFilter, dwlxFilter, this.speFilter];
				let filter = ["all", adminFilter, dateFilter, dwlxFilter];
				filter.forEach(item => {
					if (item == null || item == undefined || item == "") {
						filter.splice(filter.indexOf(item), 1);
					}
				});

				let param = {
					filter: filter,
					groupFields: [f.valueField],
					aggregates: [
						["zh5_bcb", "sum"],
						["point_all", "sum"],
						["zh5_cb", "sum"],
					],
				};
				res = await this.$apis.feature.aggregate(f.table, param);
				//  国控监测点类型
			} else {
				let filter = ["all", adminFilter, dateFilter];
				filter.forEach(item => {
					if (item == null || item == undefined || item == "") {
						filter.splice(filter.indexOf(item), 1);
					}
				});
				if (filter.length === 2) filter = filter[1];

				let param = {
					filter: filter,
					groupFields: ["rwlx"],
					aggregates: [["rwlx", "count"]],
				};
				res = await this.$apis.feature.aggregate("land_gkjcd_point", param);
			}

			if (res) {
				this.hasData = true;
				if (this.type !== 0) {
					let total = res[0].point_all_sum;
					let qualifiedCount = res[0].zh5_bcb_sum;
					let unqualifiedCount = res[0].zh5_cb_sum;
					// 合格率
					let qualifiedRate = total > 0 ? parseFloat((qualifiedCount / total) * 100).toFixed(2) : 0;
					this.configList.list.forEach(item => {
						switch (item.field) {
							case "qualifiedCount":
								item.value = qualifiedCount;
								break;
							case "unqualifiedCount":
								item.value = unqualifiedCount;
								break;
							case "qualifiedRate":
								item.value = qualifiedRate;
								break;
						}
					});
				} else {
					this.configList.list.forEach(item => {
						const data = res.filter(e => e.rwlx === item.field);
						item.value = data[0].rwlx_count;
					});
				}
			} else {
				this.hasData = false;
			}
		},
		async getTrAggregation() {
			const q = {
				province: {
					filterField: "CXZQDM",
					valueField: "PXZQDM",
					table: "land_tr_dw_province",
				},
				city: {
					filterField: "CXZQDM",
					valueField: "CXZQDM",
					table: "land_tr_dw_city",
				},
				county: {
					filterField: "FXZQDM",
					valueField: "CXZQDM",
					table: "land_tr_dw_county",
				},
				town: {
					filterField: "CXZQDM",
					valueField: "TXZQDM",
					table: "land_tr_dw_town",
				},
				village: {
					filterField: "TXZQDM",
					valueField: "VXZQDM",
					table: "land_tr_dw_village",
				},
			};
			console.log("this.adminData", this.adminData);
			const f = q[this.adminData ? this.adminData.region : "province"];
			let adminFilter = this.adminData ? ["=", f.filterField, this.adminData[f.filterField]] : null;

			let dwlxFilter = ["=", "dwlx", "定位预警监测"];
			let dateFilter = ["=", "date", this.selectValue];
			let filter = ["all", adminFilter, dwlxFilter, dateFilter];

			filter.forEach(item => {
				if (item == null || item == undefined || item == "") {
					filter.splice(filter.indexOf(item), 1);
				}
			});

			let param = {
				filter: filter,
				groupFields: [f.valueField],
				aggregates: [
					["zh5_yxbh", "sum"],
					["zh5_yggk", "sum"],
					["zh5_aqly", "sum"],
					["point_all", "sum"],
				],
			};
			let res = await this.$apis.feature.aggregate(f.table, param);
			if (res) {
				this.hasData = true;
				let totalCount = res[0].point_all_sum;
				let yxCount = res[0].zh5_yxbh_sum;
				let aqCount = res[0].zh5_aqly_sum;
				let ygCount = res[0].zh5_yggk_sum;
				this.configList.list.forEach(item => {
					switch (item.field) {
						case "totalCount":
							item.value = totalCount;
							break;
						case "yxCount":
							item.value = yxCount;
							break;
						case "aqCount":
							item.value = aqCount;
							break;
						case "ygCount":
							item.value = ygCount;
							break;
					}
				});
			} else {
				this.hasData = false;
			}
		},
	},
	mounted() {
		this.getData();
	},
	computed: {
		...mapState({
			adminData: state => state.data?.admin,
		}),
	},
	watch: {
		async adminData() {
			await this.getData();
		},
		async selectValue() {
			await this.getData();
		},
		async speFilter() {
			await this.getData();
		},
	},
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";
.statistic-bg {
}

.filter-item {
	display: flex;
	justify-content: space-around;
	/deep/.ant-statistic-title {
		text-align: center;
	}
	/deep/.ant-statistic-content {
		// background-color: rgba(204, 202, 79, 0.05);
	}
	.suffix-unit {
		font-size: 10px;
	}
	.title {
		color: @primaryTextColor;
	}
	.btn {
		padding: 8px 10px;
		color: #fff;
	}
}
</style>
