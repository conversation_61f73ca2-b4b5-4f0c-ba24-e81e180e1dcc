<template>
  <div v-if="tableConfig" class="wrap">
    <div>
      <template>
        <div class="search">
          <span class="search_label">时期:</span>
          <a-select
            :default-value="selectValue"
            style="width: 80%"
            @change="handleChange"
          >
            <a-select-option
              v-for="(item, index) in selectArr"
              :key="index"
              :value="item.value"
            >
              {{ item.label }}
            </a-select-option>
          </a-select>
        </div>
        <template v-if="tableConfig.analysis">
          <AnalysisList
            class="analysis-list"
            :type="type"
            :config="tableConfig.analysis"
            :region="region"
            :adminFilter="adminFilter"
            :selectValue="selectValue"
            :speFilter="speFilter"
          />
        </template>
        <template v-if="tableConfig.elemFilter">
          <ElemChange
            class="elem-filter"
            @setFilter="setElemFilter"
            :filters="tableConfig.elemFilter"
          />
        </template>
      </template>
      <template v-if="tableConfig.filters">
        <ListFilter @setFilter="setTypeFilter" :filters="tableConfig.filters" />
      </template>
    </div>
    <ListTable
      v-show="loading"
      :layout="layout"
      class="list-table"
      :res="res"
      @rowItemClick="rowItemClick"
    />
    <a-icon v-show="!loading" type="loading" />
    <div class="pagination">
      <a-button class="page-btn" :disabled="pageCurrent == 1" @click="prePage"
        >上一页</a-button
      >
      <a-button class="page-btn" @click="nextPage">下一页</a-button>
    </div>
    <!-- <a-pagination
      v-model="pageCurrent"
      :page-size-options="pageSizeOptions"
      :total="total"
      show-size-changer
      :page-size="pageSize"
      @change="pageChange"
      @showSizeChange="onShowSizeChange"
    >
      <template slot="buildOptionText" slot-scope="props">
        <span>{{ props.value }}条/页</span>
      </template>
    </a-pagination> -->
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
import ListFilter from "../ListFilter/index.vue";
import ElemChange from "../ElemChange/index.vue";
import ListTable from "../ListTable/index.vue";
import AnalysisList from "../AnalysisList/index.vue";
import { mapState } from "vuex";

export default {
  name: "TabContent",
  mixins: [BaseWidget],
  components: { ListFilter, ListTable, ElemChange, AnalysisList },
  data() {
    return {
      pageSizeOptions: ["10", "20", "30", "40", "50"],
      pageCurrent: 1,
      hasNext: true,
      pageSize: 10,
      total: 50,
      res: null,
      filters: [],
      typeFilter: null,
      elemFilter: null,
      adminFilter: null,

      region: "city",
      specialFilter: null,
      regionParam: {
        city: "CXZQDM",
        county: "FXZQDM",
        town: "TXZQDM",
        village: "XZQDM",
      },
      loading: true,

      selectGkArr: [
        {
          label: "2020年产地环境国控监测点",
          value: "2020",
        },
        {
          label: "2021年产地环境国控监测点",
          value: "2021",
        },
        {
          label: "2022年产地环境国控监测点",
          value: "2022",
        },
        {
          label: "2023年产地环境国控监测点",
          value: "2023",
        },
      ],
      selectNcpArr: [
        {
          label: "2017年定位预警监测农产品",
          value: "2017",
        },
        {
          label: "2018年定位预警监测农产品",
          value: "2018",
        },
        {
          label: "2019年定位预警监测农产品",
          value: "2019",
        },
        {
          label: "2020年定位预警监测农产品",
          value: "2020",
        },
        {
          label: "2021年定位预警监测农产品",
          value: "2021",
        },
        {
          label: "2022年定位预警监测农产品",
          value: "2022",
        },
      ],
      selectTrArr: [
        {
          label: "2017年定位预警监测土壤",
          value: "2017",
        },
        {
          label: "2018年定位预警监测土壤",
          value: "2018",
        },
        {
          label: "2019年定位预警监测土壤",
          value: "2019",
        },
        {
          label: "2020年定位预警监测土壤",
          value: "2020",
        },
        {
          label: "2021年定位预警监测土壤",
          value: "2021",
        },
        {
          label: "2022年定位预警监测土壤",
          value: "2022",
        },
      ],
      selectValue: "2020",
      select: [],

      speFilter: [],
    };
  },
  props: {
    tableConfig: {
      type: Object,
    },
    type: {
      type: Number,
    },
  },
  async mounted() {
    this.bus.$on(`setTableFilter_${this.tableConfig.value}`, (e) => {
      if (e.layer == this.tableConfig.value) {
        this.setSpecialFilter(e);
      }
    });
    this.bus.$on(
      this.$events.admin.CHANGE_ADMIN,
      this.adminFilterUpdateHandler
    );

    await this.getData(1);
  },
  computed: {
    // 从全局配置中获取年份配置
    ...mapState({
      years: (state) => state.config.year?.environment,
      adminData: (state) => state.data?.admin,
    }),

    layout() {
      return this.tableConfig.layout;
    },
    selectArr() {
      console.log("this.type ", this.type);
      if (this.type == 0) {
        return this.selectGkArr;
      } else if (this.type == 1) {
        return this.selectNcpArr;
      } else {
        return this.selectTrArr;
      }
    },
  },
  methods: {
    handleChange(e) {
      this.select = [];
      this.select = this.selectArr.filter((item) => {
        return item.value == e;
      });
      console.log("eeeee", e);
      this.selectValue = e;
      this.getData();
    },
    setSpecialFilter(e) {
      if (!this.specialFilter) {
        this.specialFilter = {};
      }
      if (!this.specialFilter[e.layer]) {
        this.$set(this.specialFilter, e.layer, {});
      }
      const layerData = this.specialFilter[e.layer];
      Object.assign(layerData, {
        [e.key]: e.tableFilter,
      });
      this.$set(this.specialFilter, [e.layer], layerData);

      this.speFilter = e.tableFilter;
      this.getData();
    },
    async getData() {
      this.loading = false;
      let countParams = {};
      let param = {
        returnGeometry: false,
        format: "json",
        limit: this.pageSize,
        page: this.pageCurrent,
      };
      let filter = ["all"];
      if (this.typeFilter) {
        filter.push(this.typeFilter);
      }
      if (this.elemFilter) {
        filter.push(this.elemFilter);
      }
      if (this.adminFilter) {
        filter.push(this.adminFilter);
      }
      console.log("adminFilteradminFilter", this.adminFilter);
      if (this.specialFilter?.[this.tableConfig.value]) {
        Object.values(this.specialFilter[this.tableConfig.value]).forEach(
          (item) => {
            if (item) filter.push(item);
          }
        );
      }
      // 添加时间过滤
      if (this.selectValue && this.type == 2) {
        filter.push(["=", "date", Number(this.selectValue)]);
      } else {
        filter.push(["=", "date", this.selectValue]);
      }
      // dwlx
      if (this.type != 0) {
        filter.push(["=", "dwlx", "定位预警监测"]);
      }

      if (filter?.length == 2) {
        filter = filter[1];
      }
      if (filter?.length !== 1) {
        param.filter = filter;
        countParams.filter = filter;
      }

      let nextParam = {
        returnGeometry: false,
        format: "json",
        limit: this.pageSize,
        page: this.pageCurrent + 1,
      };

      let ares = await this.$apis.feature.query(
        this.tableConfig.uri,
        nextParam
      );
      let res = await this.$apis.feature.query(this.tableConfig.uri, param);
      if (ares.length == 0 || res.length == 10) {
        this.hasNext = false;
      }
      if (this.type !== 0) {
        res.map((item) => {
          item.zhn_class = Number(item.zhn_class) > 1 ? "不合格" : "合格";
        });
      }
      this.res = res;
      if (res.length > 0 && res.length < 10) {
        this.hasNext = false;
        this.res = res;
      }
      this.loading = true;
    },
    onShowSizeChange(current, pageSize) {
      this.pageSize = pageSize;
      this.getData();
    },
    nextPage() {
      this.pageCurrent++;
      this.getData();
    },
    prePage() {
      this.pageCurrent--;
      this.getData(this.pageCurrent - 1);
    },
    setTypeFilter(e) {
      this.typeFilter = e;
      this.getData();
    },
    setElemFilter(e) {
      this.elemFilter = e;
      this.getData();
    },
    rowItemClick(item) {
      if (this.type == 0) {
        console.log(item[this.tableConfig.primaryKey]);
        this.bus.$emit(this.$events.table.ROW_SELECT, {
          key: `guangxi_${this.selectValue}gkjcd`,
          recordId: item[this.tableConfig.primaryKey],
        });
      } else if (this.type == 1) {
        this.bus.$emit(this.$events.table.ROW_SELECT, {
          key: `guangxi_${this.selectValue}njcncpd`,
          recordId: item[this.tableConfig.primaryKey],
        });
      } else if (this.type == 2) {
        this.bus.$emit(this.$events.table.ROW_SELECT, {
          key: `guangxi_${this.selectValue}jctrd`,
          recordId: item[this.tableConfig.primaryKey],
        });
      }
    },
    adminFilterUpdateHandler(e) {
      if (e) {
        this.adminFilter = ["=", this.regionParam[e.region], e.code];
        this.region = e.region;
      } else {
        this.adminFilter = null;
        this.region = "city";
      }
    },
  },
  watch: {
    adminFilter() {
      this.getData();
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";
.wrap {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.search {
  // 在同一行
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-bottom: 12px;
  .search_label {
    color: @primaryTextColor;
  }
}

.dropData {
  margin: 0 20px 20px 20px;
}

.list-table {
  margin: 20px 0 40px;
  // box-sizing: border-box;
  flex: 1;
}
.elem-filter {
  margin-bottom: 20px;
}
.analysis-list {
  background-color: #f2f2f2;
  padding: 15px 0;
  margin-bottom: 20px;
}
.pagination {
  display: flex;
  justify-content: center;
  .page-btn {
    margin-right: 20px;
  }
}
</style>
