<template>
  <div class="jimu-widget jimu-tool-widget" v-if="configLoaded">
    <div class="tabs-content">
      <div
        :class="['tabs', index == tabIndex ? 'active' : '']"
        v-for="(item, index) in config"
        :style="{ width: `${100 / config.length}%` }"
        :key="index"
        @click="tabClick(index)"
      >
        {{ item.name }}
      </div>
    </div>

    <template v-for="(item, index) in config">
      <template>
        <div v-show="tabIndex == index" class="content" :key="index">
          <template v-if="item.selectList.length > 1">
            <div class="select-box">
              <a-select v-model="item.defaultID">
                <template v-for="i in item.selectList">
                  <a-select-option :key="i.value" :value="i.value">{{
                    i.label
                  }}</a-select-option>
                </template>
              </a-select>
            </div>
          </template>
          <template v-for="el in Object.values(item.obj)">
            <TabContent
              v-show="el.value == item.defaultID"
              :key="el.value"
              class="table-content"
              :tableConfig="el"
              :type="index"
            />
          </template>
        </div>
      </template>
    </template>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
import TabContent from "./components/TabContent/index.vue";

export default {
  name: "SpecialTableListWidget",
  mixins: [BaseWidget],
  data() {
    return {
      tabIndex: 0,
      configLoaded: false,
    };
  },
  components: {
    TabContent,
  },

  configLoaded() {
    this.configLoaded = true;
  },
  mounted() {},
  methods: {
    tabClick(index) {
      this.tabIndex = index;
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";
.tabs-content {
  display: flex;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #e7ebf4;
  overflow: hidden;
}
.tabs {
  width: 50%;
  height: 40px;
  text-align: center;
  // border: 1px solid #ccc;
  line-height: 40px;
}
.content {
  height: calc(100% - 65px);
  display: flex;
  flex-direction: column;
}
.active {
  border-bottom: none;
  // color: rgb(62, 145, 247);
  color: #fff;
  background-color: @primaryColor;
}
.select-box {
  margin-top: 20px;
}
.table-content {
  margin-top: 20px;
  // height: calc(100% - 90px);
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
