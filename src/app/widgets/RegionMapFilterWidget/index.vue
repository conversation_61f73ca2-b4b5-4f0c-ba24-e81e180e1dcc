<template>
  <div class="jimu-widget wrap" v-if="isLoad">
    <template v-for="(item, index) in config">
      <div :key="index" class="select-item" v-if="item.list">
        <template v-if="item.type == 'cascaderFilter'">
          <a-cascader
            :allowClear="false"
            :options="item.list"
            placeholder="Please select"
            @change="onChange"
            v-model="item.value"
          />
        </template>
        <template v-else>
          <a-select v-model="item.value" @change="onChange">
            <template v-for="(i, n) in item.list">
              <a-select-option :key="n" :value="i.value">
                {{ i.label }}
              </a-select-option>
            </template>
          </a-select>
        </template>
      </div>
    </template>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
export default {
  name: "RegionMapFilterWidget",
  mixins: [BaseWidget],
  data() {
    return {
      isLoad: false,
      levelParam: ["PXZQDM", "CXZQDM", "FXZQDM"],
      level: 0,
      adminCode: "45",
      adminData: null,

      asyncChange: false,
    };
  },
  mounted() {
    this.bus.$on(
      this.$events.admin.CHANGE_ADMIN,
      this.adminFilterUpdateHandler
    );
  },
  activated() {
    console.log("======================activated======================");
    this.getAsyncData();
    if (this.asyncChange) this.onChange();
  },
  configLoaded() {
    this.isLoad = true;
    this.getAsyncData();
    setTimeout(() => {
      this.onChange();
    }, 500);
  },
  methods: {
    getAsyncData() {
      let change = false;
      this.config.map((item) => {
        if (item.async) {
          let asyncData = this.getGlobalData(item.async);
          if (item.value != asyncData) change = true;
          if (asyncData) item.value = asyncData;
        }
        return item;
      });
      this.asyncChange = change;
    },
    adminFilterUpdateHandler(e) {
      if (e) {
        this.adminData = e;
        this.adminCode = e.code;
        this.level = e.level;
      } else {
        this.adminData = null;
        this.adminCode = "45";
        this.level = 0;
      }
      this.onChange();
    },
    onChange() {
      let fieldObj = this.config.find((item) => {
        return item.type == "field";
      });
      const field = fieldObj.value;
      let thisField = fieldObj.list.find((item) => {
        return item.value == field;
      });
      const type = thisField.type;

      let filters = this.config
        .filter((item) => {
          return item.type == "filter";
        })
        .map((item) => {
          if (item.field === "date") {
            if (!this.map) return;
            let filter = [
              "all",
              ["==", ["get", "date"], item.value],
              ["in", ["get", this.levelParam[this.level]], this.adminCode],
            ];
            this.setModuleLayerFilter("straw_company", "straw", filter);
            this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "straw_company");

            // this.map.setFilter("straw_company", [
            //   "all",
            //   ["==", ["get", "date"], item.value],
            //   ["in", ["get", this.levelParam[this.level]], this.adminCode],
            // ]);
          }
          return ["=", item.field, item.value];
        });
      let cascaderFilterData = [];
      const cascaderFilter = this.config.filter((item) => {
        return item.type == "cascaderFilter";
      });
      cascaderFilter.forEach((elem) => {
        elem.field.forEach((fieldItem, index) => {
          cascaderFilterData.push([
            "=",
            fieldItem,
            elem.value[index] ? elem.value[index] : "合计",
          ]);
        });
      });
      filters = [...filters, ...cascaderFilterData];
      this.bus.$emit("RegionCHART_UPDATE", {
        field,
        filters,
        type,
        name: thisField.label,
      });
      let filterField = filters.map((item) => {
        return { id: item[1], value: item[2] };
      });
      this.bus.$emit("RegionMapFilter", filterField);
      this.config.forEach((item) => {
        if (item.async) {
          this.setGlobalData(item.async, item.value);
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.wrap {
  display: flex;
  .select-item {
    background: #ffffff;
    border-radius: 4px;
    padding: 3px;
    box-shadow: 2px 2px 3px rgb(0 0 0 / 20%);
    margin-right: 12px;
  }
  /deep/.ant-select-selection--single {
    border: none;
  }
  /deep/.ant-cascader-picker {
    min-width: 200px;
  }
  /deep/.ant-select {
    min-width: 120px;
  }
}
</style>
