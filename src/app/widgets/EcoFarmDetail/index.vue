<template>
  <div class="jimu-widget jimu-tool-widget box">
    <div class="search">
      <a-input-search
        placeholder="请输入基地名称"
        style="width: 90%"
        @search="onSearch"
      />
      <div class="query">
        <div
          class="queryData"
          v-for="(item, index) in base"
          :key="index"
          @click="checkedBase(item)"
          v-show="!checked"
        >
          {{ item.ncmc }}
        </div>
      </div>
    </div>
    <div class="base" v-show="checked">
      <div class="info">
        <TitleBar :title="'基本信息'"></TitleBar>
        <div class="contain cleardiv">
          <!-- <img src="../../../../public/images/basemap_grey.jpg" alt=""> -->
          <div>
            <p class="nc-title">{{ checked.ncmc }}</p>
            <p><span>地址:</span> {{ checked.dz }}</p>
            <p><span>占地面积(亩):</span> {{ checked.zdmj }}</p>
            <p><span>投入资金(万元):</span> {{ checked.trzj }}</p>
            <p><span>农场类型:</span> {{ checked.nclx }}</p>
            <p><span>主导农产品:</span> {{ checked.zdncp }}</p>
            <p><span>通信地址:</span> {{ checked.dz }}</p>
            <p><span>注册登记编码:</span> {{ checked.zcdjbm }}</p>
            <p><span>统一社会信用代码:</span> {{ checked.tyshxydm }}</p>
            <p><span>营业执照注册号:</span> {{ checked.yyzz }}</p>
            <p><span>经营模式:</span> {{ checked.jyms }}</p>
            <p><span>组织模式:</span> {{ checked.zzms }}</p>
            <p v-if="checked.ncjj">
              <span>附件链接:</span> <a-icon type="link" />
              <a
                target="_blank"
                :href="
                  'http://localhost/document/farm/farmword/简介/' + checked.ncjj
                "
                >农场简介</a
              >
            </p>
          </div>
        </div>
      </div>
      <div class="info" v-if="checked.stnc_xxcjb">
        <TitleBar :title="'信息采集表'"></TitleBar>
        <div class="contain cleardiv">
          <!-- <img src="../../../../public/images/basemap_grey.jpg" alt=""> -->
          <div class="cleardiv">
            <p>
              附件链接 : <a-icon type="link" />
              <a
                target="_blank"
                :href="
                  'http://localhost/document/farm/farmword/信息采集表/' +
                  checked.stnc_xxcjb
                "
                >信息采集表</a
              >
            </p>
          </div>
        </div>
      </div>
      <div class="info" v-if="checked.fzcl">
        <TitleBar :title="'相关辅证材料'"></TitleBar>
        <div class="contain cleardiv" style="margin-bottom: 10px">
          <!-- <img src="../../../../public/images/basemap_grey.jpg" alt=""> -->
          <div>
            <p>
              附件链接 : <a-icon type="link" />
              <a
                target="_blank"
                :href="
                  'http://localhost/document/farm/farmword/附证材料/' +
                  checked.fzcl
                "
                >相关辅证材料</a
              >
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
import TitleBar from "../../Components/TitleBar";
import { mapState } from "vuex";
export default {
  name: "index",

  mixins: [BaseWidget],

  components: {
    TitleBar,
  },

  data() {
    return {
      base: [],
      checked: "",
    };
  },

  computed: {
    ...mapState({
      document: (state) => state.config.api.document,
      img: (state) => state.config.api.img,
    }),
  },

  mounted() {
    this.bus.$on("onBaseClick", this.getBase);
    document.addEventListener("click", () => {
      this.base = [];
    });
  },
  methods: {
    async onSearch(value) {
      this.base = [];
      this.checked = "";

      let params = {
        format: "json",
        returnGeometry: false,
        filter: ["like", "ncmc", value],
      };
      let data = await this.$apis.feature.query("farm_gx", params);
      this.base = data;
    },
    checkedBase(value) {
      this.checked = value;
    },
    getBase(value) {
      let keys = Object.keys(value);
      keys.forEach((item) => {
        if (value[item] == "null") {
          value[item] = null;
        }
      });
      this.checked = value;
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";

.box {
  padding: 0px;
  overflow: hidden;
  .search {
    height: 60px;
    width: 100%;
    background: #45a6ea;
    text-align: center;
    line-height: 50px;
    .query {
      max-height: 400px;
      width: 90%;
      transform: translateX(5%) translateY(-9px);
      overflow-y: scroll;
      overflow-x: hidden;

      font-weight: 500;
      .queryData {
        height: 30px;
        width: 100%;
        border-right: 1px solid #40a9ff;
        border-bottom: 1px solid #40a9ff;
        border-left: 1px solid #40a9ff;
        border-radius: 5px;
        background-color: #fff;
        padding: 5px 10px;
        line-height: 20px;
        text-align: left;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        font-weight: 500;
        cursor: pointer;
      }
    }
  }
  .base {
    width: 100%;
    height: calc(100% - 50px);
    overflow-y: scroll;
    overflow-x: hidden;
    .info {
      width: 100%;
      padding: 12px;

      font-weight: 500;
      .contain {
        width: 100%;
        border: 1px solid #f2f2f2;
        padding: 12px;
        font-size: 16px;
        img {
          width: 150px;
        }
        div {
          // float: right;
          height: 100%;
          width: 100%;
          .nc-title {
            font-size: 24px;
            font-weight: 600;
            color: #002159;
          }
        }
      }
    }
  }
}
</style>
