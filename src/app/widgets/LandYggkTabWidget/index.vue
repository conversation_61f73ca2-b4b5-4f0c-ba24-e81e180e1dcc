<template>
  <div class="jimu-widget wrap box">
    <div class="tab">
      <a-radio-group
        @change="onChange"
        default-value="类别划分"
        v-model="thisTab"
        button-style="solid"
        class="btn-list"
        style="width: 100%"
      >
        <template v-for="(item, index) in tabs">
          <a-radio-button :value="item.value" :key="index" class="btn">
            {{ item.label }}
          </a-radio-button>
        </template>
      </a-radio-group>
    </div>

    <div class="tab-content">
      <component
        :is="thisTab"
        :level="level"
        :mapObj="map"
        :adminCode="adminCode"
        :adminName="adminName"
      ></component>
    </div>
  </div>
</template>

<script>
import Tz from "./child/Tz";
import Cdhj from "./child/Cdhj";
import Lbhf from "./child/Lbhf";
import BaseWidget from "@/BaseWidget";
export default {
  name: "LandYggkTabWidget",
  mixins: [BaseWidget],
  components: { Tz, Cdhj, Lbhf },
  data() {
    return {
      thisTab: "Lbhf",
      tabs: [
        { label: "类别划分", value: "Lbhf", layer: ["nyd_lbhf_aqly"] },
        // {
        //   label: "安全利用措施",
        //   value: "Tz",
        //   layer: ["guangxi_2020ndksstz"],
        // },
        {
          label: "产地环境监测",
          value: "Cdhj",
          layer: ["guangxi_2017njcncpd"],
        },
      ],
      level: 0,
      adminCode: "45",
      adminData: null,
      adminName: "广西",
    };
  },
  mounted() {
    this.bus.$on(
      this.$events.admin.CHANGE_ADMIN,
      this.adminFilterUpdateHandler
    );
    this.setModuleLayerFilter("nyd_lbhf_yggk", "base-stylet", [
      "==",
      ["get", "zh_class"],
      3,
    ]);
    this.setModuleLayerFilter("nyd_tz_layer", "base-stylet", [
      "==",
      ["get", "zh_class"],
      3,
    ]);
    this.setModuleLayerFilter("nyd_cdhj_yggk", "base-stylet", [
      "==",
      ["get", "class"],
      "严格管控类",
    ]);
  },
  methods: {
    onChange(e) {
      const item = this.tabs.find((item) => {
        return item.value == e.target.value;
      });
      this.bus.$emit(this.$events.layer.CHECKED_LAYER, item.layer);
    },
    adminFilterUpdateHandler(e) {
      if (e) {
        this.adminName = e.name;
        this.level = e.level;
        this.adminData = e;
        this.adminCode = e.code;
      } else {
        this.adminName = "广西";
        this.level = 0;
        this.adminData = null;
        this.adminCode = "45";
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";
.wrap {
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow-y: auto;
  overflow-x: hidden;
  .tab {
    width: 100%;
    margin-top: 20px;
    display: flex;
    /deep/.ant-radio-button-wrapper-checked {
      background-color: @primaryColor;
      border-color: @primaryColor;
    }
  }
  .tab-content {
    flex: 1;
    width: 100%;
  }
}
.box {
  box-shadow: 0px 3px 10px 0px rgba(56, 139, 201, 0.3);
  font-size: 21px;
}

.btn-list {
  display: flex;
  margin: 0 15px;
  height: 40px !important;
  .btn {
    flex: 1;
    height: 100%;
    line-height: 40px;
    text-align: center;
    font-size: 18px;
  }
}
</style>
