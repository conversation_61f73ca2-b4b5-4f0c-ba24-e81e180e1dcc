<template>
  <div class="wrap">
    <div class="search">
      <div style="width: 70px">时期:</div>
      <a-select
        :default-value="selectValue"
        style="width: 100%"
        @change="handleChange"
      >
        <a-select-option
          v-for="(item, index) in selectArr"
          :key="index"
          :value="item.value"
        >
          {{ item.label }}
        </a-select-option>
      </a-select>
    </div>
    <div class="cont-box" v-if="select">
      <div class="label">{{ adminName }}{{ select.label }}数量</div>

      <div class="value">
        <span style="font-size: 18px; font-weight: 550; color: rgb(179, 0, 0)">
          严格管控类耕地内
        </span>
        <span class="val">{{ queryAllDwData["point_num"] }}</span>
        <span class="unit">个</span>
      </div>
    </div>
    <div class="filter">
      质量类别:
      <div
        v-for="(item, index) in btnList"
        :style="{ 'background-color': item.active ? item.color : '#ccc' }"
        :key="index"
        @click="filterLevelLayer(item)"
      >
        {{ item.label }}
      </div>
    </div>
    <div class="allArea cleardiv">
      <p style="font-size: 18px; font-weight: 550; color: rgb(179, 0, 0)">
        严格管控类耕地内农产品合格率
      </p>
      <div class="lb">
        <p>合格率</p>
        <p class="num hgl">
          {{ parseFloat(parseFloat(queryAllDwData.point_rate).toFixed(2))
          }}<span class="unit">%</span>
        </p>
      </div>
      <div class="lb">
        <p>合格</p>
        <p class="num hg">
          {{ parseFloat(parseFloat(queryAllDwData.point_qualified))
          }}<span class="unit">个</span>
        </p>
      </div>
      <div class="lb">
        <p>不合格</p>
        <p class="num bhg">
          {{ parseFloat(parseFloat(queryAllDwData.point_defective))
          }}<span class="unit">个</span>
        </p>
      </div>
    </div>
    <!-- 农产品统计 -->
    <div class="allArea cleardiv">
      <p style="font-size: 18px; font-weight: 550; color: rgb(179, 179, 0)">
        主要农产品数量统计
      </p>
      <div class="top-label">
        <p>玉米</p>
        <p>水稻</p>
      </div>
      <div class="lb" style="width: 25%">
        <p>合格率</p>
        <p class="num hg">{{ ncpData.ym.rate }}<span class="unit">%</span></p>
      </div>
      <div class="lb" style="width: 25%">
        <p>数量</p>
        <p class="num hgl">
          {{ ncpData.ym.total }}<span class="unit">个</span>
        </p>
      </div>
      <div class="lb" style="width: 25%">
        <p>合格率</p>
        <p class="num hg">{{ ncpData.sd.rate }}<span class="unit">%</span></p>
      </div>
      <div class="lb" style="width: 25%">
        <p>数量</p>
        <p class="num hgl">
          {{ ncpData.sd.total }}<span class="unit">个</span>
        </p>
      </div>
    </div>

    <div style="width: 100%">
      <TitleBar :title="`${adminName}${select.label}数量统计`"></TitleBar>
    </div>

    <div class="chart-wrap">
      <Chart ref="chart" :options="option" />
    </div>
  </div>
</template>

<script>
import Chart from "@/components/Chart";
import TitleBar from "../../../../Components/TitleBar";
import BaseWidget from "@/BaseWidget.js";
import _ from "lodash";

export default {
  name: "AqlyCdhj",
  components: { Chart, TitleBar },
  mixins: [BaseWidget],
  props: {
    level: {
      type: Number,
      default: 0,
    },
    adminCode: {
      type: String,
    },
    adminName: {
      type: String,
    },
  },

  data() {
    return {
      selectArr: [
        // {
        //   label: "全部定位预警监测农产品",
        //   value: "all,定位预警监测",
        // },
        {
          label: "2017年定位预警监测农产品",
          value: "2017,定位预警监测",
        },
        {
          label: "2018年定位预警监测农产品",
          value: "2018,定位预警监测",
        },
        {
          label: "2019年定位预警监测农产品",
          value: "2019,定位预警监测",
        },
        {
          label: "2020年定位预警监测农产品",
          value: "2020,定位预警监测",
        },
        {
          label: "2021年定位预警监测农产品",
          value: "2021,定位预警监测",
        },
        {
          label: "2022年定位预警监测农产品",
          value: "2022,定位预警监测",
        },
        {
          label: "2020自证采样农产品",
          value: "2020,自证农产品",
        },
        {
          label: "2021自证采样农产品",
          value: "2021,自证农产品",
        },
        {
          label: "2022自证采样农产品",
          value: "2022,自证农产品",
        },
        {
          label: "2023自证采样农产品",
          value: "2023,自证农产品",
        },
      ],
      selectValue: "2017,定位预警监测",
      filterValue: [],
      select: {},
      queryAllDwData: {},
      ncpData: null,
      queryDqDwData: [],
      levelParam: ["PXZQDM", "CXZQDM", "FXZQDM", "TXZQDM", "XZQDM", "XZQDM"],
      levelRegion: ["city", "city", "county", "town", "village", "village"],
      levelName: ["PXZQMC", "CXZQMC", "FXZQMC", "TXZQMC", "XZQMC", "XZQMC"],

      btnList: [
        {
          label: "合格",
          value: 1,
          active: true,
          color: "rgb(0,179,0)",
        },
        {
          label: "不合格",
          value: 2,
          active: true,
          color: "rgb(179,179,0)",
        },
      ],
    };
  },
  computed: {
    option() {
      return {
        legend: {
          show: false,
        },
        xAxis: {
          position: "top",
          type: "value",
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        yAxis: {
          type: "category",
          data: this.queryDqDwData.map((item) => {
            return item[this.levelName[this.level + 1]];
          }),
          axisLabel: {
            show: true,
            textStyle: {
              fontSize: 15, //更改坐标轴文字大小
            },
          },
          formatter: function (params) {
            return params.slice(0, 3) + "...";
          },
        },

        grid: {
          left: "17%",
          right: "4%",
          top: "20px",
          bottom: "5%",
        },

        series: [
          {
            name: "合格",
            data: this.queryDqDwData.map((item) => {
              return item.point_qualified;
            }),
            type: "bar",
            stack: "total",
            barWidth: 15,
            color: "rgb(0,179,0)",
          },
          {
            name: "不合格",
            data: this.queryDqDwData.map((item) => {
              return item.point_defective;
            }),
            type: "bar",
            stack: "total",
            barWidth: 15,
            color: "rgb(179,179,0)",
          },
        ],
      };
    },
  },
  watch: {
    adminCode: {
      handler() {
        this.getAllDwData();
        this.getNcpData();
        this.getDwData();
      },
    },
    // selectValue: {
    //   handler() {
    //     this.getAllDwData();
    //     this.getDwData();
    //     this.filterDateLayer();
    //   },
    // },
  },
  methods: {
    handleChange(e) {
      const value = e.split(",");
      const date = value[0] == "all" ? null : value[0];
      const type = value[1];
      this.select = this.selectArr.find((item) => {
        return item.value == e;
      });

      this.filterValue = [];
      console.log("type", type);
      if (date) this.filterValue.push(["=", "date", Number(date)]);
      if (type) this.filterValue.push(["=", "dwlx", type]);
      this.selectValue = e;
      this.getAllDwData();
      this.getNcpData();
      this.getDwData();
      this.filterDateLayer();
    },
    async getNcpData() {
      const filters = _.cloneDeep(this.filterValue);
      let filter = ["all", ...filters, ["=", "class", "严格管控类"]];
      this.level == 0
        ? filter.push(["=", "PXZQDM", "45"])
        : filter.push(["=", this.levelParam[this.level], this.adminCode]);
      let params = {
        returnGeometry: false,
        format: "json",
        filter,
        aggregates: [["_id", "count"]],
        groupFields: ["ncplx", "zhn_class"],
      };
      let res = await this.$apis.feature.aggregate("land_lnjcd_ncp", params);
      let data = {
        ym: {
          rate: 0,
          total: 0,
          count: 0,
        },
        sd: {
          rate: 0,
          total: 0,
          count: 0,
        },
      };
      res.forEach((item) => {
        if (item.ncplx == "YM") {
          data.ym.total += item._id_count;
          if (item.zhn_class == "1") {
            data.ym.count += item._id_count;
          }
        }
        if (item.ncplx == "SD" || item.ncplx == "DG") {
          data.sd.total += item._id_count;
          if (item.zhn_class == "1") {
            data.sd.count += item._id_count;
          }
        }
      });
      data.ym.rate =
        data.ym.count == 0
          ? 0
          : parseFloat(((data.ym.count / data.ym.total) * 100).toFixed(2));
      data.sd.rate =
        data.sd.count == 0
          ? 0
          : parseFloat(((data.sd.count / data.sd.total) * 100).toFixed(2));
      this.ncpData = data;
      console.log("请求的数据", res);
    },
    //总点位数据
    async getAllDwData() {
      const filters = _.cloneDeep(this.filterValue);
      let dateFilter = filters.find((item) => {
        return item[1] == "date";
      });
      if (!dateFilter) filters.push(["=", "date", "全部"]);
      let filter = ["all", ...filters, ["=", "class", "严格管控类"]];
      this.level == 0
        ? filter.push(["=", "CXZQDM", "4500"])
        : filter.push(["=", this.levelParam[this.level], this.adminCode]);
      let params = {
        returnGeometry: false,
        format: "json",
        filter,
      };
      let res = await this.$apis.feature.query(
        `land_dw_jcn_${this.levelRegion[this.level]}`,
        params
      );
      this.queryAllDwData = res[0];
      if (!this.queryAllDwData) {
        this.queryAllDwData = {
          point_defective: 0,
          point_num: 0,
          point_qualified: 0,
          point_rate: 0,
        };
      }
    },
    //echart图表数据
    async getDwData() {
      this.queryDqDwData = [];
      // 根据selectValue获取相应数据

      const filters = _.cloneDeep(this.filterValue);
      let dateFilter = filters.find((item) => {
        return item[1] == "date";
      });
      if (!dateFilter) filters.push(["=", "date", "全部"]);
      let filter = [
        "all",
        ...filters,
        ["=", this.levelParam[this.level], this.adminCode],
        ["=", "class", "严格管控类"],
      ];
      let param = {
        returnGeometry: false,
        format: "json",
        filter,
      };
      let res = await this.$apis.feature.query(
        `land_dw_jcn_${this.levelRegion[this.level + 1]}`,
        param
      );

      if (res.length == 0) {
        // this.option.yAxis.data = ['暂无数据']
        // this.option.series[0].data = [0]
        // this.option.series[1].data = [0]
      } else {
        if (this.level == 0)
          this.queryDqDwData = res.filter((item) => {
            return parseInt(item.CXZQDM) != 4500;
          });
        else this.queryDqDwData = res;
        //排序
        // this.queryDqDwData = this.queryDqDwData.sort((a, b) => {
        //   return a.point_num - b.point_num
        // })
        this.filterLevelLayer();
      }
    },
    //过滤图层
    filterLevelLayer(item) {
      if (item) {
        item.active = !item.active;
        let layerFilter = ["all"];
        this.btnList.forEach((item) => {
          if (item.active) return;
          layerFilter.push(["!=", ["get", "zhn_class"], item.value]);
        });
        layerFilter =
          layerFilter.length == 1
            ? null
            : layerFilter.length == 2
            ? layerFilter[1]
            : layerFilter;
        this.setModuleLayerFilter("nyd_cdhj_yggk", "zhn_class", layerFilter);
        this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "nyd_cdhj_yggk");
      }
      let thisType = this.btnList.filter((item) => {
        return item.active;
      });
      if (thisType.length == 0 || thisType.length == 2) {
        this.queryDqDwData = this.queryDqDwData.sort((a, b) => {
          return a.point_num - b.point_num;
        });
      } else {
        if (thisType[0].label == "合格") {
          this.queryDqDwData = this.queryDqDwData.sort((a, b) => {
            return a.point_qualified - b.point_qualified;
          });
        }
        if (thisType[0].label == "不合格") {
          this.queryDqDwData = this.queryDqDwData.sort((a, b) => {
            return a.point_defective - b.point_defective;
          });
        }
      }
      setTimeout(() => {
        this.btnList.forEach((item) => {
          this.$refs.chart.chart.dispatchAction({
            type: item.active ? "legendSelect" : "legendUnSelect",
            // 图例名称
            name: item.label,
          });
        });
      }, 100);
    },
    filterDateLayer() {
      this.setModuleLayerFilter("nyd_cdhj_yggk", "date", null);
      this.filterValue.forEach((item) => {
        let filter = ["==", ["get", item[1]], item[2]];
        this.setModuleLayerFilter("nyd_cdhj_yggk", item[1], filter);
      });
      this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "nyd_cdhj_yggk");
    },
  },
  mounted() {
    this.handleChange(this.selectValue);
  },
};
</script>

<style lang="less" scoped>
.wrap {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  padding-top: 30px;
  padding: 12px;
  .search {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }
  .cont-box {
    width: 100%;
    background-color: #e6eaea;
    padding: 12px;
    .label {
      font-size: 18px;
      font-weight: 550;
    }
    .value {
      margin-top: 12px;
      text-align: left;
      .val {
        font-size: 30px;
        font-weight: 650;
        color: rgb(179, 0, 0);
      }
      .unit {
        margin-left: 10px;
      }
    }
  }
  .filter {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    margin-top: 10px;
    cursor: pointer;
    div {
      font-size: 21px;
      text-align: center;
      padding: 1px 10px;
      color: #fff;
      font-weight: 500;
    }
  }
  .allArea {
    width: 100%;
    padding: 12px;
    background-color: rgb(242, 242, 242);
    margin-bottom: 10px;
    .top-label {
      display: flex;
      align-items: center;
      p {
        flex: 1;
        text-align: center;
        font-weight: bold;
        margin-bottom: 10px;
      }
    }
    .lb {
      float: left;
      width: 33%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      font-weight: 600;
      p {
        margin-bottom: 5px;
      }
      .num {
        font-size: 24px;
        font-weight: 600;
      }
      .unit {
        font-size: 17px;
      }
      .hgl {
        color: rgb(67, 138, 232);
      }
      .hg {
        color: rgb(80, 174, 51);
      }
      .bhg {
        color: rgb(181, 176, 57);
      }
    }
  }
  .chart-wrap {
    width: 100%;
    flex: 1;
  }
}
</style>
