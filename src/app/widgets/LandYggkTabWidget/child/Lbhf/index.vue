<template>
  <div class="wrap">
    <div class="cont-box">
      <div class="label">{{ adminName }}严格管控类耕地面积</div>
      <div class="value">

        <span class="val">{{ this.$utils.number.bigDataFormat(analysisValue,10000,1,['亩','万亩']).value }}</span>
        <span class="unit">{{ this.$utils.number.bigDataFormat(analysisValue,10000,1,['亩','万亩']).unit }}</span>
      </div>
    </div>
    <div style="width:100%;margin-top:10px;padding-left:20px">
      <TitleBar :title="`${adminName}严格管控类耕地面积统计`"></TitleBar>
    </div>
    <div class="chart-wrap">
      <Chart :options="option" />
    </div>
  </div>
</template>

<script>
const analysisUris = [
  'land_plough_city',
  'land_plough_city',
  'land_plough_county',
  'land_plough_town',
  'land_plough_village'
]
const levelParam = [
  { code: 'PXZQDM', name: 'PXZQMC' },
  { code: 'CXZQDM', name: 'CXZQMC' },
  { code: 'FXZQDM', name: 'FXZQMC' },
  { code: 'TXZQDM', name: 'TXZQMC' },
  { code: 'XZQDM', name: 'XZQMC' }
]
import BaseWidget from '@/BaseWidget'
import Chart from '@/components/Chart'
import TitleBar from '@/app/Components/TitleBar/'
export default {
  mixins: [BaseWidget],
  props: {
    level: {
      type: Number,
      default: 0
    },
    adminCode: {
      type: String
    },
    adminName: {
      type: String
    }
  },
  watch: {
    adminCode() {
      this.getAnalysis()
      this.getChartData()
    }
  },
  data() {
    return {
      analysisValue: '',
      chartData: [],
      field: 'land_yggk',
      unit: '亩'
    }
  },
  computed: {
    option() {
      return {
        xAxis: {
          type: 'value',
          position: 'top',
          axisLabel:{
            interval:1
          }
        },
        tooltip: {
          // formatter: function (params) {
          //   return params.value + this.level>1?'亩':'万亩'
          // }
          formatter:'{b}:{c}'+(this.level>1?'亩':'万亩')
        },
        yAxis: {
          type: 'category',
          data: this.chartData.map(item => {
            return item[levelParam[this.level == 4 ? 4 : this.level + 1].name]
          }),
          axisLabel: {
            show: true,
            textStyle: {
              fontSize : 15     //更改坐标轴文字大小
            }
          },
          formatter: function (params) {
            return params.slice(0, 3) + '...'
          }
        },
        grid: {
          left: '17%',
          right: '4%',
          top: '20px',
          bottom: "5%"
        },
        series: [
          {
            data: this.level>1?this.chartData.map(item => {
              return Number(item.land_yggk).toFixed(2)
            }):this.chartData.map(item => {
              return (Number(item.land_yggk)/10000).toFixed(2)
            }),
            type: 'bar',
            showBackground: true,
            backgroundStyle: {
              color: 'rgba(180, 180, 180, 0.2)'
            },
            itemStyle: {
              color: 'rgb(164, 41, 22)'
            },
            barWidth:15
          }
        ]
      }
    }
  },
  components: { Chart ,TitleBar},
  mounted() {
    this.getAnalysis()
    this.getChartData()
  },
  methods: {
    async getAnalysis() {
      const filter = this.getAnalysisFilter()

      let param = {
        returnGeometry: false,
        format: 'json',
        outFields: [this.field],
        filter
      }
      let res = await this.$apis.feature.query(analysisUris[this.level], param)
      this.analysisValue = res[0][this.field]
    },
    async getChartData() {
      const nextIndex = this.getNextLevel()
      const filter = this.getChartFilter()
      let param = {
        returnGeometry: false,
        format: 'json',
        outFields: [
          this.field,
          levelParam[nextIndex].code,
          levelParam[nextIndex].name
        ],
        filter: ["all", ["=", "date", "2020"], filter],
      }
      let res = await this.$apis.feature.query(analysisUris[nextIndex], param)
      res = res.sort((a, b) => {
        return a[this.field] - b[this.field]
      })
      this.chartData = res
    },
    getNextLevel() {
      return this.level == 4 ? 4 : this.level + 1
    },
    getAnalysisFilter() {
      return this.adminCode == '45'
        ? ['=', 'CXZQDM', '4500']
        : ['=', levelParam[this.level].code, this.adminCode]
    },
    getChartFilter() {
      if (this.level == 0) {
        return ['!=', 'CXZQDM', '4500']
      } else {
        return ['=', levelParam[this.level].code, this.adminCode]
      }
    }
  }
}
</script>

<style lang="less" scoped>
.wrap {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  padding-top: 30px;
  .cont-box {
    width: calc(100% - 40px);
    background-color: #e6eaea;
    padding: 12px;
    .label {
      font-weight: 550;
    }
    .value {
      margin-top: 12px;
      text-align: center;
      .val {
        font-size: 30px;
        font-weight: 550;
        color: rgb(179, 0, 0);
      }
      .unit{
        margin-left: 10px;
        font-size: 17px;
      }
    }
  }
  .chart-wrap {
    width: 100%;
    flex: 1;
    padding: 20px;
  }
}
</style>