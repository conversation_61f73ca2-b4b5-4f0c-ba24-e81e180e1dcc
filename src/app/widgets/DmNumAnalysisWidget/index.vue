<template>
  <div class="jimu-widget" v-if="isLoad">
    <a-row :gutter="[8, 8]">
      <a-col :span="24" v-for="(item, index) in config" :key="index">
        <Card :title="item.label">
          <!-- <PlainStatistic :value="data[item.field] || ''" unit="个" :color="color" /> -->
        </Card>
      </a-col>
      <a-col :span="24">
        <Card :title="'国控监测点'">
          <PlainStatistic :value="gkjcdCount" unit="个" :color="color" />
        </Card>
        <Card :title="'省控监测点'">
          <PlainStatistic :value="sjjcdCount" unit="个" :color="color" />
        </Card>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
import PlainStatistic from "@/app/Components/PlainStatistic";
import Card from "@/app/Components/Card";

export default {
  name: "DmNumAnalysisWidget",
  mixins: [BaseWidget],
  components: {
    Card,
    PlainStatistic,
  },
  data() {
    return {
      isLoad: false,
      adminCode: "45",
      data: {},
      adminData: null,
      dmCount: null,
      levelParam: ["PXZQDM", "CXZQDM", "FXZQDM"],
      level: 0,
      dmShow: false,
      color: "#4065E0",
      dmJcCount: null,
      dmDcCount: null,
      pmDcCount: null,
      sjjcdCount: null,
      gkjcdCount: null,
      date: 2020,
    };
  },
  configLoaded() {
    this.isLoad = true;
  },
  mounted() {
    this.getDmData();
    this.bus.$on("RegionCHART_UPDATE", this.setFilter);
    this.bus.$on(
      this.$events.admin.CHANGE_ADMIN,
      this.adminFilterUpdateHandler
    );
    this.bus.$on(this.$events.map.LAYER_SHOW, this.layerShowHandler);
    this.bus.$on(this.$events.map.LAYER_HIDE, this.layerHideHandler);
    this.bus.$on("dateChange", (e) => {
      this.date = e;
    });
  },
  watch: {
    date() {
      this.getDmData();
    },
  },
  methods: {
    layerShowHandler(layers) {
      layers.forEach((layer) => {
        if (layer.key == "film_jc_point") {
          this.dmShow = true;
        }
      });
    },
    layerHideHandler(layers) {
      layers.forEach((layer) => {
        if (layer.key == "film_jc_point") {
          this.dmShow = false;
        }
      });
    },
    adminFilterUpdateHandler(e) {
      if (e) {
        this.adminData = e;
        this.adminCode = e.code;
        this.level = e.level;
      } else {
        this.adminData = null;
        this.adminCode = "45";
        this.level = 0;
      }
      this.getData();
      this.getDmData();
    },
    async setFilter(e) {
      this.filters = e.filters;
      this.getData();
    },
    async getData() {
      let filter = ["all", ["=", "PXZQDM", this.adminCode], ...this.filters];

      let params = {
        returnGeometry: false,
        format: "json",
        filter,
      };
      let data = await this.$apis.feature.query("film_gx", params);
      this.data = data;
    },
    async getDmData() {
      this.sjjcdCount = null;
      this.gkjcdCount = null;

      let param = {
        filter: [
          "all",
          ["=", this.levelParam[this.level], this.adminCode],
          ["=", "date", this.date],
          ["=", "dwlx", "省控监测点"],
        ],
        aggregates: [["_id", "count"]],
      };
      let res = await this.$apis.feature.aggregate("film_jc_point", param);
      this.sjjcdCount = res[0]["_id_count"];

      let param1 = {
        filter: [
          "all",
          ["=", this.levelParam[this.level], this.adminCode],
          ["=", "date", this.date],
          ["=", "dwlx", "国控监测点"],
        ],
        aggregates: [["_id", "count"]],
      };
      let res1 = await this.$apis.feature.aggregate("film_jc_point", param1);
      this.gkjcdCount = res1[0]["_id_count"];

      // let param = {
      //   filter: ["all", ["=", this.levelParam[this.level], this.adminCode], ["=", "date", this.date], ["=", "dwlx", "地膜监测点"]],
      //   aggregates: [["_id", "count"]],
      // };
      // let res = await this.$apis.feature.aggregate("film_jc_point", param);
      // this.dmJcCount = res[0]["_id_count"];

      // let param1 = {
      //   filter: ["all", ["=", this.levelParam[this.level], this.adminCode], ["=", "date", this.date], ["=", "dwlx", "地膜调查点"]],
      //   aggregates: [["_id", "count"]],
      // };
      // let res1 = await this.$apis.feature.aggregate("film_jc_point", param1);
      // this.pmDcCount = res1[0]["_id_count"];

      // let param2 = {
      //   filter: ["all", ["=", this.levelParam[this.level], this.adminCode], ["=", "date", this.date], ["=", "dwlx", "棚膜调查点"]],
      //   aggregates: [["_id", "count"]],
      // };
      // let res2 = await this.$apis.feature.aggregate("film_jc_point", param2);
      // this.dmDcCount = res2[0]["_id_count"];
    },
  },
};
</script>

<style lang="less" scoped>
.jimu-widget {
  // background-color: antiquewhite;
  .count-box {
    width: 200px;
    height: 70px;
    display: flex;
    font-size: 18px;
    align-items: center;
    justify-content: space-around;
    color: #002159;
    margin-bottom: 18px;
    background: white;
    border-radius: 3px;
    box-shadow: 0px 3px 10px 0px rgba(56, 139, 201, 0.3);
    img {
      height: 30px;
    }
    .count {
      font-size: 20px;
    }
  }
}

/deep/ .ant-card {
  margin-bottom: 3px;
}
</style>
