export const typeParam = {
  default: {
    tooltip: function (params) {
      var relVal = params[0].name;
      for (var i = 0, l = params.length; i < l; i++) {
        relVal += "<br/>" + params[i].value;
      }
      return relVal;
    },
    unit: "",
  },
  ge: {
    tooltip: function (params) {
      var relVal = params[0].name;
      for (var i = 0, l = params.length; i < l; i++) {
        relVal += "<br/>" + params[i].value + "个";
      }
      return relVal;
    },
    unit: "个",
  },
  rate: {
    tooltip: function (params) {
      var relVal = params[0].name;
      for (var i = 0, l = params.length; i < l; i++) {
        relVal += "<br/>" + parseFloat(params[i].value).toFixed(2) + "%";
      }
      return relVal;
    },
    unit: "%",
  },
  ton: {
    tooltip: function (params) {
      var relVal = params[0].name;
      for (var i = 0, l = params.length; i < l; i++) {
        relVal += "<br/>" + parseFloat(params[i].value).toFixed(2) + "吨";
      }
      return relVal;
    },
    unit: "吨",
  },
  are: {
    tooltip: function (params) {
      var relVal = params[0].name;
      for (var i = 0, l = params.length; i < l; i++) {
        relVal += "<br/>" + parseFloat(params[i].value).toFixed(2) + "亩";
      }
      return relVal;
    },
    unit: "亩",
  },
  wanare: {
    tooltip: function (params) {
      var relVal = params[0].name;
      for (var i = 0, l = params.length; i < l; i++) {
        relVal += "<br/>" + parseFloat(params[i].value).toFixed(2) + "万亩";
      }
      return relVal;
    },
    unit: "万亩",
  },
  tTon: {
    tooltip: function (params) {
      var relVal = params[0].name;
      for (var i = 0, l = params.length; i < l; i++) {
        relVal += "<br/>" + parseFloat(params[i].value).toFixed(2) + "万吨";
      }
      return relVal;
    },
    unit: "万吨",
  },
  none:{
    tooltip: function (params) {
      var relVal = params[0].name;
      for (var i = 0, l = params.length; i < l; i++) {
        relVal += "<br/>" + parseFloat(params[i].value).toFixed(2) ;
      }
      return relVal;
    },
    unit:""
  }
};
