<template>
  <div class="jimu-widget jimu-tool-widget">
    <div class="title">
      {{ adminData ? adminData.name : "广西壮族自治区"
      }}{{
        filter.length
          ? filter
              .map((item) => item[2])
              .filter((item) => item !== "合计")
              .join("")
          : ""
      }}{{ title === "农户总数" ? `${title}（户）` : title }}
    </div>
    <a-radio-group class="btn-list" v-model="index" button-style="solid">
      <a-radio-button value="city"> 市 </a-radio-button>
      <a-radio-button value="county"> 区/县 </a-radio-button>
    </a-radio-group>
    <Chart class="Chart" @barClick="chartBarClick" :options="option"></Chart>
  </div>
</template>

<script>
import { typeParam } from "./config/type.config";
import Chart from "@/components/Chart/index.vue";
import BaseWidget from "@/BaseWidget";

export default {
  name: "RegionAnalysisChartWidget",
  components: { Chart },
  mixins: [BaseWidget],
  data() {
    return {
      index: "city",
      admin: [],
      scope: "city",
      region: "",
      regionCode: "",
      type: "",
      option: {
        textStyle: {
          color: "#000",
        },
        legend: {},
        tooltip: {
          trigger: "axis",
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "30",
          containLabel: true,
        },
        xAxis: {
          axisLabel: {
            showMaxLabel: true,
            interval: 0,
            show: true,
          },
          type: "category",
          data: [],
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            type: "bar",
            data: [],
            itemStyle: {
              color: "#2a45ad",
              normal: {
                label: {
                  show: true, //开启显示
                  position: "top", //在上方显示
                  textStyle: {
                    //数值样式
                    color: "#333",
                    fontSize: 12,
                  },
                  // formatter: (res) => {
                  //   return res.value.toFixed(2);
                  // },
                },
              },
            },
            barMaxWidth: 40,
            barGap: "40%",
          },
        ],
        dataZoom: {
          show: true,
          type: "slider",
          xAxisIndex: [0],
          start: 0,
          height: 25,
          bottom: 0,

          handleSize: 0,
        },
      },
      regionConfig: {
        province: {
          name: "PXZQMC",
          code: "PXZQDM",
        },
        city: {
          name: "CXZQMC",
          code: "CXZQDM",
        },
        county: {
          name: "FXZQMC",
          code: "FXZQDM",
        },
      },
      dataRegionConfig: {
        province: {
          name: "pname",
          code: "pcode",
        },
        city: {
          name: "cname",
          code: "ccode",
        },
        county: {
          name: "qymc",
          code: "qydm",
        },
      },
      adminData: null,
      fields: null,
      filter: [],
      title: null,
      typeParam,
      elIndex: { nextRegion: "city" },
    };
  },
  created() {},
  mounted() {
    this.bus.$on("RegionCHART_UPDATE", this.upDataChange);
    this.bus.$on(this.$events.admin.CHANGE_ADMIN, this.changeAdmin);
  },
  watch: {
    index(e) {
      this.getData();
      this.bus.$emit("ChartLevelChange", e);
    },
    type(e) {
      console.log("tooltip", e);
      this.$set(
        this.option.tooltip,
        "formatter",
        this.typeParam[e ? e : "default"].tooltip
      );
    },
    elIndex(e) {
      this.getData();
      this.bus.$emit("ChartLevelChange", e.nextRegion);
    },
  },
  methods: {
    changeAdmin(e) {
      if (e) {
        this.adminData = e;
        this.region = e.region;
        this.regionCode = e.code;
        this.index = e.nextRegion;
        this.elIndex = e;
      } else {
        this.adminData = "";
        this.region = "";
        this.regionCode = "";
        this.index = "city";
        this.elIndex = { nextRegion: "city" };
      }
      this.getData();
    },
    async getData() {
      let param = {
        format: "json",
        returnGeometry: false,
        orderField: this.fields,
        orderType: "DESC",
        limit: 999,
      };
      let filter = ["all", ...this.filter, ["=", "level", this.index]];
      if (this.regionCode) {
        filter.push([
          "=",
          this.dataRegionConfig[this.region].code,
          this.regionCode,
        ]);
      } else {
        filter.push(["=", "pcode", "45"]);
      }
      if (filter.length == 2) {
        filter = filter[1];
      }
      param.filter = filter;
      // let url = 'straw_data_gx'
      let url = this.config.url;
      let data = await this.$apis.feature.query(url, param);
      // data = data.sort((a, b) => {
      //   return Number(b[this.fields]) - Number(a[this.fields])
      // })
      this.setChartData(data);
    },
    setChartData(data) {
      let nameList = data.map((item) => {
        return item[this.dataRegionConfig[this.index].name];
      });
      this.$set(this.option.xAxis, "data", nameList);
      this.$set(
        this.option.series[0],
        "data",
        data.map((item) => {
          let value = item[this.fields];
          switch (this.type) {
            case "tTon":
            case "wanare":
              value = this.$utils.number.divisor(value, 10000, 2);
              break;
            case "rate":
            case "are":
            case "ton":
              value = parseFloat(value).toFixed(2);
              break;
            case "ge":
              value = parseFloat(value).toFixed(0);
              break;
            default:
              break;
          }

          return value;
        })
      );
      if (data.length > 20) {
        this.$set(this.option.dataZoom, "end", (20 / data.length) * 100);
      } else {
        this.$set(this.option.dataZoom, "end", 100);
      }
    },
    upDataChange(e) {
      this.filter = e.filters;
      this.fields = e.field;
      this.title = e.name;
      this.type = e.type;
      this.getData();
      this.handleTitle();
    },
    // 处理标题
    handleTitle() {
      let title = this.title;
      if (this.type == "tTon") {
        let tTon = title.indexOf("万吨");
        if (tTon > -1) return;
        this.title = title.replace("吨", "万吨");
      } else if (this.type == "wanare") {
        let wanare = title.indexOf("万亩");
        if (wanare > -1) return;
        this.title = title.replace("亩", "万亩");
      }
    },
    async chartBarClick(e) {
      let list = {
        city: "gx_land_region_city",
        county: "gx_land_region_county",
        town: "gx_land_region_town",
        village: "gx_land_region_village",
      };
      let params = {
        returnGeometry: false,
        format: "json",
        filter: ["=", this.regionConfig[this.index].name, e.name],
      };
      let data = await this.$apis.feature.query(list[this.index], params);
      let obj = data[0];
      let listData = [];
      if (obj.CXZQDM) {
        listData.push({ index: 0, codeKey: "CXZQDM", code: obj.CXZQDM });
      }
      if (obj.FXZQDM) {
        listData.push({ index: 1, codeKey: "FXZQDM", code: obj.FXZQDM });
      }
      this.bus.$emit("emit_admin_change", listData);
    },
    onClickChangeArea(val) {
      this.bus.$emit("Area", { Area: val });
    },
  },
};
</script>

<style lang="less" scoped>
.btn-list {
  position: absolute;
  right: 12px;
  top: 8px;
  z-index: 10000;
}
.title {
  font-size: 20px;
  font-weight: 600;
  position: absolute;
  left: 12px;
  top: 8px;
}
</style>
