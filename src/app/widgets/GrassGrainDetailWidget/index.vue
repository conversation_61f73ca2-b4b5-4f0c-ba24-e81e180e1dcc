<template>
  <div class="jimu-widget jimu-tool-widget box" ref="myElement">
    <div class="tabs-content">
      <div :class="['tabs', item == currentTab ? 'active' : '']" v-for="(item, index) in tabs" :key="index" @click="currentTab = item">
        {{ item }}
      </div>
    </div>
    <div class="content" v-show="currentTab == '草谷比监测系数'">
      <div class="part">
        <TitleBar :title="`水稻`"></TitleBar>
        <a-table :columns="tableConfig1" :data-source="tableData1" :scroll="{ x: 500, y: 400 }" :pagination="false"></a-table>
      </div>
      <div class="part">
        <TitleBar :title="`玉米`"></TitleBar>
        <a-table :columns="tableConfig1" :data-source="tableData2" :scroll="{ x: 500, y: 400 }" :pagination="false"></a-table>
      </div>
    </div>

    <div class="content" v-show="currentTab == '秸秆可收集系数'">
      <div class="part">
        <TitleBar :title="`水稻`"></TitleBar>
        <a-table :columns="tableConfig2" :data-source="tableData3" :scroll="{ y: 800 }" :pagination="false"></a-table>
      </div>
    </div>
  </div>
</template>

<script>
import TitleBar from "../../Components/TitleBar";
import BaseWidget from "@/BaseWidget";
import { tableConfig1, tableConfig2 } from "./config";
export default {
  name: "PlantSpeciesJcEchart",

  mixins: [BaseWidget],

  components: {
    TitleBar,
  },

  data() {
    return {
      currentTab: "草谷比监测系数",
      tabs: ["草谷比监测系数", "秸秆可收集系数"],
      adminFilter: ["=", "PXZQDM", "45"],
      adminCode: ["PXZQDM", "CXZQDM", "FXZQDM", "TXZQDM", "VXZQDM"],
      id: "",
      filters: [],
      tableConfig1,
      tableConfig2,
      tableData1: [],
      tableData2: [],
      tableData3: [],
    };
  },

  methods: {
    pointClick(e) {
      console.log("点数据", e);
      if (!e.dwbm) return;
      this.id = e.dwbm;
      this.getData();
    },
    getFilter(e) {
      let filters = [];
      e.forEach((item) => {
        filters.push(["=", item.id, item.value]);
      });
      this.filters = filters;
      this.getData();
    },
    setAdminFilter(e) {
      console.log("region", e);
      if (e) {
        this.adminFilter = ["=", this.adminCode[e.level], e.code];
      } else {
        this.adminFilter = ["=", "PXZQDM", "45"];
      }
      this.getData();
    },
    async getData() {
      if (this.id) this.getDwData();
      this.getSjxsData();
    },
    async getDwData() {
      let filter = [this.adminFilter, ...this.filters, ["=", "dwbm", this.id]];
      if (filter.length > 1) filter.unshift("all");
      if (filter.length == 1) filter = filter[0];
      const params = {
        returnGeometry: false,
        format: "json",
        filter,
      };
      let res = await this.$apis.feature.query("straw_cgb_dk", params);
      this.tableData1 = res.filter((item) => item.zwlx == "水稻");
      this.tableData2 = res.filter((item) => item.zwlx == "玉米");
    },
    async getSjxsData() {
      let filter = [this.adminFilter, ...this.filters];
      if (filter.length > 1) filter.unshift("all");
      if (filter.length == 1) filter = filter[0];
      const params = {
        returnGeometry: false,
        format: "json",
        filter,
      };
      let data = await this.$apis.feature.query("straw_sjxs_pjz", params);
      this.tableData3 = data;
    },
  },
  mounted() {
    this.bus.$on(this.$events.admin.CHANGE_ADMIN, this.setAdminFilter);
    this.bus.$on("strawSJXSSelectDate", this.getFilter);
    this.bus.$on("onBaseClick", this.pointClick);
    this.getData();
  },
};
</script>

<style lang="less" scoped>
.jimu-widget-title-ui {
  line-height: normal;
  height: auto;
}
.box {
  .part {
    width: 100%;
    margin-bottom: 2vh;
    .table {
      margin-bottom: 20px;
    }
  }
}
.tabs-content {
  display: flex;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #e7ebf4;
  overflow: hidden;
  margin-bottom: 2vh;
  .tabs {
    width: 50%;
    height: 40px;
    text-align: center;
    // border: 1px solid #ccc;
    line-height: 40px;
    cursor: pointer;
  }

  .active {
    border-bottom: none;
    // color: rgb(62, 145, 247);
    color: #fff;
    background-color: #4065e0;
  }
}
.content {
  height: calc(100% - 65px);
  display: flex;
  flex-direction: column;
  overflow-y: scroll;
}
</style>
