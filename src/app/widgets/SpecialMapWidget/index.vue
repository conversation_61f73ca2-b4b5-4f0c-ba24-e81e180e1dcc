<!-- 该文件处理<农用地-数据分析-数据统计>的点击显示数据处理，不处理图层分级 -->
<template>
  <div class="jimu-widget map-widget">
    <div :id="mapUid" class="jimu-map"></div>
    <Popup ref="popup" :map="map" />
  </div>
</template>

<script>
import { uid } from "uid";
import axios from "axios";

import mapboxgl from "mapbox-gl";
import "mapbox-gl/dist/mapbox-gl.css";

import { mapState } from "vuex";

import BaseWidget from "@/BaseWidget";
import LayerManager from "@/managers/LayerManager";
import spelialConfig from "./map.config";
import Popup from "./component/Popup";

const MAP_DEFAULT_CENTER = [107, 34];
const MAP_DEFAULT_ZOOM = 3;
const MAP_DEFAULT_MAXZOOM = 18;
const MAP_DEFAULT_MINZOOM = 1;

const FIELD_PARAM = {
  land_area: { unit: "亩", label: "面积" },
  land_yxbh: { unit: "亩", label: "面积" },
  land_aqly: { unit: "亩", label: "面积" },
  land_yggk: { unit: "亩", label: "面积" },
  land_yxbh_ratio: { unit: "%", label: "面积比", type: "ratio" },
  land_aqly_ratio: { unit: "%", label: "面积比", type: "ratio" },
  land_yggk_ratio: { unit: "%", label: "面积比", type: "ratio" },
  cd_cbl: { unit: "%", label: "点位超标率", type: "ratio" },
  hg_cbl: { unit: "%", label: "点位超标率", type: "ratio" },
  as_cbl: { unit: "%", label: "点位超标率", type: "ratio" },
  pb_cbl: { unit: "%", label: "点位超标率", type: "ratio" },
  cr_cbl: { unit: "%", label: "点位超标率", type: "ratio" },
};

export default {
  name: "SpecialMapWidget",

  mixins: [BaseWidget],

  components: { Popup },
  data() {
    return {
      updataParam: null,
      spelialConfig,
      mapUid: `jimu-map-${uid()}`,
      highlightLayerUid: `jimu-layer-${uid()}`,
      extendsHighlightLayerUid: `jimu-layer-${uid()}`,
      map: null,
      layerManager: null,
      layerConfigMap: {},
      index: "city",
      region: "",
      regionCode: null,
      nextRegion: "city",
      regionLayerConfig: [
        {
          region: "city",
          layer: "land_city_fill",
          nameLayer: "land_city_name",
        },
        {
          region: "county",
          layer: "land_county_fill",
          nameLayer: "land_county_name",
        },
        {
          region: "town",
          layer: "land_town_fill",
          nameLayer: "land_town_name",
        },
        {
          region: "village",
          layer: "land_village_fill",
          nameLayer: "land_village_name",
        },
      ],
      regionParam: {
        city: { code: "CXZQDM", name: "CXZQMC" },
        county: { code: "FXZQDM", name: "FXZQMC" },
        town: { code: "TXZQDM", name: "TXZQMC" },
        village: { code: "XZQDM", name: "XZQMC" },
      },
      mapData: null,
    };
  },

  computed: {
    // 从全局配置中获取地图初始状态
    ...mapState({
      initExtent: (state) => ({
        zoom: state.config.map?.initZoom,
        center: state.config.map?.initCenter,
        maxZoom: state.config.map?.maxZoom,
        minZoom: state.config.map?.minZoom,
      }),
      basemaps: (state) => state.config?.basemaps,
      apiUrl: (state) => state.config.api,
      adminData: (state) => state.data?.admin,
    }),
  },

  mounted() {
    this.init();
    this.addListeners();
  },
  activated() {
    if (this.map) {
      this.map.resize();
    }
  },
  watch: {
    index(e) {
      this.regionLayerConfig.forEach((item) => {
        if (item.region == e) {
          this.map.setLayoutProperty(item.layer, "visibility", "visible");
          this.map.setLayoutProperty(item.nameLayer, "visibility", "visible");
        } else {
          this.map.setLayoutProperty(item.layer, "visibility", "none");
          this.map.setLayoutProperty(item.nameLayer, "visibility", "none");
        }
      });
      this.mapUpdateHandler(this.updataParam);
    },
  },

  methods: {
    init() {
      this.map = new mapboxgl.Map({
        container: this.mapUid,
        center: this.initExtent.center || MAP_DEFAULT_CENTER,
        zoom: this.initExtent.zoom || MAP_DEFAULT_ZOOM,
        minZoom: this.initExtent.minZoom || MAP_DEFAULT_MINZOOM,
        maxZoom: this.initExtent.maxZoom || MAP_DEFAULT_MAXZOOM,
        // TODO: 提升地图体验，初始化时不加载任何在线资源
        bounds: [
          [104.446554, 20.902282],
          [112.056814, 26.388535],
        ],
        fitBoundsOptions: {
          padding: { left: 0, top: 0, right: 400, bottom: 0 },
        },
        style: "config/style/baseMapStyle.json",
      });
      // 初始化LayerManager
      this.layerManager = new LayerManager(this.map);
      // 分发地图实例
      this.map.on("load", () => {
        this.bus.$emit(this.$events.map.MAP_LOAD, this.map);
        window.map = this.map;
      });
    },

    getBasemapStyle() {
      if (!this.basemaps || !Array.isArray(this.basemaps)) return;
      const defaultBasemaps = this.basemaps.filter((basemap) => basemap.default);
      const basemap = defaultBasemaps.length > 0 ? defaultBasemaps[0] : this.basemaps[0];
      return this.genBasemapStyle(basemap);
    },

    // 获取或者生成底图的样式
    genBasemapStyle(basemap) {
      if (!basemap) return;
      if (basemap.type == this.$constants.layerTypes.VECTOR) {
        return basemap.url;
      } else if (basemap.type == this.$constants.layerTypes.RASTER) {
        const id = `jimu-basemap-${uid()}`;
        return {
          version: 8,
          sources: {
            [id]: {
              type: "raster",
              tiles: [basemap.url],
              tileSize: basemap.tileSize || 256,
            },
          },
          layers: [
            {
              id,
              type: "raster",
              source: id,
            },
          ],
        };
      }
    },

    addListeners() {
      this.bus.$on(this.$events.map.LAYER_SHOW, this.layerShowHandler);
      this.bus.$on(this.$events.map.LAYER_HIDE, this.layerHideHandler);
      this.bus.$on(this.$events.admin.CHANGE_ADMIN, this.adminFilterUpdateHandler);
      this.bus.$on("ChartLevelChange", this.changeLevel);
      this.bus.$on(this.$events.special.CHART_UPDATE, (e) => {
        this.updataParam = e;
        this.mapUpdateHandler(e);
      });
      this.bus.$on(this.$events.map.SET_LAYER_FILTER, this.setLayerFilter);
      this.bus.$on(this.$events.map.SET_LAYER_STYLE, this.setLayerStyle);
    },
    changeLevel(e) {
      this.index = e;
      this.mapUpdateHandler(this.updataParam);
    },
    adminFilterUpdateHandler(adminData) {
      if (adminData) {
        if (adminData.bounds) {
          this.map.fitBounds(adminData.bounds, {
            padding: { left: 100, top: 100, right: 400, bottom: 100 },
          });
        }
        if (adminData.region) {
          this.region = adminData.region;
          this.regionCode = adminData.code;
        }
        this.map.setFilter(
          this.regionLayerConfig.find((item) => {
            return item.region == adminData.nextRegion;
          }).layer,
          ["==", this.regionParam[this.region].code, adminData.code]
        );
        this.map.setFilter(
          this.regionLayerConfig.find((item) => {
            return item.region == adminData.nextRegion;
          }).nameLayer,
          ["==", this.regionParam[this.region].code, adminData.code]
        );
      } else {
        this.map.flyTo(this.initExtent);
        this.region = "";
        this.regionCode = "";
        this.map.setFilter(
          this.regionLayerConfig.find((item) => {
            return item.region == this.index;
          }).layer,
          null
        );
        this.map.setFilter(
          this.regionLayerConfig.find((item) => {
            return item.region == this.index;
          }).nameLayer,
          null
        );
      }
      const layerFilter = this.getModuleData(this.$constants.shareDataKey.LAYER_FILTER);
      // const drawFeature = this.getModuleData(
      //   this.$constants.shareDataKey.GEOMETRY_FILTER
      // )
      // const geometryFilter = drawFeature ? drawFeature.geometry : null
      const regionFilter = this.getRagionFilter();
      this.layerManager.refreshAllLayers(null, regionFilter, layerFilter);
    },

    async mapUpdateHandler(e) {
      console.log(e, "------e");
      if (!e) return;
      let params = {
        returnGeometry: false,
        format: "json",
        outFields: [this.regionParam[this.index].code, e[2], this.regionParam[this.index].name],
      };
      params.filter = ["all", ...e[1]];
      if (this.region) {
        params.filter.push(["=", this.regionParam[this.region].code, this.regionCode]);
      }
      if (params.filter.length == 2) {
        params.filter = params.filter[1];
      }
      try {
        let data = await this.$apis.feature.query(this.spelialConfig.apiConfig[e[0]][this.index], params);
        if (!data) return;
        if (data.length == 0) {
          this.$message.warning("当前选项下未查询到数据");
          return;
        }
        this.mapData = data;
      } catch (error) {
        console.log(error, "error");
      }
    },

    // 显示图层
    layerShowHandler(layers) {
      if (!layers || !Array.isArray(layers)) return;
      layers.forEach(async (layer) => {
        let layerConfig = this.layerConfigMap[layer.key];
        // 获取图层配置信息
        if (!layerConfig) {
          layerConfig = this.layerConfigMap[layer.key] = await this.getLayerConfig(layer);
        }
        // 获取空间过滤geometry
        if (layerConfig.region == this.index) {
          layerConfig.style.layout.visibility = "visible";
        }
        const layerStyleData = this.getModuleLayerStyle(layer.key);
        const regionFilter = this.getRagionFilter();
        const filterData = this.getModuleLayerFilter(layer.key);
        const newLayer = this.layerManager.showLayer(layerConfig, null, regionFilter, filterData, layerStyleData);
        // 对新增图层添加点击事件响应
        if (newLayer && layerConfig.type !== this.$constants.layerTypes.RASTER) {
          this.addLayerClickHandler(layerConfig);
        }
        // 打开图例
        if (layerConfig.legend) {
          this.bus.$emit(this.$events.map.LEGEND_SHOW, {
            key: layerConfig.key,
            legend: layerConfig.legend,
            orderWeight: layerConfig.orderWeight,
          });
        }
      });

      this.mapUpdateHandler(this.updataParam);
      //
      this.clearMap();
    },

    addLayerClickHandler(layerConfig) {
      this.map.on("dblclick", layerConfig.key, (e) => {
        let obj = e.features[0].properties;
        let list = [];
        if (obj.CXZQDM) {
          list.push({ index: 0, codeKey: "CXZQDM", code: obj.CXZQDM });
        }
        if (obj.FXZQDM) {
          list.push({ index: 1, codeKey: "FXZQDM", code: obj.FXZQDM });
        }
        if (obj.TXZQDM) {
          list.push({ index: 2, codeKey: "TXZQDM", code: obj.TXZQDM });
        }
        if (obj.XZQDM) {
          list.push({ index: 3, codeKey: "XZQDM", code: obj.XZQDM });
        }
        this.bus.$emit("emit_admin_change", list);
      });
      this.map.on("click", layerConfig.key, (e) => {
        const { code, name } = this.regionParam[this.index];
        const res = this.mapData.find((item) => {
          return item[code] == e.features[0].properties[code];
        });
        const popupData = {
          title: name,
          fields: [
            {
              label: FIELD_PARAM[this.updataParam[2]].label,
              field: this.updataParam[2],
              unit: FIELD_PARAM[this.updataParam[2]].unit,
              type: FIELD_PARAM[this.updataParam[2]].type,
            },
          ],
        };
        this.showPopup(e.lngLat, res, popupData);
      });
    },
    showPopup(lngLat, feature, popupData) {
      this.$refs.popup.show(lngLat, feature, popupData);
    },

    async getLayerConfig(layer) {
      if (!layer || !layer.key || !layer.config) return;
      layer = this.$utils.object.deepCloneJson(layer);
      const resp = await axios.get(layer.config);
      if (layer.extend) {
        let extend = await axios.get(layer.extend);
        resp.data = Object.assign(resp.data, extend.data);
      }
      let config = resp.data;
      config.url = this.genLayerUrl(config);
      Object.assign(layer, config);
      return layer;
    },

    genLayerUrl(layer) {
      if (!layer || !layer.type) return;
      switch (layer.type) {
        case this.$constants.layerTypes.VECTOR:
          return `${this.apiUrl?.vector}/${layer.name}/{z}/{x}/{y}.vector.pbf`;
        case this.$constants.layerTypes.RASTER:
          return `${this.apiUrl?.raster}/${layer.name}/{z}/{x}/{y}.png`;
        case this.$constants.layerTypes.FEATURE:
          return `${this.apiUrl?.feature}`;
      }
    },

    // 隐藏图层
    layerHideHandler(layers) {
      if (!layers || !Array.isArray(layers)) return;
      layers.forEach((layer) => {
        this.layerManager.hideLayer(layer);
        // 关闭图层
        const layerConfig = this.layerConfigMap[layer.key];
        if (layerConfig.legend) {
          this.bus.$emit(this.$events.map.LEGEND_HIDE, layer.key);
        }
      });
      //
      this.clearMap();
    },

    getRagionFilter() {
      const regionData = this.getModuleData(this.$constants.shareDataKey.REGION_FILTER);
      if (regionData && regionData.region) {
        let region = this.config.regionWidget[regionData.region];

        const regionFilter = region.slice
          ? ["==", ["slice", ["get", region.keyCode], 0, regionData.code.length], regionData.code]
          : ["==", ["get", region.keyCode], regionData.code];
        return regionFilter;
      } else {
        return null;
      }
    },
    setLayerFilter(name) {
      const layerItem = this.layerConfigMap[name];
      const filterData = this.getModuleLayerFilter(name);
      // const drawFeature = this.getModuleData(
      //   this.$constants.shareDataKey.GEOMETRY_FILTER
      // )
      // const geometryFilter = drawFeature ? drawFeature.geometry : null

      const regionFilter = this.getRagionFilter();
      if (layerItem) {
        this.layerManager.refreshLayer(layerItem, null, regionFilter, filterData);
      }
    },
    setLayerStyle(name) {
      const layerItem = this.layerConfigMap[name];
      const layerStyleData = this.getModuleLayerStyle(name);
      if (layerItem) {
        this.layerManager.refreshLayerStyle(layerItem, layerStyleData);
      }
    },
    // }
  },
};
</script>

<style lang="less" scoped>
.jimu-map {
  width: 100%;
  height: 100%;
}
</style>
