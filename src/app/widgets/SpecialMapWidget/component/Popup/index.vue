<template>
  <div id="jimu-popup-container">
    <div class="title" v-show="title">{{ title }}</div>
    <a-icon class="btn-popup-close" type="close" @click="closeHandler" />
    <div class="item" v-for="(item, index) in items" :key="index">
      <span class="label">{{ item.label }}</span> :
      <span class="value">{{ item.value || '' }} {{ item.unit }}</span>
    </div>
  </div>
</template>

<script>
import mapboxgl from 'mapbox-gl'

// import DefaultPopupContent from './components/DefaultPopupContent'

export default {
  name: 'Popup',

  props: {
    map: {
      type: Object
    }
  },

  data() {
    return {
      title: null,
      items: []
    }
  },

  mounted() {
    var div = window.document.getElementById('jimu-popup-container')
    this.popup = new mapboxgl.Popup({
      closeOnClick: false,
      closeButton: false
    }).setDOMContent(div)
  },

  methods: {
    async show(lngLat, feature, popupData) {
      // this.content = {
      //   component: module,
      //   data: data.items
      // }
      this.title = feature[popupData.title]
      this.items = popupData.fields.map(item => {
        let obj = {
          label: item.label,
          value: feature[item.field],
          unit: item.unit
        }
        if (item.type == 'ratio') {
          obj.value = obj.value * 100
        }
        obj.value=Number(obj.value).toFixed(2)
        return obj
      })
      this.popup.setLngLat(lngLat).addTo(this.map)
    },

    close() {
      if (this.popup.isOpen()) {
        this.popup.remove()
      }
    },

    closeHandler() {
      this.popup.remove()
      this.$emit('close')
    }
  }
}
</script>


<style lang="less" scoped>
#jimu-popup-container {
  min-width: 100px;
  max-width: 800px;

  .title {
    font-size: 18px;
    font-weight: bold;
    padding: 0 8px 4px 0;
  }

  .item {
    font-size: 14px;
    padding: 2px 0 2px 0;
  }

  .btn-popup-close {
    position: absolute;
    right: 6px;
    top: 6px;
  }
}
</style>