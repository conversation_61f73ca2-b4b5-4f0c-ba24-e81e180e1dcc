export default {
  apiConfig: {
    area: {
      city: 'land_plough_city',
      county: 'land_plough_county',
      town: 'land_plough_town',
      village: 'land_plough_village'
    },
    area_ratio: {
      city: 'land_plough_city',
      county: 'land_plough_county',
      town: 'land_plough_town',
      village: 'land_plough_village'
    },
    cbl: {
      city: 'land_tr_dw_city',
      county: 'land_tr_dw_county',
      town: 'land_tr_dw_town',
      village: 'land_tr_dw_village'
    },
    ncp_cbl: {
      city: 'land_ncp_dw_city',
      county: 'land_ncp_dw_county',
      town: 'land_ncp_dw_town',
      village: 'land_ncp_dw_village'
    },
  },
  colors: {
    land_aqly: ["rgb(255, 225, 105)",
      "rgb(250, 214, 67)",
      "rgb(237, 197, 49)",
      "rgb(219, 180, 44)",
      "rgb(201, 162, 39)",],
    land_yxbh: ["rgb(216, 243, 220)",
      "rgb(183, 228, 199)",
      "rgb(149, 213, 178)",
      "rgb(116, 198, 157)",
      "rgb(64, 145, 108)",],
    land_yggk: ["rgb(255, 227, 224)",
      "rgb(251, 195, 188)",
      "rgb(247, 163, 153)",
      "rgb(243, 131, 117)",
      "rgb(239, 99, 81)",],
    land_aqly_ratio: ["rgb(255, 225, 105)",
      "rgb(250, 214, 67)",
      "rgb(237, 197, 49)",
      "rgb(219, 180, 44)",
      "rgb(201, 162, 39)",],
    land_yxbh_ratio: ["rgb(216, 243, 220)",
      "rgb(183, 228, 199)",
      "rgb(149, 213, 178)",
      "rgb(116, 198, 157)",
      "rgb(64, 145, 108)"],
    land_yggk_ratio: ["rgb(255, 227, 224)",
      "rgb(251, 195, 188)",
      "rgb(247, 163, 153)",
      "rgb(243, 131, 117)",
      "rgb(239, 99, 81)"],

  }
}