<template>
  <div class="jimu-widget">
    <div v-if="isLoad">
      <Card
        class="card"
        v-for="(item, index) in config"
        :key="index"
        :title="item.name"
      >
        <PlainStatistic :value="item.count" unit="个" :color="color" />
      </Card>
    </div>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
import PlainStatistic from "@/app/Components/PlainStatistic";
import Card from "@/app/Components/Card";

export default {
  name: "BaseNumAnalysisCardWidget",

  mixins: [BaseWidget],

  components: {
    Card,
    PlainStatistic,
  },

  data() {
    return {
      isLoad: false,
      filters: [],
      levelParam: ["PXZQDM", "CXZQDM", "FXZQDM"],
      level: 0,
      adminCode: "45",
      adminData: null,
      count: null,
      color: "#4065E0",
    };
  },
  mounted() {
    this.bus.$on(
      this.$events.admin.CHANGE_ADMIN,
      this.adminFilterUpdateHandler
    );
    this.bus.$on("setAnalysisFilterCount", this.setFilter);
    this.bus.$on("stncDate", (e) => {
      this.date = e;
    });
  },
  configLoaded() {
    this.isLoad = true;
    this.getData();
  },
  watch: {
    date() {
      this.getData();
    },
  },
  methods: {
    setFilter(e) {
      this.filters = e;
      if (e) {
        this.getData();
      }
    },
    adminFilterUpdateHandler(e) {
      if (e) {
        this.adminData = e;
        this.adminCode = e.code;
        this.level = e.level;
      } else {
        this.adminData = null;
        this.adminCode = "45";
        this.level = 0;
      }
      this.getData();
    },
    async getData() {
      for (const config of this.config) {
        let filter = [
          "all",
          ["=", this.levelParam[this.level], this.adminCode],
          ...this.filters,
        ];
        if (this.date !== "全部" && this.date) {
          filter.push(["=", "date", this.date]);
        }
        if (config.filter) {
          filter = [...filter, config.filter];
        }
        if (filter.length == 2) {
          filter = filter[1];
        }
        let params = {
          returnGeometry: false,
          format: "json",
          filter,
          aggregates: [["_id", "count", null, "distinct"]],
        };
        let res = await this.$apis.feature.aggregate(config.table, params);
        config.count = res[0]["_id_count"];
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";

.card {
  width: 280px;
}
</style>
