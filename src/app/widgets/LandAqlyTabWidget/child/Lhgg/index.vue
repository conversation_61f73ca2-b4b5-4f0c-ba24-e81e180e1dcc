<template>
  <div class="wrap">
    <div class="search">
      <div style="width: 70px">时期:</div>
      <a-select
        :default-value="selectValue"
        style="width: 100%"
        @change="handleChange"
      >
        <a-select-option
          v-for="(item, index) in selectArr"
          :key="index"
          :value="item.value"
        >
          {{ item.label }}
        </a-select-option>
      </a-select>
    </div>
    <div style="width: 100%; padding-left: 20px; margin-top: 20px">
      <TitleBar :title="`${name}${select.label}`"></TitleBar>
    </div>
    <div class="type-cont-box">
      <div class="type-box-item">
        <div class="label">
          <div>联合攻关区</div>
          <div>项目县数量</div>
        </div>
        <div class="value">
          <span class="val"> {{ lhgg_num }}</span>
          <span class="unit">个</span>
        </div>
      </div>
      <div class="type-box-item">
        <div class="label">
          <div>集中推进示范区</div>
          <div>项目县数量</div>
        </div>
        <div class="value">
          <span class="val"> {{ jztj_num }}</span>
          <span class="unit">个</span>
        </div>
      </div>
      <div class="type-box-item">
        <div class="label">
          <div>村级服务站</div>
          <div>数量</div>
        </div>
        <div class="value">
          <span class="val"> {{ cjfwz_num }}</span>
          <span class="unit">个</span>
        </div>
      </div>
    </div>
    <div style="width: 100%; padding-left: 20px; margin-top: 20px">
      <TitleBar :title="`${name}联合攻关区面积统计`"></TitleBar>
    </div>
    <div class="type-cont-box">
      <div class="type-box-item">
        <div class="label">
          <div>联合攻关区</div>
          <div>面积</div>
        </div>
        <div class="value">
          <span class="val">
            {{
              this.$utils.number.bigDataFormat(lhgg_mj, 10000, 1, [
                "亩",
                "万亩",
              ]).value
            }}</span
          >
          <span class="unit">{{
            this.$utils.number.bigDataFormat(lhgg_mj, 10000, 1, ["亩", "万亩"])
              .unit
          }}</span>
        </div>
      </div>
      <div class="type-box-item">
        <div class="label">
          <div>集中推进示范区</div>
          <div>面积</div>
        </div>
        <div class="value">
          <span class="val">
            {{
              this.$utils.number.bigDataFormat(jztj_mj, 10000, 1, [
                "亩",
                "万亩",
              ]).value
            }}</span
          >
          <span class="unit">{{
            this.$utils.number.bigDataFormat(jztj_mj, 10000, 1, ["亩", "万亩"])
              .unit
          }}</span>
        </div>
      </div>
      <div class="type-box-item">
        <div class="label">
          <div>村级服务站建设</div>
          <div>面积</div>
        </div>
        <div class="value">
          <span class="val">
            {{
              this.$utils.number.bigDataFormat(cjfwz_mj, 10000, 1, [
                "亩",
                "万亩",
              ]).value
            }}</span
          >
          <span class="unit">{{
            this.$utils.number.bigDataFormat(cjfwz_mj, 10000, 1, ["亩", "万亩"])
              .unit
          }}</span>
        </div>
      </div>
    </div>
    <div class="chart-wrap">
      <Chart :options="option" />
    </div>
  </div>
</template>

<script>
const levelParam = [
  { code: "PXZQDM", name: "PXZQMC" },
  { code: "CXZQDM", name: "CXZQMC" },
  { code: "FXZQDM", name: "FXZQMC" },
  { code: "TXZQDM", name: "TXZQMC" },
  { code: "XZQDM", name: "XZQMC" },
];
import BaseWidget from "@/BaseWidget";
import Chart from "@/components/Chart";
import TitleBar from "@/app/Components/TitleBar/";
export default {
  mixins: [BaseWidget],
  props: {
    level: {
      type: Number,
      default: 0,
    },
    adminCode: {
      type: String,
    },
    adminName: {
      type: String,
    },
    adminData: {
      type: Object,
    },
  },
  watch: {
    adminCode() {
      this.handleChange(this.selectValue);
    },
  },
  data() {
    return {
      lhgg_num: 0,
      jztj_num: 0,
      cjfwz_num: 0,
      lhgg_mj: 0,
      jztj_mj: 0,
      cjfwz_mj: 0,
      aqlyData: [],
      lhggTypeData: [],
      field: "land_aqly",
      unit: "亩",
      option: {
        xAxis: {
          type: "value",
          position: "top",
        },
        tooltip: {
          trigger: "axis",
        },
        yAxis: {
          type: "category",
          data: null,
          axisLabel: {
            show: true,
            textStyle: {
              fontSize: 15, //更改坐标轴文字大小
            },
          },
          formatter: function (params) {
            return params.slice(0, 3) + "...";
          },
        },
        grid: {
          left: "17%",
          right: "4%",
          top: "20px",
          bottom: "5%",
        },
        series: [],
      },

      selectValue: "2020,农用地安全利用项目区情况",
      selectArr: [
        {
          label: "2020年农用地安全利用项目区情况",
          value: "2020,农用地安全利用项目区情况",
        },
        {
          label: "2021年农用地安全利用项目区情况",
          value: "2021,农用地安全利用项目区情况",
        },
        {
          label: "2022年农用地安全利用项目区情况",
          value: "2022,农用地安全利用项目区情况",
        },
        {
          label: "2023年农用地安全利用项目区情况",
          value: "2023,农用地安全利用项目区情况",
        },
      ],
      select: {},
    };
  },
  computed: {
    name() {
      if (this.level > 2) {
        return this.adminData.FXZQMC;
      } else {
        return this.adminName;
      }
    },
  },
  components: { Chart, TitleBar },
  mounted() {
    this.handleChange(this.selectValue);
  },
  methods: {
    handleChange(e) {
      this.select = this.selectArr.find((item) => {
        return item.value == e;
      });
      this.selectValue = e;

      this.getChartData();
    },

    async getChartData() {
      if (this.level > 2) return;
      const filter = this.getChartFilter();
      let param = {
        returnGeometry: false,
        format: "json",
        filter,
      };
      let res = await this.$apis.feature.query("land_aqlyxm", param);

      const groupedData = res.reduce((acc, cur) => {
        const key = this.level === 0 ? cur.CXZQMC : cur.FXZQMC;

        if (!acc[key]) {
          acc[key] = {
            CXZQMC: cur.CXZQMC,
            FXZQMC: cur.FXZQMC,
            lhgg_mj: 0,
            lhgg_num: 0,
            jztj_mj: 0,
            jztj_num: 0,
            cjfwz_mj: 0,
            cjfwz_num: 0,
          };
        }

        acc[key].lhgg_mj += cur.lhgg_mj;
        acc[key].lhgg_num += cur.lhgg_num;
        acc[key].jztj_mj += cur.jztj_mj;
        acc[key].jztj_num += cur.jztj_num;

        acc[key].cjfwz_mj += cur.cjfwz_mj;
        acc[key].cjfwz_num += cur.cjfwz_num;

        return acc;
      }, {});

      const result = Object.values(groupedData);
      const totalFields = [
        "lhgg_mj",
        "lhgg_num",
        "jztj_mj",
        "jztj_num",
        "cjfwz_mj",
        "cjfwz_num",
      ];

      totalFields.forEach((field) => {
        this[field] = result.reduce((acc, cur) => acc + cur[field], 0);
      });

      console.log("result", result);

      result.sort((a, b) => {
        return (
          a.lhgg_mj +
          a.cjfwz_mj +
          a.jztj_mj -
          (b.lhgg_mj + b.cjfwz_mj + b.jztj_mj)
        );
      });

      this.option.yAxis.data = result.map((item) => {
        return item[`${this.level === 0 ? "CXZQMC" : "FXZQMC"}`];
      });

      const seriesNames = [
        {
          lable: "联合攻关区",
          value: "lhgg_mj",
          color: "#557721",
        },
        {
          lable: "集中推进示范区",
          value: "jztj_mj",
          color: "#acee4b",
        },
        {
          lable: "村级服务站",
          value: "cjfwz_mj",
          color: "rgb(179,124,0)",
        },
      ];

      this.option.series = seriesNames.map(({ lable, value, color }) => ({
        stack: "Total",
        name: lable,
        data: result.map((item) => parseFloat(item[value].toFixed(2))),
        type: "bar",
        showBackground: true,
        backgroundStyle: {
          color: "rgba(180, 180, 180, 0.2)",
        },
        itemStyle: {
          color,
        },
        barWidth: 15,
      }));
    },

    getChartFilter() {
      if (this.level == 0) {
        return [
          "all",
          ["!=", "CXZQDM", "4500"],
          ["=", "date", this.selectValue.split(",")[0]],
        ];
      } else {
        return [
          "all",
          ["=", levelParam[this.level].code, this.adminCode],
          ["=", "date", this.selectValue.split(",")[0]],
        ];
      }
    },
  },
};
</script>

<style lang="less" scoped>
.wrap {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  padding-top: 15px;
  .search {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
  }
  .cont-box {
    width: calc(100% - 40px);
    background-color: #e6eaea;
    padding: 12px;
    .label {
      font-size: 21px;
      font-weight: 550;
    }
    .value {
      margin-top: 12px;
      text-align: center;
      .val {
        font-size: 30px;
        font-weight: 650;
        color: #438ae8;
      }
      .unit {
        margin-left: 10px;
      }
    }
  }
  .type-cont-box {
    display: flex;
    width: 100%;
    padding: 12px 40px;
    justify-content: space-between;

    .type-box-item {
      .label {
        text-align: center;
        font-size: 18px;
        font-weight: 550;
      }
      .value {
        margin-top: 12px;
        text-align: center;
        .val {
          font-size: 22px;
          font-weight: 650;
          color: #438ae8;
        }
        .unit {
          margin-left: 10px;
        }
      }
    }
  }
  .chart-wrap {
    width: 100%;
    flex: 1;
    padding: 20px;
  }
}
</style>
