<template>
  <div class="wrap">
    <div class="cont-box">
      <div>
        <div class="label">实施面积</div>
        <div class="value">
          <span class="val" style="color: rgb(0, 179, 0)">
            {{
              this.$utils.number.bigDataFormat(areaSumValue, 10000, 1, [
                "亩",
                "万亩",
              ]).value || 0
            }}</span
          >
          <span class="unit">{{
            this.$utils.number.bigDataFormat(areaSumValue, 10000, 1, [
              "亩",
              "万亩",
            ]).unit || 0
          }}</span>
        </div>
      </div>
      <div>
        <div class="label">安全利用类耕地面积</div>
        <div class="value">
          <span class="val" style="color: rgb(179, 179, 0)">
            {{
              this.$utils.number.bigDataFormat(analysisValue, 10000, 1, [
                "亩",
                "万亩",
              ]).value || 0
            }}</span
          >
          <span class="unit">{{
            this.$utils.number.bigDataFormat(analysisValue, 10000, 1, [
              "亩",
              "万亩",
            ]).unit || 0
          }}</span>
        </div>
      </div>
      <div>
        <div class="label">比例</div>
        <div class="value">
          <span class="val" style="color: rgb(0, 179, 0)">
            {{ ((areaSumValue / analysisValue) * 100).toFixed(1) + "%" }}</span
          >
        </div>
      </div>
    </div>
    <div style="width: 100%; padding-left: 20px; margin-top: 20px">
      <TitleBar :title="`${adminName}安全利用措施实施面积统计`"></TitleBar>
    </div>
    <div class="chart-wrap">
      <Chart :options="option" />
    </div>
  </div>
</template>

<script>
const analysisUris = [
  "land_plough_city",
  "land_plough_city",
  "land_plough_county",
  "land_plough_town",
  "land_plough_village",
];
const levelParam = [
  { code: "PXZQDM", name: "PXZQMC" },
  { code: "CXZQDM", name: "CXZQMC" },
  { code: "FXZQDM", name: "FXZQMC" },
  { code: "TXZQDM", name: "TXZQMC" },
  { code: "XZQDM", name: "XZQMC" },
];
const csParam = {
  shtj: "石灰调节",
  yhsf: "优化施肥",
  pztz: "品种结构调整",
  sftk: "水分调控",
  ymtk: "叶面调控",
  lzxg: "轮作休耕",
  xg: "休耕",
  qt: "其他",
  zhzl: "综合治理",
};
import BaseWidget from "@/BaseWidget";
import Chart from "@/components/Chart";
import TitleBar from "@/app/Components/TitleBar/";
export default {
  mixins: [BaseWidget],
  props: {
    level: {
      type: Number,
      default: 0,
    },
    adminCode: {
      type: String,
    },
    adminName: {
      type: String,
    },
    mapObj: {
      type: Object,
    },
  },
  watch: {
    adminCode() {
      this.getAnalysis();
      this.getChartData();
    },
  },
  data() {
    return {
      selectValue: "nyd_tz_layer",
      areaSumValue: "",
      chartData: [],
      analysisValue: "",
      unit: "亩",
      vectorName: "nyd_tz",
    };
  },
  computed: {
    option() {
      return {
        xAxis: {
          type: "value",
          position: "top",
          name: this.level > 1 ? "亩" : "万亩",
          axisLabel: {
            formatter: (text) => {
              if (text > 10000) {
                return text / 10000 + "万";
              }
              return text;
            },
          },
        },
        tooltip: {
          trigger: "axis",
        },
        yAxis: {
          type: "category",
          data: this.chartData.map((item) => {
            return csParam[item["aqlycs"]] || 0;
          }),
          formatter: function (params) {
            return params.slice(0, 3) + "...";
          },
        },
        grid: {
          left: "20%",
          right: "4%",
          top: "25px",
          bottom: "5%",
        },
        series: [
          {
            stack: "Total",
            name: "面积",
            data: this.chartData.map((item) => {
              return Number(item["tz_mj_sum"]).toFixed(2);
            }),
            type: "bar",
            showBackground: true,
            backgroundStyle: {
              color: "rgba(180, 180, 180, 0.2)",
            },
            itemStyle: {
              color: "rgb(179,179,0)",
            },
            barWidth: 20,
          },
        ],
      };
    },
  },
  components: { Chart, TitleBar },
  mounted() {
    this.getAnalysis();
    this.getChartData();
  },
  methods: {
    async getAnalysis() {
      const lbhfFilter = this.getLbhfFilter();

      let lbhfParam = {
        returnGeometry: false,
        format: "json",
        outFields: ["land_aqly"],
        filter: lbhfFilter,
      };
      let lbhfRes = await this.$apis.feature.query(
        analysisUris[this.level],
        lbhfParam
      );
      if (lbhfRes.length == 0) return;
      this.analysisValue = lbhfRes[0]["land_aqly"];
      const filter = this.getAnalysisFilter();

      let param = {
        aggregates: [["tz_mj", "sum"]],
        filter,
      };
      let res = await this.$apis.feature.aggregate(this.vectorName, param);
      this.areaSumValue = res ? Number(res[0]["tz_mj_sum"]).toFixed(2) : 0;
    },
    async getChartData() {
      let name = "aqlycs";
      let lhggParam = {
        filter: this.getAnalysisFilter(),
        aggregates: [["tz_mj", "sum", "asc"]],
        groupFields: [name],
      };
      let res = await this.$apis.feature.aggregate(this.vectorName, lhggParam);
      this.chartData = res || [];
    },
    getNextLevel() {
      return this.level == 4 ? 4 : this.level + 1;
    },
    getLbhfFilter() {
      return this.adminCode == "45"
        ? ["=", "CXZQDM", "4500"]
        : ["=", levelParam[this.level].code, this.adminCode];
    },
    getAnalysisFilter() {
      return this.adminCode == "45"
        ? ["=", "zh_class", 2]
        : [
            "all",
            ["=", "zh_class", 2],
            ["=", levelParam[this.level].code, this.adminCode],
          ];
    },
  },
};
</script>

<style lang="less" scoped>
.wrap {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  padding-top: 15px;
  .search {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px 15px;
  }
  .cont-box {
    width: calc(100% - 40px);
    background-color: #e6eaea;
    padding: 12px;
    display: flex;
    justify-content: space-between;
    .label {
      font-size: 18px;
      font-weight: 550;
    }
    .value {
      margin-top: 12px;
      text-align: center;
      .val {
        font-size: 25px;
        font-weight: 650;
        color: #438ae8;
      }
      .unit {
        margin-left: 10px;
      }
    }
  }
  .type-cont-box {
    display: flex;
    width: 100%;
    padding: 12px 40px;
    justify-content: space-between;
    .type-box-item {
      // flex: 1;
      .label {
        font-size: 18px;
        font-weight: 550;
      }
      .value {
        margin-top: 12px;
        text-align: center;
        .val {
          font-size: 22px;
          font-weight: 650;
          color: #438ae8;
        }
        .unit {
          margin-left: 10px;
        }
      }
    }
  }
  .chart-wrap {
    width: 100%;
    flex: 1;
    padding: 20px;
  }
}
</style>
