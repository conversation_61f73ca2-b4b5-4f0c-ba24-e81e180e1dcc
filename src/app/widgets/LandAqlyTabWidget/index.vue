<template>
  <div class="jimu-widget wrap">
    <div class="tab">
      <a-radio-group
        @change="onChange"
        default-value="类别划分"
        v-model="thisTab"
        button-style="solid"
      >
        <template v-for="(item, index) in tabs">
          <a-radio-button :value="item.value" :key="index">
            {{ item.label }}
          </a-radio-button>
        </template>
      </a-radio-group>
    </div>
    <div class="tab-content">
      <component
        :is="thisTab"
        :level="level"
        :mapObj="map"
        :adminCode="adminCode"
        :adminName="adminName"
        :adminData="adminData"
      ></component>
    </div>
  </div>
</template>

<script>
import Tz from "./child/Tz";
import Cdhj from "./child/Cdhj";
import Lbhf from "./child/Lbhf";
import Lhgg from "./child/Lhgg";
import BaseWidget from "@/BaseWidget";
export default {
  name: "LandTabWidget",
  mixins: [BaseWidget],
  components: { Tz, Cdhj, Lbhf, Lhgg },
  data() {
    return {
      thisTab: "Lbhf",
      tabs: [
        { label: "类别划分", value: "Lbhf", layer: ["nyd_lbhf_aqly"] },
        {
          label: "安全利用项目区",
          value: "Lhgg",
          layer: ["land_aqlyjztjq_lhggq_2020", "land_aqlyjztjq_jztjq_2020"],
        },
        { label: "安全利用措施", value: "Tz", layer: ["nyd_tz_layer"] },
        {
          label: "产地环境监测",
          value: "Cdhj",
          layer: ["guangxi_2017njcncpd"],
        },
      ],
      level: 0,
      adminCode: "45",
      adminData: null,
      adminName: "广西",
    };
  },
  mounted() {
    this.bus.$on(
      this.$events.admin.CHANGE_ADMIN,
      this.adminFilterUpdateHandler
    );
    this.setModuleLayerFilter("nyd_tz_layer", "base-stylet", [
      "==",
      ["get", "zh_class"],
      2,
    ]);
    this.setModuleLayerFilter("nyd_lbhf_aqly", "base-stylet", [
      "==",
      ["get", "zh_class"],
      2,
    ]);
    this.setModuleLayerFilter("nyd_cdhj_aqly", "base-stylet", [
      "==",
      ["get", "class"],
      "安全利用类",
    ]);
  },
  methods: {
    onChange(e) {
      console.log(e);
      const item = this.tabs.find((item) => {
        return item.value == e.target.value;
      });

      this.bus.$emit(this.$events.layer.CHECKED_LAYER, item.layer);
    },
    adminFilterUpdateHandler(e) {
      if (e) {
        this.adminName = e.name;
        this.level = e.level;
        this.adminData = e;
        this.adminCode = e.code;
      } else {
        this.adminName = "广西";
        this.level = 0;
        this.adminData = null;
        this.adminCode = "45";
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";
.wrap {
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow-y: auto;
  overflow-x: hidden;
  .tab {
    margin-top: 20px;
    width: 100%;

    /deep/.ant-radio-button-wrapper-checked {
      background-color: @primaryColor;
      border-color: @primaryColor;
    }

    /deep/ .ant-radio-group {
      display: flex;
      justify-content: center;
    }
    /deep/ .ant-radio-button-wrapper {
      // 一行展示三个
      font-size: 14px;
      padding: 0 10px;
    }
    // .a-radio-button {
    //   display: flex;
    //   flex-direction: column;
    // }
  }
  .tab-content {
    flex: 1;
    width: 100%;
  }
}
</style>
