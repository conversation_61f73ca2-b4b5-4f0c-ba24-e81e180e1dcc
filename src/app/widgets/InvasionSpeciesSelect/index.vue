<template>
  <div class="jimu-widget wrap">
    <div class="select-item">
      <a-select
        show-search
        placeholder="请选择物种类型"
        option-filter-prop="children"
        defaultValue="重点管理物种"
        style="width: 100%"
        @change="handleChangeSpecies"
        allowClear
      >
        <a-select-option
          :value="item"
          v-for="(item, index) in speciesTypeList"
          :key="index"
        >
          {{ item }}
        </a-select-option>
      </a-select>
    </div>
    <div class="select-item">
      <a-select
        show-search
        placeholder="请选择物种"
        option-filter-prop="children"
        defaultValue="全部"
        style="width: 100%"
        :filter-option="filterOption"
        @change="handleChange"
        allowClear
      >
        <a-select-option
          :value="item.species_bm"
          v-for="item in speciesList"
          :key="item.species_bm"
        >
          {{ item.species_name }}
        </a-select-option>
      </a-select>
    </div>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
export default {
  name: "InvasionSpeciesPCLayerRadio",
  mixins: [BaseWidget],
  data() {
    return {
      list: [],
      speciesList: [],
      speciesTypeList: [],
    };
  },
  configLoaded() {
    this.init();
  },
  methods: {
    async init() {
      let res = await this.$apis.feature.query(this.config.table, {
        format: "json",
      });
      this.list = res;
      // 获取物种类型
      this.speciesTypeList = [...new Set(res.map((item) => item.wzlx))];
      this.speciesTypeList.push("全部");
      // 获取物种清单
      let arr = [];
      res.forEach((item) => {
        if (item.wzlx === "重点管理物种") arr.push(item);
      });
      this.speciesList = res;
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    //  修改物种类型 获取 对应物种清单
    handleChangeSpecies(e) {
      if (e === "全部") {
        this.speciesList = this.list;
      } else {
        let arr = [];
        this.list.forEach((item) => {
          if (item.wzlx === e) arr.push(item);
        });

        this.speciesList = arr;
      }
      this.bus.$emit("setPCTableType", e);
    },
    async handleChange(e) {
      let obj = {};
      for (let i = 0; i < this.config.filterTables.length; i++) {
        const f = this.config.filterTables[i];
        if (e) {
          let res = await this.$apis.feature.query(f.table, {
            format: "json",
            filter: ["=", f.field, e],
          });
          const arr = [...new Set(res.map((item) => item[f.mainTableField]))];
          let filter = ["in", ["get", f.mainTableField], ["literal", arr]];
          const tableFilter = ["in", f.mainTableField, arr];
          obj[f.mainTable] = tableFilter;
          this.setModuleLayerFilter(f.mainTable, "wz", filter);
          this.bus.$emit(this.$events.map.SET_LAYER_FILTER, f.mainTable);
        } else {
          this.setModuleLayerFilter(f.mainTable, "wz", null);
          this.bus.$emit(this.$events.map.SET_LAYER_FILTER, f.mainTable);
          obj[f.mainTable] = null;
          this.bus.$emit("selectPCspeciesNull");
        }
      }
      this.bus.$emit("selectPCspecies", e);
      console.log("selectPCspecies", e);
      this.bus.$emit("setPCTableFilter", obj);
      console.log("setPCTableFilter", obj);
    },
  },
};
</script>

<style lang="less" scoped>
.wrap {
  display: flex;
  .select-item {
    background: #ffffff;
    border-radius: 4px;
    padding: 3px;
    box-shadow: 2px 2px 3px rgb(0 0 0 / 20%);
    margin-right: 12px;
  }
  /deep/.ant-select {
    min-width: 120px;
  }
  /deep/.ant-select-selection--single {
    border: none;
  }
}
</style>
