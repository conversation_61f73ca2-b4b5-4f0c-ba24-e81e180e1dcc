<template>
  <div class="jimu-widget jimu-tool-widget box" ref="myElement">
    <div class="tabs-content">
      <div
        :class="['tabs', item == currentTab ? 'active' : '']"
        v-for="(item, index) in tabs"
        :key="index"
        @click="currentTab = item"
      >
        {{ item }}
      </div>
    </div>

    <div class="content scrollbar" v-show="currentTab == '项目简介'">
      <div v-for="(item, index) in profile" :key="index">
        <div class="content_title">{{ item.title }}</div>
        <div
          class="content_font"
          style="text-align: justify; text-indent: 2em"
          v-for="(i, index) in item.info"
          :key="index"
        >
          <p v-html="i"></p>
        </div>
      </div>
    </div>

    <div class="content" v-show="currentTab == '物种发生情况'">
      <div class="part">
        <TitleBar :title="`物种发生情况`" style="line-height: 22px"></TitleBar>
        <a-table
          :columns="tableColumn"
          :data-source="tableData"
          :scroll="{ y: viewportHeight * 35 }"
          :pagination="false"
        >
          <template slot="operation" slot-scope="text, record">
            <a-button type="link" @click="rowClick(record)">详情</a-button>
          </template>
        </a-table>
      </div>
      <div v-if="currentSpecies" class="part">
        <TitleBar :title="`${currentSpecies.物种名称}发生情况`"></TitleBar>
        <a-table
          :columns="tableColumn3"
          :data-source="tableData3"
          :pagination="false"
        >
        </a-table>
      </div>
      <div v-if="currentSpecies" class="part">
        <TitleBar
          :title="`历次调查${currentSpecies.物种名称}发现情况`"
        ></TitleBar>

        <div class="chart-wrap">
          <Chart :options="option" />
        </div>
      </div>
    </div>

    <div class="content" v-show="currentTab == '预警监测点调查情况'">
      <div class="part">
        <TitleBar
          :title="`预警监测点调查情况`"
          style="line-height: 22px"
        ></TitleBar>
        <a-table
          :columns="tableColumn1"
          :data-source="tableData1"
          :scroll="{ y: viewportHeight * 35 }"
          :pagination="false"
        >
          <template slot="operation" slot-scope="text, record">
            <a-button type="link" @click="rowClick1(record)">详情</a-button>
          </template>
        </a-table>
      </div>

      <div v-if="currentCounty" class="part">
        <TitleBar :title="`历次调查${currentCounty.FXZQMC}发现情况`"></TitleBar>
        <!-- <TitleBar :title="`${currentCounty.FXZQMC}发生情况`"></TitleBar> -->
        <a-table
          :columns="tableColumn4"
          :data-source="tableData4"
          :pagination="false"
          class="table"
        >
        </a-table>
      </div>
      <div v-if="currentCounty" class="part">
        <div class="chart-wrap">
          <Chart :options="option1" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import TitleBar from "../../Components/TitleBar";
import Chart from "@/components/Chart/";
import BaseWidget from "@/BaseWidget";
import { mapState } from "vuex";
const map = {
  "2022年度第一次调查": {
    index: 1,
    val: "第1次调查",
  },
  "2022年度第二次调查": {
    index: 2,
    val: "第2次调查",
  },
  "2022年度第三次调查": {
    index: 3,
    val: "第3次调查",
  },
  "2022年度第四次调查": {
    index: 4,
    val: "第4次调查",
  },
};
export default {
  name: "PlantSpeciesJcEchart",

  mixins: [BaseWidget],

  computed: {
    ...mapState({
      adminData: (state) => state.data?.admin,
    }),

    viewportHeight() {
      return window.innerHeight / 100; // 获取1vh对应的像素数
    },
    option() {
      return {
        legend: {},
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        xAxis: {
          type: "category",
          data: this.chartData.map((item) => item.label),
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            name: "发生样地数",
            data: this.chartData.map((item) => item.样地数),
            type: "line",
          },
          {
            name: "监测点数",
            data: this.chartData.map((item) => item.监测点数),
            type: "line",
          },
        ],
      };
    },
    option1() {
      return {
        legend: {},
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        xAxis: {
          type: "category",
          data: this.chartData1.map((item) => item.label),
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            name: "物种个数",
            data: this.chartData1.map((item) => item.物种数),
            type: "line",
          },
          {
            name: "物种种类数",
            data: this.chartData1.map((item) => item.物种种类),
            type: "line",
          },
        ],
      };
    },
  },

  components: {
    TitleBar,
    Chart,
  },

  watch: {
    async adminData() {},
  },

  data() {
    return {
      currentTab: "项目简介",
      tabs: ["项目简介", "物种发生情况", "预警监测点调查情况"],
      profile: [],
      currentSpecies: null,
      currentCounty: null,
      tableColumn: [
        {
          width: 70,
          dataIndex: "物种编码",
          title: "物种编码",
        },
        {
          width: 90,
          dataIndex: "物种名称",
          title: "物种名称",
        },
        {
          width: 60,
          dataIndex: "FXZQDM_count",
          title: "监测点",
        },

        {
          width: 50,
          title: "",
          dataIndex: "operation",
          scopedSlots: { customRender: "operation" },
        },
      ],
      tableColumn1: [
        {
          width: 45,
          dataIndex: "index",
          title: "排名",
        },
        {
          width: 70,
          dataIndex: "FXZQMC",
          title: "监测点",
        },
        {
          width: 60,
          dataIndex: "物种编码_count",
          title: "发现物种",
        },

        {
          width: 40,
          title: "",
          dataIndex: "operation",
          scopedSlots: { customRender: "operation" },
        },
      ],
      tableColumn3: [
        {
          width: 80,
          dataIndex: "label",
          title: "任务名称",
        },
        {
          width: 80,
          dataIndex: "监测点数",
          title: "监测点",
        },
        {
          width: 80,
          dataIndex: "样地数",
          title: "发生样地数",
        },
      ],
      tableColumn4: [
        {
          width: 100,
          dataIndex: "label",
          title: "任务名称",
          align: "center",
        },
        {
          width: 120,
          dataIndex: "物种数",
          title: "样方物种个数",
          align: "center",
        },
        {
          width: 110,
          dataIndex: "物种种类",
          title: "物种种类数",
          align: "center",
        },

        {
          width: 420,
          dataIndex: "物种名称",
          title: "物种名称",
          align: "center",
        },
      ],
      tableData: [],
      tableData1: [],
      tableData3: [],
      tableData4: [],
      chartData: [],
      chartData1: [],
      filterMap: {},
      taskList: map,
    };
  },

  methods: {
    async rowClick(e) {
      this.currentSpecies = e;
      const filter = this.filterMap.admin
        ? ["all", this.filterMap.admin, ["=", "物种编码", e["物种编码"]]]
        : ["=", "物种编码", e["物种编码"]];
      let res = await this.$apis.feature.aggregate("species_jcyj_yd_wz_2022", {
        returnGeometry: false,
        format: "json",
        filter,
        groupFields: ["任务名称"],
        aggregates: [
          ["样地编码", "count"],
          ["FXZQDM", "count", null, "distinct"],
        ],
      });
      let arr = [];
      Object.keys(map).forEach((k) => {
        let obj = {
          label: map[k].val,
          样地数: 0,
          监测点数: 0,
        };
        res.forEach((i) => {
          if (i.任务名称 == k) {
            obj.样地数 = i.样地编码_count;
            obj.监测点数 = i.FXZQDM_count;
          }
        });
        arr.push(obj);
      });
      this.chartData = arr;
      this.tableData3 = arr;
    },
    async rowClick1(e) {
      this.currentCounty = e;
      let taskListValue = [];
      for (const key in this.taskList) {
        const filter = [
          "all",
          ["=", "FXZQDM", e["FXZQDM"]],
          ["=", "任务名称", key],
        ];

        if (this.filterMap.admin) {
          filter.unshift(this.filterMap.admin);
        }
        let res = await this.$apis.feature.query("species_jcyj_yd_wz_2022", {
          filter,
        });
        const wzbm = [];
        const wzmc = [];
        res.features.forEach((i) => {
          wzbm.push(i.properties.物种编码);
          wzmc.push(i.properties.物种名称);
        });
        const set_wzmc = Array.from(new Set(wzmc));
        taskListValue.push({
          任务名称: key,
          物种编码: wzbm,
          物种名称: set_wzmc,
        });
      }
      let arr = [];
      Object.keys(map).forEach((k) => {
        let obj = {
          label: map[k].val,
          物种个数: 0,
          物种种类: 0,
          物种名称: null,
        };

        taskListValue.forEach((i) => {
          if (i.任务名称 == k) {
            obj.物种数 = i.物种编码.length;
            obj.物种种类 = i.物种名称.length;
            obj.物种名称 = i.物种名称.join("、");
          }
        });
        arr.push(obj);
      });

      this.chartData1 = arr;
      this.tableData4 = arr;
    },
    async init() {
      let filters = Object.keys(this.filterMap).map((k) => {
        return this.filterMap[k];
      });

      let filter = null;
      if (filters.length === 1) {
        filter = filters[0];
      } else if (filters.length > 1) {
        filter = ["all", ...filters];
      }
      let res = await this.$apis.feature.aggregate("species_jcyj_yd_wz_2022", {
        returnGeometry: false,
        format: "json",
        groupFields: ["物种编码", "物种名称"],
        aggregates: [["FXZQDM", "count", null, "distinct"]],
        filter,
      });

      this.tableData = res.sort((a, b) => {
        return b.FXZQDM_count - a.FXZQDM_count;
      });
      let res1 = await this.$apis.feature.aggregate("species_jcyj_yd_wz_2022", {
        returnGeometry: false,
        format: "json",
        groupFields: ["FXZQMC", "FXZQDM"],
        aggregates: [["物种编码", "count"]],
        filter,
      });

      this.tableData1 = res1
        .sort((a, b) => {
          return b["物种编码_count"] - a["物种编码_count"];
        })
        .map((item, index) => {
          return { ...item, index: index + 1 };
        });
    },

    setJCYJTaskFilter(e) {
      if (e) {
        const filter = ["=", "任务名称", e];
        this.$set(this.filterMap, "task", filter);
      } else {
        this.$set(this.filterMap, "task", null);
      }

      this.init();
    },
    setAdminFilter(e) {
      const map = {
        city: "CXZQDM",
        county: "FXZQDM",
        town: "TXZQDM",
      };
      if (e) {
        const filter = ["=", map[e.region], e[map[e.region]]];
        this.$set(this.filterMap, "admin", filter);
      } else {
        this.$set(this.filterMap, "admin", null);
      }

      this.init();
      // if (this.currentCounty) {
      //   this.rowClick1(this.currentCounty);
      // }
      if (this.currentSpecies) {
        this.rowClick(this.currentSpecies);
      }
    },
  },
  mounted() {
    this.init();
    this.bus.$on(this.$events.admin.CHANGE_ADMIN, this.setAdminFilter);
    this.bus.$on("setJCYJTaskFilter", this.setJCYJTaskFilter);
  },
  allLoaded() {
    // 定时为了等待其他组件先加载完毕
    setTimeout(async () => {
      if (Array.isArray(this.config.profile)) {
        this.profile = this.config.profile;
      }
    }, 500);
  },
};
</script>

<style lang="less" scoped>
.box {
  .part {
    width: 100%;
    margin-bottom: 2vh;
    .table {
      margin-bottom: 20px;
    }
    .more {
      cursor: pointer;
    }
    .chart-box {
      position: relative;
      .back {
        position: absolute;
        right: 1vh;
        top: 0;
        cursor: pointer;
        color: #002159;
      }
    }
  }
}
.chart-wrap {
  height: 30vh;
}
.tabs-content {
  display: flex;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #e7ebf4;
  overflow: hidden;
  margin-bottom: 1vh;
  .tabs {
    width: 50%;
    height: 40px;
    text-align: center;
    // border: 1px solid #ccc;
    line-height: 40px;
    cursor: pointer;
  }

  .active {
    border-bottom: none;
    // color: rgb(62, 145, 247);
    color: #fff;
    background-color: #4065e0;
  }
}
.scrollbar {
  height: 100%;
  overflow-y: scroll;
  // 隐藏滚动条
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.content {
  height: calc(100% - 65px);
  display: flex;
  flex-direction: column;
  overflow-y: scroll;
  .content_title {
    font-size: 22px;
    font-weight: bold;
    color: #333;
    margin: 10px 0;
    line-height: 1.5;
  }
  .content_font {
    font-size: 18px;
    color: #555;
    margin: 10px 0;
    line-height: 1.6;
  }
}

.table {
  overflow-x: scroll;
  // 滚动条的大小
  &::-webkit-scrollbar {
    height: 10px;
  }
}
</style>
