<template>
  <div class="jimu-widget wrap" v-if="isLoad">
    <template v-for="(item, index) in config">
      <template v-if="item.type == 'cascaderFilter'">
        <div :key="index" class="select-item" v-if="hasDm">
          <a-cascader
            :allowClear="false"
            :options="item.list"
            placeholder="Please select"
            @change="onChange"
            v-model="item.value"
          />
        </div>
      </template>
      <template v-else>
        <div :key="index" class="select-item">
          <a-select v-model="item.value" @change="onChange">
            <template v-for="(i, n) in item.list">
              <a-select-option :key="n" :value="i.value">
                {{ i.label }}
              </a-select-option>
            </template>
          </a-select>
        </div>
      </template>
    </template>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
export default {
  name: "DmRegionMapFilterWidget",
  mixins: [BaseWidget],
  data() {
    return {
      isLoad: false,
    };
  },
  mounted() {
    // this.onChange()
  },
  configLoaded() {
    this.isLoad = true;
    setTimeout(() => {
      this.onChange();
    }, 500);
  },
  computed: {
    hasDm() {
      let fieldObj = this.config.find((item) => {
        return item.type == "field";
      });
      const field = fieldObj.value;
      let thisField = fieldObj.list.find((item) => {
        return item.value == field;
      });
      const hasZw = thisField.hasZw;
      return hasZw;
    },
  },
  methods: {
    onChange() {
      let fieldObj = this.config.find((item) => {
        return item.type == "field";
      });
      const field = fieldObj.value;
      let thisField = fieldObj.list.find((item) => {
        return item.value == field;
      });
      const type = thisField.type;
      const hasZw = thisField.hasZw;
      let filters = this.config
        .filter((item) => {
          return item.type == "filter";
        })
        .map((item) => {
          return ["=", item.field, item.value];
        });
      if (hasZw) {
        let cascaderFilterData = [];
        const cascaderFilter = this.config.filter((item) => {
          return item.type == "cascaderFilter";
        });
        cascaderFilter.forEach((elem) => {
          elem.field.forEach((fieldItem, index) => {
            cascaderFilterData.push([
              "=",
              fieldItem,
              elem.value[index] ? elem.value[index] : "合计",
            ]);
          });
        });
        filters = [...filters, ...cascaderFilterData];
      } else {
        filters = [
          ...filters,
          ...[
            ["=", "zwzl", "合计"],
            ["=", "zwmc", "合计"],
          ],
        ];
      }
      this.bus.$emit("RegionCHART_UPDATE", {
        field,
        filters,
        type,
        name: thisField.label,
      });

      this.setModuleLayerFilter("t_film_jc_point_nm", "date", [
        "==",
        ["get", "date"],
        filters[0][2],
      ]);
      this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "t_film_jc_point_nm");

      this.setModuleLayerFilter("t_film_jc_point_dm", "date", [
        "==",
        ["get", "date"],
        filters[0][2],
      ]);
      this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "t_film_jc_point_dm");

      this.setModuleLayerFilter("t_film_jc_point_pm", "date", [
        "==",
        ["get", "date"],
        filters[0][2],
      ]);
      this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "t_film_jc_point_pm");

      this.setModuleLayerFilter("film_gkjcd_cll", "date", [
        "==",
        ["get", "date"],
        filters[0][2],
      ]);
      this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "film_gkjcd_cll");

      this.setModuleLayerFilter("t_film_zzh_point_dm", "date", [
        "==",
        ["get", "date"],
        filters[0][2],
      ]);
      this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "t_film_zzh_point_dm");

      this.setModuleLayerFilter("t_film_zzh_point_pm", "date", [
        "==",
        ["get", "date"],
        filters[0][2],
      ]);
      this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "t_film_zzh_point_pm");

      this.bus.$emit("dateChange", filters[0][2]);
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";
.wrap {
  display: flex;

  .select-item {
    background: #ffffff;
    border-radius: 4px;
    padding: 3px;
    box-shadow: 2px 2px 3px rgb(0 0 0 / 20%);
    margin-right: 12px;
  }
  /deep/.ant-select-selection--single {
    border: none;
  }
  /deep/.ant-cascader-picker {
    min-width: 200px;
  }
  /deep/.ant-cascader-input {
    border: none;
  }
  /deep/.ant-select {
    min-width: 120px;
  }
}
</style>
