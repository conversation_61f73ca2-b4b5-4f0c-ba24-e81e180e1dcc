<template>
  <div class="jimu-widget jimu-tool-widget box" ref="myElement">
    <div class="tabs-content">
      <div
        :class="['tabs', item == currentTab ? 'active' : '']"
        v-for="(item, index) in tabs"
        :key="index"
        @click="currentTab = item"
      >
        {{ item }}
      </div>
    </div>

    <div class="content scrollbar" v-show="currentTab == '项目简介'">
      <div v-for="(item, index) in profile" :key="index">
        <div class="content_title">{{ item.title }}</div>
        <div
          class="content_font"
          style="text-align: justify; text-indent: 2em"
          v-for="(i, index) in item.info"
          :key="index"
        >
          <p v-html="i"></p>
        </div>
      </div>
    </div>

    <div class="content" v-show="currentTab == '普查成果'">
      <TitleBar
        :title="`${adminData ? adminData.name : '广西壮族自治区'}普查成果`"
      ></TitleBar>
      <div class="p">
        <div class="p-item" v-for="item in allColumn" :key="item.dataIndex">
          <div class="label">{{ item.title }}</div>
          <div class="item">
            <div class="val">{{ allData[item.dataIndex] }}</div>
            <span class="unit">{{ item.unit }}</span>
          </div>
        </div>
      </div>
      <div class="table">
        <a-table
          :columns="tableColumn"
          :data-source="tableData"
          :scroll="{ x: 720, y: viewportHeight * 33.5 }"
          :pagination="false"
        ></a-table>
      </div>
      <div v-show="!showChart">
        <TitleBar
          :title="`${
            adminData ? adminData.name : '广西壮族自治区'
          }${titleType}物种发生面积(亩)`"
          :style="adminData ? '' : 'font-size: 18px;'"
        ></TitleBar>
        <div class="table">
          {{ adminData ? adminData.range : "" }}
          <a-table
            :columns="tableColumn1"
            :data-source="tableData1"
            :scroll="{ y: viewportHeight * 33.5 }"
            :pagination="false"
          >
            <template slot="operation" slot-scope="text, record">
              <a-icon
                v-show="!(adminData && adminData.region == 'county')"
                class="more"
                @click="rowClick(record)"
                type="bar-chart"
                :style="{ color: 'rgb(64, 101, 224)' }"
              /> </template
          ></a-table>
        </div>
      </div>
      <div v-show="showChart" class="chart-box">
        <TitleBar
          :title="`${
            adminData ? adminData.name : '广西壮族自治区'
          }${currentSpecies}发生面积(亩)`"
          :style="adminData ? '' : 'font-size: 18px;'"
        ></TitleBar>
        <div class="back" @click="showChart = false">
          <a-icon type="rollback" />
        </div>
        <div class="chart-wrap">
          <Chart :options="option" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import TitleBar from "../../Components/TitleBar";
import Chart from "@/components/Chart/";
import BaseWidget from "@/BaseWidget";
import { mapState } from "vuex";
import _ from "lodash";
export default {
  name: "PlantSpeciesJcEchart",

  mixins: [BaseWidget],

  computed: {
    ...mapState({
      adminData: (state) => state.data?.admin,
    }),

    option() {
      return {
        xAxis: {
          type: "value",
          position: "top",
          name: "亩",
        },
        tooltip: {
          trigger: "axis",
        },
        yAxis: {
          type: "category",
          data: this.chartData.map((item) => {
            return item.label;
          }),
          formatter: function (params) {
            return params.slice(0, 3) + "...";
          },
        },

        series: [
          {
            stack: "Total",
            name: "面积",
            data: this.chartData.map((item) => {
              return item.occurrence_area;
            }),
            type: "bar",
            showBackground: true,
            backgroundStyle: {
              color: "rgba(180, 180, 180, 0.2)",
            },
            itemStyle: {
              color: "rgb(179,179,0)",
            },
            barWidth: 20,
          },
        ],
      };
    },
    viewportHeight() {
      return window.innerHeight / 100; // 获取1vh对应的像素数
    },
    titleType() {
      if (this.wzType === "全部") {
        return "";
      } else {
        return this.wzType;
      }
    },
  },

  components: {
    TitleBar,
    Chart,
  },

  watch: {
    async adminData() {
      console.log("adminData", this.adminData);
      await this.getTableData();
      if (this.species_bm) {
        this.getRowData();
      }
    },
    wzType() {
      this.getTableData();
    },
  },

  data() {
    return {
      currentTab: "项目简介",
      tabs: ["项目简介", "普查成果"],
      profile: [],
      allColumn: [
        {
          dataIndex: "snxz_num",
          title: "涉农乡镇数",
          unit: "个",
        },
        {
          dataIndex: "tcd_num",
          title: "踏查点",
          unit: "个",
        },
        // {
        //   dataIndex: "bzyd_num",
        //   title: "标准样地数",
        // },
        {
          dataIndex: "tc_area",
          title: "踏查面积",
          unit: "亩",
        },
      ],
      tableColumn: [
        {
          width: 120,
          dataIndex: "区划",
          title: "区划",
        },
        {
          width: 150,
          dataIndex: "snxz_num",
          title: "涉农乡镇数(个)",
          sorter: (a, b) => a.snxz_num - b.snxz_num,
        },
        {
          width: 150,
          dataIndex: "tcd_num",
          title: "踏查点(个)",
          sorter: (a, b) => a.tcd_num - b.tcd_num,
        },
        {
          width: 150,
          dataIndex: "bzyd_num",
          title: "标准样地数(个)",
          sorter: (a, b) => a.bzyd_num - b.bzyd_num,
        },
        {
          width: 150,
          dataIndex: "tc_area",
          title: "踏查面积(亩)",
          customRender(e) {
            return Number(e).toFixed(2);
          },
          sorter: (a, b) => a.tc_area - b.tc_area,
        },
      ],
      tableData: [],
      allData: {},
      tableColumn1: [
        {
          width: 140,
          dataIndex: "species_name",
          title: "物种名称",
        },
        {
          width: 130,
          dataIndex: "occurrence_area",
          title: "物种发生面积(亩)",
          sorter: (a, b) => a.occurrence_area - b.occurrence_area,
        },
        {
          width: 50,
          title: "",
          dataIndex: "operation",
          scopedSlots: { customRender: "operation" },
        },
      ],
      tableData1: [],
      chartData: [],
      showChart: false,
      currentSpecies: null,
      currentRow: null,
      tableY: 0,

      wzType: "重点管理物种",
      tcd_wz_data: [],
      yd_data: [],
      tcd_data: [],
    };
  },

  methods: {
    // 表格排序
    rowChangeHandler(pagination, filters, sorter) {
      console.log(pagination, filters, sorter);
    },
    speciesChange(options) {
      console.log("speciesChange", options);

      if (!options || options.length === 0) {
        this.showChart = false;
      }
      let filters = [];
      options.forEach((item) => {
        filters.push(["=", item.id, item.value]);
      });
      let species_bm = options.find((item) => item.id == "species_bm");
      // const row = this.tableData1.find((item) => {
      //   if (species_bm) return item.species_bm == species_bm.value;
      // });
      // this.species_bm = row ? row.species_bm : null;
      this.species_bm = species_bm ? species_bm.value : null;

      let wzlx = options.find((item) => item.id == "wzlx");
      this.wzType = wzlx ? wzlx.value : "全部";
      console.log(this.species_bm);
      if (!this.species_bm) this.showChart = false;
      this.getRowData();
      this.statisticTableData();

      this.bus.$emit("RegionCHART_UPDATE", {
        field: "occurrence_area",
        filters,
        type: "cascaderField",
        name: this.species_bm,
      });
      this.map.setPaintProperty(
        "land_city_fill",
        "fill-color",
        "rgba(255,255,255,1)"
      );
      this.map.setPaintProperty(
        "land_city_fill",
        "fill-outline-color",
        "rgba(175,135,255,1)"
      );
    },
    getRowData() {
      const row = this.tableData1.find((item) => {
        return item.species_bm == this.species_bm;
      });
      if (!row) {
        this.chartData = [];
        return;
      }

      this.rowClick(row);
    },
    async getTableDataPc() {
      let tcd_wz_form = "species_tcd_wz_2020";
      let yd_wz_form = "species_yd_wz_2020";
      let tcd_form = "species_tcd_2020";
      // 获取踏查点数量
      let tcd_wz = await this.$apis.feature.query(tcd_wz_form, {
        format: "json",
      });
      let yd_wz = await this.$apis.feature.query(yd_wz_form, {
        format: "json",
      });
      let tcd = await this.$apis.feature.query(tcd_form, { format: "json" });
      this.tcd_wz_data = tcd_wz;
      this.yd_data = yd_wz;
      this.tcd_data = tcd;
      this.statisticTableData();
    },
    statisticTableData() {
      let adminLevel = [
        { value: "CXZQDM", name: "CXZQMC" },
        { value: "FXZQDM", name: "FXZQMC" },
        { value: "TXZQDM", name: "TXZQMC" },
      ];
      let level = 0;
      if (this.adminData && this.adminData.level) level = this.adminData.level;
      let wzlx = this.wzType;
      let bm = this.species_bm;
      let tcd_wz_data = _.cloneDeep(this.tcd_wz_data);
      let yd_data = _.cloneDeep(this.yd_data);
      let tcd_data = _.cloneDeep(this.tcd_data);

      if (bm) {
        tcd_wz_data = tcd_wz_data.filter((item) => item.species_bm == bm);
        yd_data = yd_data.filter((item) => item.species_bm == bm);
      }
      if (wzlx !== "全部") {
        tcd_wz_data = tcd_wz_data.filter((item) => item.wzlx == wzlx);
        yd_data = yd_data.filter((item) => item.wzlx == wzlx);
      }
      let tcd_ids = tcd_wz_data.map((item) => item.tcd_id);

      let result = new Map();
      let rows = new Map();
      // 处理样地数据
      tcd_data.forEach((item) => {
        let obj = {
          code: item.TXZQDM,
          tcd_num: 0,
          bzyd_num: 0,
          snxz_num: 0,
          tc_area: 0,
        };
        if (result.has(item.TXZQDM)) obj = result.get(item.TXZQDM);
        obj.bzyd_num += 1;
        result.set(item.TXZQDM, obj);
      });
      console.log("result", Array.from(result.values()));

      // 处理踏查点数据
      let town = [];
      tcd_data.forEach((item) => {
        let code = item[adminLevel[level].value];
        let name = item[adminLevel[level].name];
        let obj = result.get(item.TXZQDM);

        let data = {
          区划: name,
          tcd_num: obj ? obj.tcd_num : 0,
          tc_area: obj ? obj.tc_area : 0,
          bzyd_num: obj ? obj.bzyd_num : 0,
          snxz_num: 0,
        };
        if (rows.has(code)) data = rows.get(code);
        data.tcd_num += 1;
        if (obj && tcd_ids.includes(item.tcd_id)) {
          data.tc_area += Number(item.tcd_area);
          if (!town.includes(item.TXZQDM)) {
            town.push(item.TXZQDM);
            data.snxz_num += 1;
          }
        }
        if (
          level === 0 ||
          (level === 1 && this.adminData.code === item.CXZQDM) ||
          (level === 2 && this.adminData.code === item.FXZQDM)
        ) {
          rows.set(code, data);
        }
      });
      this.tableData = Array.from(rows.values());
      console.log("tableData", this.tableData);

      const obj = {};
      this.allColumn.forEach((col) => {
        obj[col.dataIndex] = parseFloat(
          this.tableData
            .reduce((a, b) => {
              return (a += Number(b[col.dataIndex]));
            }, 0)
            .toFixed(2)
        );
        if (col.dataIndex == "tc_area") obj.tc_area = obj.tc_area.toFixed(2);
      });
      this.allData = obj;
    },
    async getTableData() {
      const p = {
        province: {
          table: "species_city",
          labelField: "CXZQMC",
          valueField: "CXZQDM",
          table2: "species_occurred_province",
        },
        city: {
          table: "species_county",
          filterField: "CXZQDM",
          labelField: "FXZQMC",
          valueField: "FXZQDM",
          table2: "species_occurred_city",
        },
        county: {
          table: "species_town",
          filterField: "FXZQDM",
          labelField: "TXZQMC",
          valueField: "TXZQDM",
          table2: "species_occurred_county",
        },
      };
      const f = p[this.adminData ? this.adminData.region : "province"];
      if (!f) return;

      // 处理请求普查成果
      this.statisticTableData();
      // let filter = ["=", "wzlx", "全部"];
      // if (this.adminData) {
      //   filter = ["all", ["=", "wzlx", "全部"], ["=", f.filterField, this.adminData[f.filterField]]];
      // }

      // let res = await this.$apis.feature.query(f.table, {
      //   format: "json",
      //   filter,
      // });
      // this.tableData = res
      //   .map((item) => {
      //     item["区划"] = item[f.labelField];
      //     return item;
      //   })
      //   .sort((a, b) => {
      //     return a[f.valueField] - b[f.valueField];
      //   });

      // const obj = {};
      // this.allColumn.forEach((col) => {
      //   obj[col.dataIndex] = parseFloat(
      //     this.tableData
      //       .reduce((a, b) => {
      //         return (a += Number(b[col.dataIndex]));
      //       }, 0)
      //       .toFixed(2)
      //   );
      // });
      // this.allData = obj;

      let filter2;
      if (this.adminData) {
        if (this.wzType === "全部") {
          filter2 = ["=", f.filterField, this.adminData[f.filterField]];
        } else {
          filter2 = [
            "all",
            ["=", "wzlx", this.wzType],
            ["=", f.filterField, this.adminData[f.filterField]],
          ];
        }
      } else {
        if (this.wzType === "全部") {
          filter2 = null;
        } else {
          filter2 = ["=", "wzlx", this.wzType];
        }
      }
      let res1 = await this.$apis.feature.query(f.table2, {
        format: "json",
        filter: filter2,
      });

      this.tableData1 = res1.sort((a, b) => {
        return b.occurrence_area - a.occurrence_area;
      });
    },
    async rowClick(e) {
      this.currentRow = e;
      this.getChartData();
    },
    async getChartData() {
      const e = this.currentRow;
      this.currentSpecies = e.species_name;
      const p = {
        province: {
          filterField: "PXZQDM",
          labelField: "CXZQMC",
          table: "species_occurred_city",
        },
        city: {
          filterField: "CXZQDM",
          labelField: "FXZQMC",
          table: "species_occurred_county",
        },
      };
      const f = p[this.adminData ? this.adminData.region : "province"];
      if (!f) {
        this.showChart = false;
        return;
      }
      let res = await this.$apis.feature.query(f.table, {
        format: "json",
        filter: [
          "all",
          ["=", f.filterField, e[f.filterField]],
          ["=", "species_bm", e.species_bm],
        ],
      });
      this.chartData = res
        .map((item) => {
          return {
            label: item[f.labelField],
            occurrence_area: Number(item.occurrence_area),
          };
        })
        .sort((a, b) => {
          return a.occurrence_area - b.occurrence_area;
        });
      this.showChart = true;
    },
  },
  mounted() {
    this.getTableDataPc();
    this.getTableData();
    this.bus.$on("selectPCspecies", this.speciesChange);
  },
  allLoaded() {
    // 定时为了等待其他组件先加载完毕
    setTimeout(async () => {
      if (Array.isArray(this.config.profile)) {
        this.profile = this.config.profile;
      }
    }, 500);
  },
};
</script>

<style lang="less" scoped>
.box {
  overflow-y: scroll;
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.tabs-content {
  display: flex;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #e7ebf4;
  overflow: hidden;
  margin-bottom: 1vh;
  .tabs {
    width: 50%;
    height: 40px;
    text-align: center;
    // border: 1px solid #ccc;
    line-height: 40px;
    cursor: pointer;
  }

  .active {
    border-bottom: none;
    // color: rgb(62, 145, 247);
    color: #fff;
    background-color: #4065e0;
  }
}
.scrollbar {
  height: 100%;
  overflow-y: scroll;
  // 隐藏滚动条
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.content {
  height: calc(100% - 65px);
  display: flex;
  flex-direction: column;
  .content_title {
    font-size: 22px;
    font-weight: bold;
    color: #333;
    margin: 10px 0;
    line-height: 1.5;
  }
  .content_font {
    font-size: 18px;
    color: #555;
    margin: 10px 0;
    line-height: 1.6;
  }
}
.p {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-top: 5px;
  .p-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 10px;
    color: #002159;
    .label {
      font-size: 14px;
      font-weight: 900;
    }
    .item {
      display: flex;
      align-items: flex-end;
      margin-top: 5px;
      // 左右排列
      .val {
        font-size: 22px;
        font-weight: 900;
        line-height: 22px;
      }
      .unit {
        font-size: 16px;
        font-weight: 900;
        margin-left: 5px;
        // 往下位移
        transform: translateY(2px);
      }
    }
  }
}
.table {
  margin-bottom: 20px;
}
.more {
  cursor: pointer;
}
.chart-box {
  position: relative;
  .back {
    position: absolute;
    right: 1vh;
    top: 0;
    cursor: pointer;
    color: #002159;
  }
}
.chart-wrap {
  height: 50vh;
}
</style>
