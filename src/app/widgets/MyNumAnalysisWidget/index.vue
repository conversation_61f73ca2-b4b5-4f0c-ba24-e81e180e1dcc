<template>
  <div class="jimu-widget" v-if="isLoad">
    <a-row :gutter="[8, 8]">
      <a-col :span="24">
        <Card :title="'氮磷流失国控监测点'">
          <PlainStatistic :value="dlCount" unit="个" :color="color" />
        </Card>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
import PlainStatistic from "@/app/Components/PlainStatistic";
import Card from "@/app/Components/Card";

export default {
  name: "MyNumAnalysisWidget",
  mixins: [BaseWidget],
  components: {
    Card,
    PlainStatistic,
  },
  data() {
    return {
      isLoad: false,
      adminCode: "45",
      data: {},
      adminData: null,
      dmCount: null,
      levelParam: ["PXZQDM", "CXZQDM", "FXZQDM"],
      level: 0,
      dmShow: false,
      color: "#4065E0",
      dlCount: 0,

      date: 2020,
    };
  },
  configLoaded() {
    this.isLoad = true;
  },
  mounted() {
    this.getData();
    this.bus.$on(
      this.$events.admin.CHANGE_ADMIN,
      this.adminFilterUpdateHandler
    );
    this.bus.$on("RegionCHART_UPDATE", this.setFilter);
  },
  watch: {
    filters() {
      this.getData();
    },
    adminCode() {
      this.getData();
    },
  },
  methods: {
    adminFilterUpdateHandler(e) {
      if (e) {
        this.adminData = e;
        this.adminCode = e.code;
        this.level = e.level;
      } else {
        this.adminData = null;
        this.adminCode = "45";
        this.level = 0;
      }
      this.getData();
    },
    async setFilter(e) {
      // 年份过滤
      this.filters = e.filters[0][2];
      console.log("eeeee", e);
      this.getData();
    },
    async getData() {
      let param = {
        filter: [
          "all",
          ["=", this.levelParam[this.level], this.adminCode],
          ["=", "dwlx", "氮磷流失原位监测点"],
          ["=", "date", this.filters],
        ],
        aggregates: [["_id", "count"]],
      };
      let res = await this.$apis.feature.aggregate("dlls_jcd_point", param);
      this.dlCount = res[0]["_id_count"];
    },
  },
};
</script>

<style lang="less" scoped>
.jimu-widget {
  // background-color: antiquewhite;
  .count-box {
    width: 200px;
    height: 70px;
    display: flex;
    font-size: 18px;
    align-items: center;
    justify-content: space-around;
    color: #002159;
    margin-bottom: 18px;
    background: white;
    border-radius: 3px;
    box-shadow: 0px 3px 10px 0px rgba(56, 139, 201, 0.3);
    img {
      height: 30px;
    }
    .count {
      font-size: 20px;
    }
  }
}

/deep/ .ant-card {
  margin-bottom: 3px;
}
</style>
