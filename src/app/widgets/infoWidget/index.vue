<template>
  <div class="jimu-widget jimu-tool-widget box" v-show="show == 'showState'">
    <StatisticsSlot>
      <template>
        <span slot="title" class="jimu-widget-title-ui">项目概况</span>
      </template>
      <template>
        <div slot="contain" class="contain">
          <p v-show="!info.xmgk == 'null'">{{ info.xmgk }}</p>
          <div class="imgDiv" v-for="(item, index) in imgs" :key="index">
            <a :href="`${img}'lhggq/xmqght/'${item}`" class="img-item">
              <img style="width: 100%" :src="`${img}'lhggq/xmqght/'${item}`" target="_blank" />
            </a>
            <p class="info">{{ item }}</p>
          </div>
        </div>
      </template>
    </StatisticsSlot>
    <StatisticsSlot>
      <template>
        <span slot="title" class="jimu-widget-title-ui"
          >试验区土壤基本情况</span
        >
      </template>
      <template>
        <div slot="contain" class="contain">
          <p v-show="info.xmqght" class="info">{{ info.trjbqk }}</p>
        </div>
      </template>
    </StatisticsSlot>
    <StatisticsSlot>
      <template>
        <span slot="title" class="jimu-widget-title-ui">最佳技术模式</span>
      </template>
      <template>
        <div slot="contain" class="contain">
          <p v-if="info.zjjsms" class="info">{{ info.zjjsms }}</p>
        </div>
      </template>
    </StatisticsSlot>
  </div>
</template>

<script>
import BaseWidget from '@/BaseWidget'
import { mapState } from 'vuex'
import StatisticsSlot from '@/app/StatisticsSlot'

export default {
  name: 'infoWidget',
  components: {
    StatisticsSlot
  },
  mixins: [BaseWidget],
  computed: {
    ...mapState({
      img: state => state.config.api.img
    })
  },
  data() {
    return {
      info: {},
      show: 'showState',
      imgs: []
    }
  },

  methods: {
    getInfo(value) {
      if (!value) return
      let data = this.$utils.object.deepCloneObj(value)
      this.imgs = []
      // let res = date._vectorTileFeature.properties
      // let arr = ['xmgk','trjbqk','zjjsms','xmqght','wz']
      // for(var i = 0 ; i < arr.length;i++){
      //     this.$set(this.info,arr[i],res[arr[i]]?res[arr[i]]:"")
      // }
      // this.$set(this.info,'imgUrls',this.info.xmqght.split("、"))
      this.info = data._vectorTileFeature.properties
      this.imgs = this.info.xmqght.split('、')
    },
    clearInfoWidget() {
      this.info = {}
    }
  },
  mounted() {
    this.bus.$on('aqlyjzq_info_show', this.getInfo)
    this.bus.$on('clearInfoWidget', this.clearInfoWidget)
  }
}
</script>

<style lang="less" scoped>
.box {
  overflow-y: scroll;
}
.close {
  float: right;
  cursor: pointer;
}
.contain {
  padding: 0px 5px;
}
.contain img {
  width: 390px;
  margin: 0 auto;
  overflow-wrap: normal;
}
.contain p {
  margin-top: 5px;
  max-width: 400px;
  word-wrap: break-word;
  word-break: break-all;
}
.contain .info {
  text-align: center;
}
.img-item {
  width: 100%;
}
</style>
