<template>
  <div class="jimu-widget jimu-tool-widget box">
    <div class="title">示范基地详情</div>
    <div class="search">
      <a-input-search
        placeholder="请输入基地名称"
        style="width: 90%"
        @search="onSearch"
      />
      <div class="query">
        <div
          class="queryData"
          v-for="(item, index) in base"
          :key="index"
          @click="checkedBase(item)"
          v-show="!checked"
        >
          {{ item.jdmc }}
        </div>
      </div>
    </div>
    <div class="base" v-show="checked">
      <div class="info">
        <TitleBar :title="'基本信息'"></TitleBar>
        <div class="contain cleardiv">
          <!-- <img src="../../../../public/images/basemap_grey.jpg" alt=""> -->
          <div>
            <p class="jd-title">{{ checked.jdmc }}</p>
            <p>地址:{{ checked.PXZQMC + checked.CXZQMC + checked.FXZQMC }}</p>
            <p>基地面积(亩):{{ checked.area }}</p>
            <p>附件链接:<a :href="'http://localhost/document/base/baseword/'+checked.gsbg">{{ checked.gsbg }}</a> </p>
          </div>
        </div>
      </div>
      <div class="info" v-if="checked.jsqk">
        <TitleBar :title="'建设内容'"></TitleBar>
        <div class="contain cleardiv">
          <!-- <img src="../../../../public/images/basemap_grey.jpg" alt=""> -->
          <div>
            <p>建设情况:{{ checked.jsqk }}</p>
          </div>
        </div>
      </div>
      <div class="info">
        <TitleBar :title="'工作总结'"></TitleBar>
        <div class="contain cleardiv">
          <!-- <img src="../../../../public/images/basemap_grey.jpg" alt=""> -->
          <div>
            <p>技术描述:{{ checked.jsms }}</p>
            <p>技术报告:{{ checked.jsbg }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
import TitleBar from "@/app/Components/TitleBar";
export default {
  name: "EcoBase",

  mixins: [BaseWidget],

  components: {
    TitleBar,
  },

  data() {
    return {
      base: [],
      checked: "",
    };
  },

  mounted() {
    this.bus.$on("onBaseClick", this.getBase);
    document.addEventListener("click", () => {
      this.base = [];
    });
  },
  methods: {
    async onSearch(value) {
      this.base = [];
      this.checked = "";

      let params = {
        format: "json",
        returnGeometry: false,
        filter: ["like", "jdmc", value],
      };
      let data = await this.$apis.feature.query("base_gx", params);
      this.base = data;
    },
    checkedBase(value) {
      this.checked = value;
    },
    getBase(value) {
      let keys = Object.keys(value);
      keys.forEach((item) => {
        if (value[item] == "null") {
          value[item] = null;
        }
      });
      this.checked = value;
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";

.box {
  padding: 0px;
  overflow: hidden;

  .title {
    text-align: center;
    font-size: 24px;
    color: #fff;
    padding: 10px 20px;
    background-color: #45a6ea;
  }
  .search {
    height: 60px;
    width: 100%;
    background: #45a6ea;
    text-align: center;
    line-height: 50px;
    .query {
      max-height: 400px;
      width: 90%;
      transform: translateX(5%) translateY(-9px);
      overflow-y: scroll;
      overflow-x: hidden;

      font-weight: 500;
      .queryData {
        height: 30px;
        width: 100%;
        border-right: 1px solid #40a9ff;
        border-bottom: 1px solid #40a9ff;
        border-left: 1px solid #40a9ff;
        border-radius: 5px;
        background-color: #fff;
        padding: 5px 10px;
        line-height: 20px;
        text-align: left;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        font-weight: 500;
        cursor: pointer;
      }
    }
  }
  .base {
    width: 100%;
    height: calc(100% - 50px);
    overflow-y: scroll;
    overflow-x: hidden;
    font-size: 16px;
    .info {
      width: 100%;
      padding: 12px;

      font-weight: 500;
      .contain {
        width: 100%;
        border: 1px solid #f2f2f2;
        padding: 12px;
        img {
          width: 35%;
        }
        div {
          // float: right;
          height: 100%;
          width: 100%;
        }
        .jd-title {
          font-size: 24px;
        }
      }
    }
  }
}
</style>
