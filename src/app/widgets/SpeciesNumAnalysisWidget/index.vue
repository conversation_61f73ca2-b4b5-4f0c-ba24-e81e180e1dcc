<template>
  <div class="jimu-widget wrap" v-if="isLoad">
    <a-row :gutter="[8, 8]">
      <a-col :span="24" v-for="(item, index) in config" :key="index">
        <Card class="card" :title="item.label">
          <PlainStatistic
            :value="item.value"
            :unit="item.unit"
            :color="color"
          />
        </Card>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import BaseWidget from '@/BaseWidget'
import PlainStatistic from '@/app/Components/PlainStatistic'
import Card from '@/app/Components/Card'

export default {
  name: 'SpeciesNumAnalysisWidget',

  mixins: [BaseWidget],

  components: {
    Card,
    PlainStatistic
  },

  data() {
    return {
      isLoad: false,
      filters: [],
      levelParam: ['PXZQDM', 'CXZQDM', 'FXZQDM'],
      level: 0,
      adminCode: '45',
      adminData: null,
      count: null,
      color: '#4065E0'
    }
  },

  mounted() {
    this.bus.$on(this.$events.admin.CHANGE_ADMIN, this.adminFilterUpdateHandler)
    this.bus.$on('setSpeciesfilters', this.setFilter)
  },

  configLoaded() {
    this.isLoad = true
    this.getData()
  },

  methods: {
    setFilter(e) {
      this.filters = e
      if (e) {
        this.getData()
      }
    },
    adminFilterUpdateHandler(e) {
      if (e) {
        this.adminData = e
        this.adminCode = e.code
        this.level = e.level
      } else {
        this.adminData = null
        this.adminCode = '45'
        this.level = 0
      }
      this.getData()
    },

    getData() {
      this.config.forEach(async item => {
        if (item.request) {
          let filter = [
            'all',
            ['=', this.levelParam[this.level], this.adminCode],
            ...this.filters
          ]
          if (filter.length == 2) {
            filter = filter[1]
          }

          let params = {
            returnGeometry: false,
            format: 'json',
            filter,
            aggregates: [['_id', 'count']]
          }
          let res = await this.$apis.feature.aggregate(item.table, params)
          item.value = res[0]['_id_count']
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.wrap{
  width: 250px;
}
.card {
  width: 250px;
}
</style>