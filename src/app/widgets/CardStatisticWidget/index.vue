<template>
  <div class="jimu-widget" v-if="isLoad">
    <a-row :gutter="[8, 8]">
      <a-col :span="24" v-for="(item, index) in dataList" :key="index">
        <Card :title="item.label" v-show="item.show">
          <PlainStatistic :value="item.value" :unit="item.unit" :color="item.color || color" />
        </Card>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
import PlainStatistic from "@/app/Components/PlainStatistic";
import Card from "@/app/Components/Card";

export default {
  name: "CardStatisticWidget",
  mixins: [BaseWidget],
  components: {
    Card,
    PlainStatistic,
  },
  data() {
    return {
      isLoad: false,
      adminCode: "45",
      data: {},
      adminData: null,
      levelParam: ["PXZQDM", "CXZQDM", "FXZQDM", "TXZQDM", "VXZQDM"],
      level: 0,
      region: "province",
      color: "#4065E0",
      filters: {},
      filterAfter: {},
      dataList: [],
      filterData: null,
    };
  },
  async configLoaded() {
    this.isLoad = true;
    let filterList = this.config.dataList.filter((item) => item.dropFilter);
    let dropEvents = filterList.map((item) => item.dropFilter.eventName);
    console.log("dropEvents", dropEvents);
    dropEvents = dropEvents.filter((item, index) => dropEvents.indexOf(item) === index);
    if (dropEvents.length == 0) {
      this.getData();
      return;
    }
    for (const event of dropEvents) {
      this.bus.$on(event, async (data) => {
        this.filterData = data;
        let filter = {};
        let filterAfter = {};
        for (const item of this.config.dataList) {
          if (!item.dropFilter) continue;
          filter[item.formName] = [];
          filterAfter[item.formName] = [];
          for (const items of item.dropFilter.filters) {
            let info = data.find((infoItem) => infoItem.id === items.id);
            if (info) {
              if (items.judge === "table") {
                let matchId = await this.requestSubFormFilter(info.value, items);
                if (items.filterAfter) {
                  filterAfter[item.formName].push(["in", items.mainTableField, matchId]);
                } else {
                  filter[item.formName].push(["in", items.mainTableField, matchId]);
                }
              } else {
                let value = items.judge === "like" ? `%${info.value}%` : info.value;
                filter[item.formName].push([items.judge, items.field, value]);
              }
            }
          }
        }
        this.$set(this.filters, event, filter);
        this.$set(this.filterAfter, event, filterAfter);
      });
    }
  },
  mounted() {
    console.log("CardStatisticWidget组件加载完毕");
    this.bus.$on(this.$events.admin.CHANGE_ADMIN, this.adminFilterUpdateHandler);
  },
  watch: {
    filters: {
      deep: true,
      handler(newVal) {
        console.log("CardStatisticWidget-filters参数变化", newVal);
        this.getData();
      },
    },
    adminCode() {
      this.getData();
    },
  },
  methods: {
    async requestSubFormFilter(value, data) {
      const params = {
        returnGeometry: false,
        format: "json",
        filter: ["=", data.filterField, value],
      };
      let res = await this.$apis.feature.query(data.table, params);
      let result = res.map((item) => item[data.tableField]);
      return result;
    },
    adminFilterUpdateHandler(e) {
      console.log("adminFilterUpdateHandler", e);
      if (e) {
        this.adminData = e;
        this.adminCode = e.code;
        this.region = e.region;
        this.level = e.level;
      } else {
        this.adminData = null;
        this.adminCode = "45";
        this.region = "province";
        this.level = 0;
      }
      this.getData();
    },
    async getData() {
      let list = [];
      for (const item of this.config.dataList) {
        item.show = true;
        if (item.showFilter == "none") {
          let show = !this.filterData || this.filterData.length == 0;
          item.show = show;
        } else if (typeof item.showFilter == "string") {
          let index = this.filterData.findIndex((items) => items.id == item.showFilter);
          item.show = index > -1;
        }
        // TODO 处理过滤条件 待优化
        if (item.fixed) {
          list.push(item);
        } else {
          let data = JSON.parse(JSON.stringify(item));
          let info = await this.requestData(data);
          console.log("CardStatisticWidget-requestData数据", info);

          // 处理拼接label
          if (info.splitLabel) {
            let label = info.label;
            this.filterData.forEach((item) => {
              if (item.data.cardLabel) label += item.data.cardLabel;
            });
            info.label = label;
          }
          list.push(info);
        }
      }
      this.dataList = list;
    },
    // 获取过滤参数
    getFilter(info) {
      let filter = [];
      if (info.adminFilter) filter.push(["=", this.levelParam[this.level], this.adminCode]);
      if (info.filter) filter.push(...info.filter);
      if (info.dropFilter && info.dropFilter.filters.length > 0) {
        let dropFilter = this.filters[info.dropFilter.eventName][info.formName];
        if (dropFilter) filter.push(...dropFilter);
      }
      if (filter.length > 1) filter.unshift("all");
      if (filter.length == 1) filter = filter[0];
      filter = filter.length == 0 ? null : filter;
      return filter;
    },
    // 请求数据
    async requestData(info) {
      try {
        let filter = this.getFilter(info);
        let url = info.formName;
        if (info.splitAdmin) url = `${info.formName}${this.region}`;
        if (info.requestType === "query") {
          let params = {
            returnGeometry: false,
            format: "json",
            filter,
          };
          let res = await this.$apis.feature.query(url, params);
          let result = this.handleFilterData(res, this.filterAfter[info.dropFilter.eventName][url]);
          info.value = result;
        } else {
          let params = {
            filter,
            aggregates: [info.aggregates],
          };
          if (info.groupFields && info.groupFields.length > 0) params.groupFields = info.groupFields;
          let res = await this.$apis.feature.aggregate(url, params);
          if (info.groupFields && info.groupFields.length > 0) {
            info.value = res.length;
          } else {
            info.value = res[0][`${info.aggregates[0]}_${info.aggregates[1]}`];
          }
        }
      } catch (error) {
        info.value = 0;
      }
      return info;
    },
    handleFilterData(data, filter) {
      if (!filter && filter.length == 0) return data.length;
      let result = data;
      filter.forEach((item) => {
        switch (item[0]) {
          case "in":
            result = data.filter((items) => item[2].includes(items[item[1]]));
            break;
        }
      });
      return result.length;
    },
  },
};
</script>

<style lang="less" scoped>
.jimu-widget {
  // background-color: antiquewhite;
  .count-box {
    width: 200px;
    height: 70px;
    display: flex;
    font-size: 18px;
    align-items: center;
    justify-content: space-around;
    color: #002159;
    margin-bottom: 18px;
    background: white;
    border-radius: 3px;
    box-shadow: 0px 3px 10px 0px rgba(56, 139, 201, 0.3);
    img {
      height: 30px;
    }
    .count {
      font-size: 20px;
    }
  }
}

/deep/ .ant-card {
  margin-bottom: 3px;
}
</style>
