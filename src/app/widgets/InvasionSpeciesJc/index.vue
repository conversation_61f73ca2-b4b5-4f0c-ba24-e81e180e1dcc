<template>
  <div class="jimu-widget jimu-tool-widget box">
    <div class="tabs-content">
      <div
        :class="['tabs', item == currentTab ? 'active' : '']"
        v-for="(item, index) in tabs"
        :key="index"
        @click="currentTab = item"
      >
        {{ item }}
      </div>
    </div>

    <div class="content scrollbar" v-show="currentTab == '项目简介'">
      <div v-for="(item, index) in profile" :key="index">
        <div class="content_title">{{ item.title }}</div>
        <div
          class="content_font"
          style="text-align: justify; text-indent: 2em"
          v-for="(i, index) in item.info"
          :key="index"
        >
          <p v-html="i"></p>
        </div>
      </div>
    </div>

    <div class="content" v-show="currentTab == '调查监测样方数量'">
      <TitleBar :title="'调查监测样方数量'"></TitleBar>
      <Echart :options="option"></Echart>
    </div>
  </div>
</template>

<script>
import TitleBar from "../../Components/TitleBar";
import Echart from "@/components/Chart/";
import BaseWidget from "@/BaseWidget";
export default {
  name: "PlantSpeciesJcEchart",

  mixins: [BaseWidget],

  components: {
    TitleBar,
    Echart,
  },

  data() {
    return {
      currentTab: "项目简介",
      tabs: ["项目简介", "调查监测样方数量"],
      profile: [],
      params: {
        code: 45,
        level: 0,
        date: "",
        name: "广西",
        crop: "",
      },
      type: ["PXZQDM", "CXZQDM", "FXZQDM", "FXZQDM"],
      name: ["PXZQMC", "CXZQMC", "FXZQMC", "FXZQMC"],
      option: {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        grid: {
          left: "3%",
          right: "4%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          boundaryGap: [0, 0.1],
        },
        yAxis: {
          type: "category",
          data: [],
          axisLabel: {
            textStyle: {
              fontSize: 16,
              fontFamily: "PingFangSC-Semibold, PingFang SC",
            },
          },
          axisTick: {
            show: false,
          },
        },
        series: [
          {
            type: "bar",
            barWidth: 30,
            data: [],
          },
        ],
      },
    };
  },
  methods: {
    formatData(value) {
      if (!value) {
        this.params = {
          code: 45,
          level: 0,
          date: "",
          name: "广西",
          crop: "",
        };
      } else {
        this.params.code = value.code;
        this.params.level = value.level;
        this.params.name = value.name;
      }
      this.getData([
        ["=", "date", "2020"],
        ["all", "month", "3"],
      ]);
    },
    async getData(e) {
      this.option.yAxis.data = [];
      this.option.series[0].data = [];
      let filter = [
        "all",
        ["=", this.type[this.params.level], this.params.code],
      ];
      if (e) filter.push(...e);
      // if (this.params.date) filter.push(['=', 'date', this.params.date])
      // if (this.params.crop) filter.push(['like', 'species', this.params.crop])

      if (filter.length == 2) filter = filter[1];

      let param = {
        returnGeometry: false,
        format: "json",
        filter,
        groupFields: [this.name[this.params.level + 1]],
        aggregates: [["_id", "count", "desc"]],
      };
      let res = await this.$apis.feature.aggregate("species_survey_gx", param);

      res.forEach((item) => {
        this.option.yAxis.data.unshift(item[this.name[this.params.level + 1]]);
        this.option.series[0].data.unshift(item._id_count);
      });
    },
  },
  mounted() {
    this.formatData(this.params);
    this.bus.$on("setSpeciesfilters", this.getData);
    this.bus.$on(this.$events.admin.CHANGE_ADMIN, this.formatData);
  },
  allLoaded() {
    // 定时为了等待其他组件先加载完毕
    setTimeout(async () => {
      if (Array.isArray(this.config.profile)) {
        this.profile = this.config.profile;
      }
    }, 500);
  },
};
</script>

<style lang="less" scoped>
.tabs-content {
  display: flex;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #e7ebf4;
  overflow: hidden;
  margin-bottom: 1vh;
  .tabs {
    width: 50%;
    height: 40px;
    text-align: center;
    // border: 1px solid #ccc;
    line-height: 40px;
    cursor: pointer;
  }

  .active {
    border-bottom: none;
    // color: rgb(62, 145, 247);
    color: #fff;
    background-color: #4065e0;
  }
}
.box {
  padding: 12px;
}

.scrollbar {
  height: 100%;
  overflow-y: scroll;
  // 隐藏滚动条
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.content {
  height: calc(100% - 65px);
  display: flex;
  flex-direction: column;
  // overflow-y: scroll;
  .content_title {
    font-size: 22px;
    font-weight: bold;
    color: #333;
    margin: 10px 0;
    line-height: 1.5;
  }
  .content_font {
    font-size: 18px;
    color: #555;
    margin: 10px 0;
    line-height: 1.6;
  }
}
</style>
