<template>
  <div class="jimu-widget select-list" v-if="isLoad">
    <div class="box">
      <template v-for="(item, index) in config">
        <div class="item" :key="index">
          <template v-if="item.method == 'like'">
            <a-select
              class="select-wrap"
              :placeholder="'全部' + item.name"
              mode="multiple"
              @change="onlikeChange($event, item.name)"
            >
              <template v-for="(el, i) in item.list">
                <a-select-option :key="i" :value="el.value">
                  {{ el.label }}
                </a-select-option>
              </template>
            </a-select>
          </template>
          <template v-else>
            <a-select
              class="select-wrap"
              :placeholder="'请选择' + item.name"
              v-model="item.selected"
              @change="onChange($event, item.name)"
            >
              <template v-for="(el, i) in item.list">
                <a-select-option :key="i" :value="el.value">
                  {{ el.label }}
                </a-select-option>
              </template>
            </a-select>
          </template>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
export default {
  name: "FarmLayerContralWidget",
  mixins: [BaseWidget],
  data() {
    return {
      isLoad: false,
      tableFilters: null,
      likeFilters: null,
    };
  },
  configLoaded() {
    this.isLoad = true;
  },
  computed: {},
  methods: {
    async onlikeChange(val, type) {
      if (type === "经营模式") {
        this.jyms = val;
        let filter = [];
        if (val) {
          val.forEach((item) => {
            if (item != "所有") {
              filter.push(["like", "jyms", item]);
            }
          });
          if (filter.length > 1) {
            filter.unshift("any");
          }
          if (filter.length == 0) {
            filter = null;
          }
          if (filter?.length == 1) {
            filter = filter[0];
          }
          this.likeFilters = filter ? [filter] : null;

          let res = await this.$apis.feature.query("farm_gx", { filter });
          this.map.getSource("eco_farm_point").setData(res);
          this.bus.$emit("setAnalysisFilterCount", [
            ...(this.tableFilters ? this.tableFilters : []),
            ...(this.likeFilters ? this.likeFilters : []),
          ]);
        }
      }
    },
    onChange(val, type) {
      if (type === "农场类型") {
        this.ncplx = val;
      }
      if (type === "组织模式") {
        this.zzms = val;
      }
      if (type === "年份") {
        this.date = val;
      }
      if (type === "生态农场级别") {
        this.nclb = val;
      }

      this.setFilter();
    },
    setFilter() {
      let filters = [];
      let tableFilters = [];
      if (this.ncplx) {
        filters.push(["==", "nclx", this.ncplx]);
        tableFilters.push(["=", "nclx", this.ncplx]);
      }
      if (this.zzms) {
        filters.push(["==", "zzms", this.zzms]);
        tableFilters.push(["=", "zzms", this.zzms]);
      }

      if (this.date) {
        filters.push(["==", "date", this.date]);
        tableFilters.push(["=", "date", this.date]);
      }

      if (this.nclb) {
        filters.push(["==", "level", this.nclb]);
        tableFilters.push(["=", "level", this.nclb]);
      }

      this.tableFilters = tableFilters;
      if (filters.length > 1) {
        filters.unshift("all");
      }
      if (filters.length == 1) {
        filters = filters[0];
      }
      console.log("setFilter", filters);

      this.setModuleLayerFilter(
        "eco_farm_point",
        "filterClass",
        filters.length ? filters : null
      );
      this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "eco_farm_point");

      this.bus.$emit("setAnalysisFilterCount", [
        ...(this.tableFilters ? this.tableFilters : []),
        ...(this.likeFilters ? this.likeFilters : []),
      ]);
    },
  },
};
</script>

<style lang="less" scoped>
.jimu-widget {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.select-list {
  display: flex;
  align-items: center;
}
.box {
  display: flex;
}
.item {
  display: flex;
  line-height: 2;
  margin-right: 10px;
  .label {
    margin-right: 20px;
  }
  .field-item {
    margin-right: 20px;
  }
  .field-item:hover {
    cursor: pointer;
  }
  .active {
    color: rgb(68, 104, 216);
  }
}

.select-wrap {
  width: 100%;
  display: flex;
  position: relative;
  align-items: center;
  padding: 7px 14px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #e7ebf4;
  height: 40px;
  box-shadow: 2px 2px 3px rgba(0, 0, 0, 0.2);
  font-size: 17px;
}

/deep/.ant-select {
  min-width: 220px;
}

/deep/ .ant-select-selection--single {
  border: 0;
  width: 100%;
  border-color: #fff;
}

/deep/ .ant-select-selection--multiple {
  width: 100%;
  border: none;
}

/deep/ .ant-select-selection__placeholder {
  color: rgba(0, 0, 0, 0.65);
}
</style>
