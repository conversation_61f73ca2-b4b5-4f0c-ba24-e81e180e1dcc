<template>
  <div class="jimu-widget wrap" v-if="isLoad">
    <template v-for="(item, index) in config">
      <div class="select-item" :key="index">
        <a-select v-model="item.value" @change="e => onChange(e, item)">
          <template v-for="(i, n) in item.list">
            <a-select-option :key="n" :value="i.value">
              {{ i.label }}
            </a-select-option>
          </template>
        </a-select>
      </div>
    </template>
  </div>
</template>

<script>
import BaseWidget from '@/BaseWidget'
export default {
  name: 'SpeciesLayerContralWidget',
  mixins: [BaseWidget],
  data() {
    return {
      isLoad: false
    }
  },
  computed: {
    date() {
      return this.config.find(item => {
        return item.name == '年份'
      }).value
    },
    crop() {
      return this.config.find(item => {
        return item.name == '物种'
      }).value
    }
  },

  async allLoaded() {
    this.isLoad = true
    await this.initConfig()
  },
  methods: {
    async initConfig() {
      for (let i = 0; i < this.config.length; i++) {
        if (this.config[i].listType == 'request') {
          let res = await this.$apis.feature.query('species_list', {
            returnGeometry: false,
            format: 'json',
            outFields: ['name']
          })
          res.forEach(item => {
            this.config[i].list.push({ label: item.name, value: item.name })
          })
        }
        this.onChange(this.config[i].value, this.config[i])
      }
    },
    async setlikeFilter(e, item) {
      let tcRes = await this.$apis.feature.query('species_survey_tc_gx', {
        filter: e ? ['like', item.field, e] : null
      })
      let yfdcRes = await this.$apis.feature.query('species_survey_yfdc_gx', {
        filter: e ? ['like', item.field, e] : null
      })
      this.map.getSource('species_survey_tc').setData(tcRes)
      this.map.getSource('species_survey_yfdc').setData(yfdcRes)
    },
    async onChange(e, item) {
      if (item.method == 'like') {
        this.setlikeFilter(e, item)
      } else {
        this.setFilter(e, item)
      }
      const filterData = this.config
        .filter(item => {
          return item.value
        })
        .map(item => {
          return [item.tableMethod, item.field, item.value]
        })
      this.bus.$emit('setSpeciesfilters', filterData)
    },
    setFilter(e, item) {
      if (e) {
        this.setModuleLayerFilter('species_survey_tc', item.field, [
          item.method,
          ['get', item.field],
          item.value
        ])
        this.setModuleLayerFilter('species_survey_yfdc', item.field, [
          item.method,
          ['get', item.field],
          item.value
        ])
      } else {
        this.setModuleLayerFilter('species_survey_tc', item.field, null)
        this.setModuleLayerFilter('species_survey_yfdc', item.field, null)
      }
      this.bus.$emit(this.$events.map.SET_LAYER_FILTER, 'species_survey_tc')
      this.bus.$emit(this.$events.map.SET_LAYER_FILTER, 'species_survey_yfdc')
    },
    unique(arr) {
      return Array.from(new Set(arr))
    }
  }
}
</script>

<style lang="less" scoped>
.wrap {
  display: flex;
  .select-item {
    background: #ffffff;
    border-radius: 4px;
    padding: 3px;
    box-shadow: 2px 2px 3px rgb(0 0 0 / 20%);
    margin-right: 12px;
  }
  /deep/.ant-select {
    min-width: 120px;
  }
  /deep/.ant-select-selection--single {
    border: none;
  }
}
</style>