<template>
  <div class="jimu-widget jimu-tool-widget charts">
    <div class="tabs-content">
      <div
        :class="['tabs', index == tabIndex ? 'active' : '']"
        v-for="(item, index) in tabs"
        :style="{ width: `${100 / tabs.length}%` }"
        :key="index"
        @click="changeTab(index)"
      >
        {{ item }}
      </div>
    </div>
    <a-spin :spinning="spinning" tip="加载中...">
      <div class="title-box">
        <div class="title">{{ cityName }}{{ ZWLX }}种植面积</div>
        <div class="num-box">
          <span class="value">
            {{
              this.$utils.number.bigDataFormat(sumTotal, 10000, 2, [
                "亩",
                "万亩",
              ]).value
            }}</span
          >
          <span class="unit">{{
            this.$utils.number.bigDataFormat(sumTotal, 10000, 2, ["亩", "万亩"])
              .unit
          }}</span>
        </div>
      </div>
    </a-spin>
    <div class="chart-box">
      <Chart :options="chartOption"></Chart>
    </div>
  </div>
</template>

<script>
import Chart from "@/components/Chart";
import BaseWidget from "@/BaseWidget";
import { city } from "./city";

export default {
  name: "RegionAnalysisWidget",

  mixins: [BaseWidget],
  data() {
    return {
      tabIndex: 0,
      tabs: ["全部", "安全利用", "严格管控类"],

      cityName: "广西",
      region: "city",
      provinceData: null,
      chartOption: {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          formatter: "{b}:{c}万亩",
        },
        grid: {
          left: "17%",
          right: "4%",
          top: "20px",
          bottom: "5%",
        },
        xAxis: {
          type: "value",
          position: "top",
        },
        yAxis: {
          type: "category",
          data: [],
        },
        series: [
          {
            name: "农用地安全利用率",
            type: "bar",
            data: [],
          },
        ],
      },
      regionParam: {
        city: "CXZQDM",
        county: "FXZQDM",
        town: "TXZQDM",
        village: "XZQDM",
      },
      sumTotal: 0,
      ZWLX: "",

      dateValue: "",
      verificationTypeValue: "",
      cropTypeValue: "",

      spinning: false,
    };
  },
  components: { Chart },
  mounted() {
    this.bus.$on("CropPlantingSelect", this.getFilter);
    this.bus.$on(
      this.$events.admin.CHANGE_ADMIN,
      this.adminFilterUpdateHandler
    );
  },
  methods: {
    changeTab(index) {
      this.tabIndex = index;
    },
    getFilter(value) {
      this.dateValue = "";
      this.verificationTypeValue = "";
      this.cropTypeValue = "";

      value.forEach((item) => {
        const { id, value: itemValue } = item;
        if (id === "date") return;
        if (id === "verificationType") {
          this.verificationTypeValue = itemValue;
          return;
        }
        if (id === "cropType") {
          this.cropTypeValue = itemValue;
          if (this.verificationTypeValue) {
            const name =
              this.verificationTypeValue === "ZZZW" ? "早稻核查" : "晚稻核查";
            this.ZWLX = name + itemValue;
          } else {
            this.ZWLX = `早稻晚稻核查` + itemValue;
          }
          return;
        }
      });

      this.getData();
    },

    adminFilterUpdateHandler(e) {
      if (e) {
        this.adminFilter = ["=", this.regionParam[e.region], e.code];
        this.region = e.nextRegion;
        this.cityName = e.name;
        this.cityCode = e.CXZQDM;
      } else {
        this.adminFilter = null;
        this.region = "city";
        this.cityName = "广西";
        this.cityCode = null;
      }
      this.getData();
    },

    async getData() {
      this.spinning = true;
      // 处理过滤参数
      let processedFilter = this.processedFilter();

      let groupFields = {
        city: ["CXZQDM", "CXZQMC"],
        county: ["FXZQDM", "FXZQMC"],
        town: ["TXZQDM", "TXZQMC"],
      };
      let qqhc_params = {
        returnGeometry: false,
        format: "json",
        groupFields: [...groupFields[this.region]],
        aggregates: [["SCMJM", "sum"]],
        filter: processedFilter,
        orderType: "DESC",
      };
      let wqqhc_params = {
        format: "json",
        groupFields: [...groupFields[this.region]],
        aggregates: [["MJ_MU", "sum"]],
        filter: processedFilter,
        orderType: "DESC",
      };

      let res = {
        qqhcData: [],
        wqqhcData: [],
      };

      // 按地市查询
      if (this.cityCode) {
        await this.$apis.feature
          .aggregate(`aqly_qqhc_${this.cityCode}`, qqhc_params)
          .then((data) => {
            if (data && data.length > 0) {
              res.qqhcData.push(...data);
            }
          });

        await this.$apis.feature
          .aggregate(`aqly_wqqhc_${this.cityCode}`, wqqhc_params)
          .then((data) => {
            if (data && data.length > 0) {
              res.wqqhcData.push(...data);
            }
          });

        this.mergeData(res);
      } else {
        // 查询所有地市数据
        const promises = city.map((item) => {
          return Promise.all([
            this.$apis.feature
              .aggregate(`aqly_qqhc_${item.code}`, qqhc_params)
              .then((data) => {
                if (data && data.length > 0) {
                  res.qqhcData.push(...data);
                }
              }),
            this.$apis.feature
              .aggregate(`aqly_wqqhc_${item.code}`, wqqhc_params)
              .then((data) => {
                if (data && data.length > 0) {
                  res.wqqhcData.push(...data);
                }
              }),
          ]);
        });

        Promise.all(promises)
          .then(() => {
            this.mergeData(res);
          })
          .catch((error) => {
            console.error("查询失败", error);
          });
      }
    },
    // 合并数据
    mergeData(res) {
      let groupFields = {
        city: ["CXZQDM", "CXZQMC"],
        county: ["FXZQDM", "FXZQMC"],
        town: ["TXZQDM", "TXZQMC"],
      };
      let label = [];
      let data = [];
      this.sumTotal = 0;
      let regionFieldIndex = groupFields[this.region][1];

      let mergedData = [];
      let dataMap = {};

      res.qqhcData.forEach((item) => {
        if (item[regionFieldIndex] !== null) {
          dataMap[item[regionFieldIndex]] = {
            ...item,
            SCMJM_sum: item.SCMJM_sum ? item.SCMJM_sum : 0,
          };
        }
      });

      res.wqqhcData.forEach((item) => {
        if (dataMap[item[regionFieldIndex]]) {
          dataMap[item[regionFieldIndex]].MJ_MU_sum = item.MJ_MU_sum
            ? item.MJ_MU_sum
            : 0;
        } else {
          dataMap[item[regionFieldIndex]] = {
            ...item,
            SCMJM_sum: 0,
            MJ_MU_sum: item.MJ_MU_sum ? item.MJ_MU_sum : 0,
          };
        }
      });
      // 将合并后的数据存入mergedData
      mergedData = Object.values(dataMap);
      console.log("mergedData", mergedData);

      mergedData.forEach((item) => {
        this.sumTotal += item.MJ_MU_sum + item.SCMJM_sum;
        label.push(item[regionFieldIndex]);
        data.push(
          parseFloat((item.MJ_MU_sum + item.SCMJM_sum) / 10000).toFixed(2)
        );
      });

      this.$set(this.chartOption, "yAxis", {
        type: "category",
        data: label,
        axisLabel: {
          show: true,
          textStyle: {
            fontSize: 15, //更改坐标轴文字大小
          },
        },
        formatter: function (params) {
          return params.slice(0, 3) + "...";
        },
      });
      this.$set(this.chartOption, "series", [
        {
          name: "面积",
          type: "bar",
          barWidth: 15,
          showBackground: true,
          backgroundStyle: {
            color: "rgba(180, 180, 180, 0.2)",
          },
          itemStyle: {
            color: "rgb(179, 179 ,0)",
          },
          data: data.sort((a, b) => a - b),
          label: {
            show: true,
            position: "right",
            formatter: "{c}万亩",
            color: "#000",
          },
        },
      ]);
      this.spinning = false;
    },
    processedFilter() {
      let filter = [];
      if (this.tabIndex > 0) {
        filter = ["all", ["=", "ZH_CLASS", this.tabIndex == 1 ? "2" : "3"]];
      } else if (this.adminFilter) {
        filter.unshift("all");
      }
      if (this.adminFilter) {
        filter.push(this.adminFilter);
      }
      const verificationCondition = this.verificationTypeValue
        ? ["=", this.verificationTypeValue, this.cropTypeValue]
        : [
            "any",
            ["like", "ZZZW", this.cropTypeValue],
            ["like", "WZZW", this.cropTypeValue],
          ];
      filter.push(verificationCondition);

      if (Array.isArray(filter) && filter[0] === "all") {
        return filter;
      }
      if (Array.isArray(filter)) {
        return filter.flat(1);
      }
      return filter;
    },
  },
  watch: {
    tabIndex() {
      this.getData();
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";
.tabs-content {
  display: flex;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #e7ebf4;
  overflow: hidden;
  margin-bottom: 10px;
}
.tabs {
  width: 50%;
  height: 40px;
  text-align: center;
  // border: 1px solid #ccc;
  line-height: 40px;
  cursor: pointer;
}
.content {
  height: calc(100% - 65px);
  display: flex;
  flex-direction: column;
}
.active {
  border-bottom: none;
  // color: rgb(62, 145, 247);
  color: #fff;
  background-color: @primaryColor;
}

.charts {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.title-box {
  display: flex;
  flex-direction: column;
  background-color: #e6eaea;
  padding: 12px;

  .title {
    font-size: 18px;
    padding-left: 10px;
    font-weight: 550;
  }
  .num-box {
    margin-top: 12px;
    text-align: center;
    font-weight: 550;
    .value {
      font-size: 30px;
      font-weight: 550;
      color: rgb(179, 179, 0);
    }
    .unit {
      margin-left: 10px;
      font-size: 16px;
    }
  }
}
.chart-box {
  margin-top: 20px;
  width: 95%;
  flex: 1;
}
</style>
