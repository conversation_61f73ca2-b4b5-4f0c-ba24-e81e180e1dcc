<template>
  <div class="jimu-widget">
    <div v-if="isLoad">
      <Card
        class="card"
        v-for="(item, index) in config"
        :key="index"
        :title="item.name"
      >
        <PlainStatistic :value="item.count" unit="个" :color="color" />
      </Card>
      <Card class="card" title="自治区级生态农场数量">
        <PlainStatistic :value="provinceCount" unit="个" :color="color" />
      </Card>
    </div>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
import PlainStatistic from "@/app/Components/PlainStatistic";
import Card from "@/app/Components/Card";

export default {
  name: "BaseNumAnalysisWidget",

  mixins: [BaseWidget],

  components: {
    Card,
    PlainStatistic,
  },

  data() {
    return {
      isLoad: false,
      filters: [],
      levelParam: ["PXZQDM", "CXZQDM", "FXZQDM"],
      level: 0,
      adminCode: "45",
      adminData: null,
      count: null,
      color: "#4065E0",
      date: "全部",
      provinceCount: 0,
    };
  },
  mounted() {
    this.bus.$on(
      this.$events.admin.CHANGE_ADMIN,
      this.adminFilterUpdateHandler
    );
    this.bus.$on("setAnalysisFilterCount", this.setFilter);
    this.bus.$on("stncDate", (e) => {
      this.date = e;
    });
  },
  configLoaded() {
    this.isLoad = true;
    this.getData();
  },
  watch: {
    date(newValue) {
      this.getData();
    },
  },
  methods: {
    setFilter(e) {
      this.filters = e;
      if (e) {
        this.getData();
      }
    },
    adminFilterUpdateHandler(e) {
      if (e) {
        this.adminData = e;
        this.adminCode = e.code;
        this.level = e.level;
      } else {
        this.adminData = null;
        this.adminCode = "45";
        this.level = 0;
      }
      this.getData();
    },
    async getData() {
      this.getProvinceCount();
      for (const config of this.config) {
        let filter = [
          "all",
          ["=", this.levelParam[this.level], this.adminCode],
          ...this.filters,
        ];
        if (this.date !== "全部") {
          filter.push(["=", "date", this.date]);
        }
        if (config.filter) {
          filter = [...filter, config.filter];
        }
        if (filter.length == 2) {
          filter = filter[1];
        }
        let params = {
          returnGeometry: false,
          format: "json",
          filter,
          aggregates: [["ncbm", "count", null, "distinct"]],
        };

        let res = await this.$apis.feature.aggregate(config.table, params);
        console.log("res", res);

        config.count = res[0]["ncbm_count"];
      }
    },
    async getProvinceCount() {
      let params = {
        returnGeometry: false,
        format: "json",
      };
      let data = await this.$apis.feature.query("farm_gx", params);
      let count = 0;
      let date = this.date;
      // 找省级数据
      let province = data.filter((item) => {
        if (date !== "全部") {
          return item.level == "自治区级" && item.date == date;
        } else {
          return item.level == "自治区级";
        }
      });
      count = province.length;
      if (date == "全部") {
        this.provinceCount = count;
        return;
      }
      province.forEach((item) => {
        // 找年份相等的国家级数据
        let has = data.findIndex(
          (items) =>
            items.level == "国家级" &&
            items.date == item.date &&
            items.ncbm == item.ncbm
        );
        if (has > -1) count -= 1;
      });
      this.provinceCount = count;
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";

.card {
  width: 280px;
  margin-bottom: 13px;
}
</style>
