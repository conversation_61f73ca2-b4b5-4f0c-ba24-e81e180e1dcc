<template>
  <div class="jimu-widget wrap" v-if="isLoad">
    <template v-for="(item, index) in config">
      <div class="select-item" :key="index">
        <a-select v-model="item.value" @change="(e) => onChange(e, item)">
          <template v-for="(i, n) in item.list">
            <a-select-option :key="n" :value="i.value">
              {{ i.label }}
            </a-select-option>
          </template>
        </a-select>
      </div>
    </template>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
export default {
  name: "PlantLayerContralWidget",
  mixins: [BaseWidget],
  data() {
    return {
      isLoad: false,
    };
  },
  computed: {
    date() {
      return this.config.find((item) => {
        return item.name == "年份";
      }).value;
    },
    semi_annual() {
      return this.config.find((item) => {
        return item.name == "时期";
      }).value;
    },
    crop() {
      return this.config.find((item) => {
        return item.name == "物种";
      }).value;
    },
  },

  async configLoaded() {
    this.isLoad = true;
    await this.initConfig();
    this.setFillDataColor();
  },
  methods: {
    async initConfig() {
      for (let i = 0; i < this.config.length; i++) {
        if (this.config[i].listType == "request") {
          let res = await this.$apis.feature.query("plant_list_gx", {
            returnGeometry: false,
            format: "json",
            outFields: ["cname"],
          });
          res.forEach((item) => {
            this.config[i].list.push({ label: item.cname, value: item.cname });
          });
        }
        this.onChange(this.config[i].value, this.config[i]);
      }
    },
    async setlikeFilter(e, item) {
      let res = await this.$apis.feature.query("plant_point_gx", {
        filter: e ? ["like", item.field, e] : null,
      });
      this.map.getSource("plant_ysjbh_point").setData(res);

      this.setFillDataColor(e);
    },
    async onChange(e, item) {
      if (item.method == "like") {
        this.setlikeFilter(e, item);
      } else {
        this.setFilter(e, item);
      }
      this.bus.$emit("setPlantfilters", {
        crop: this.crop,
        date: this.date,
        semi_annual: this.semi_annual,
      });
      const filterData = this.config
        .filter((item) => {
          return item.value;
        })
        .map((item) => {
          return [item.tableMethod, item.field, item.value];
        });
      this.bus.$emit("setAnalysisFilterCount", filterData);
    },
    setFilter(e, item) {
      if (e && item.field === "date") {
        this.setModuleLayerFilter("plant_ywzy", item.field, [
          item.method,
          ["get", item.field],
          item.value,
        ]);
        this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "plant_ywzy");
      }
      if (e) {
        this.setModuleLayerFilter("plant_ysjbh_point", item.field, [
          item.method,
          ["get", item.field],
          item.value,
        ]);
      } else {
        this.setModuleLayerFilter("plant_ysjbh_point", item.field, null);
      }
      this.bus.$emit(this.$events.map.SET_LAYER_FILTER, "plant_ysjbh_point");
    },
    async setFillDataColor(e) {
      let param = {
        format: "json",
        returnGeometry: false,
        outFields: ["FXZQDM"],
        filter: e ? ["=", "cname", e] : null,
      };
      let res = await this.$apis.feature.query("plant_resource_gx", param);
      let dmList = this.unique(
        res.map((item) => {
          return item.FXZQDM;
        })
      );
      let paintColor = dmList.length
        ? [
            "match",
            ["get", "FXZQDM"],
            dmList,
            "rgba(115, 201, 145,.7)",
            "rgba(0,0,0,0)",
          ]
        : "rgba(0,0,0,0)";
      this.setModuleLayerStyle(
        "land_plant_county_fill",
        "fill-color",
        paintColor
      );
      this.setModuleLayerFilter("land_plant_county_fill", "fill-color", [
        "match",
        ["get", "FXZQDM"],
        dmList,
        true,
        false,
      ]);
      this.bus.$emit(
        this.$events.map.SET_LAYER_FILTER,
        "land_plant_county_fill"
      );
      this.bus.$emit(
        this.$events.map.SET_LAYER_STYLE,
        "land_plant_county_fill"
      );
    },
    unique(arr) {
      return Array.from(new Set(arr));
    },
  },
};
</script>

<style lang="less" scoped>
.wrap {
  display: flex;
  .select-item {
    background: #ffffff;
    border-radius: 4px;
    padding: 3px;
    box-shadow: 2px 2px 3px rgb(0 0 0 / 20%);
    margin-right: 12px;
  }
  /deep/.ant-select {
    min-width: 120px;
  }

  /deep/.ant-select-selection--single {
    border: none;
  }
}
</style>
