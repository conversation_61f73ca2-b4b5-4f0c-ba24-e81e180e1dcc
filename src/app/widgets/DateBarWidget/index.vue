<template>
  <div class="jimu-widget wrap jimu-tool-widget">
    <div class="play">
      <a-icon type="play-circle" v-if="!isPlay" @click="playClick" />
      <a-icon type="pause-circle" v-if="isPlay" @click="stopClick" />
    </div>
    <div class="list">
      <template v-for="(item, index) in list">
        <div :class="['dot', thisIndex == index ? 'active' : '']" :key="item" @click="ItemClick(index)">
          <div :class="['dot-data', index % 2 ? 'up' : 'down']">
            {{ item }}
          </div>
        </div>
        <div
          class="line"
          :key="'line_' + index"
          v-if="index !== list.length - 1"
        ></div>
      </template>
    </div>
  </div>
</template>

<script>
import BaseWidget from '@/BaseWidget'
export default {
  name: 'DateBarWidget',
  mixins: [BaseWidget],
  data() {
    return {
      list: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],
      thisIndex: null,
      isPlay: false,
      mapTimer: null
    }
  },
  methods: {
    playClick() {
      this.isPlay = true
      this.mapTimer = setInterval(() => {
        if (this.thisIndex == this.list.length - 1) {
          this.thisIndex = null
          this.stopClick()
          return
        }
        if (this.thisIndex || this.thisIndex == 0) {
          this.thisIndex++
        } else {
          this.thisIndex = 0
        }
      }, 500)
    },
    stopClick() {
      this.isPlay = false
      clearInterval(this.mapTimer)
    },
    ItemClick(item){
      this.thisIndex = item
    }
  },
  watch: {
    thisIndex(e) {
      if (e || e == 0) {
        let value = this.list[e]
        this.config.layerId.forEach(item => {
          this.setModuleLayerFilter(item, 'month', [
            '==',
            ['get', 'month'],
            value
          ])
        })
      } else {
        this.config.layerId.forEach(item => {
          this.setModuleLayerFilter(item, 'month', null)
        })
      }
      this.config.layerId.forEach(item => {
        this.bus.$emit(this.$events.map.SET_LAYER_FILTER, item)
      })
    }
  }
}
</script>

<style lang="less" scoped>
.wrap {
  display: flex;
  background-color: rgba(7, 125, 211, 0.8);
  padding: 15px 40px;
  border-radius: 28px;
  .play {
    color: #fff;
    margin-right: 20px;
    font-size: 20px;
    &:hover {
      color: rgba(255, 107, 53, 0.8);
    }
  }
  .list {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .line {
      // width: 100%;
      flex: 1;
      height: 2px;
      background-color: #fff;
    }
    .dot {
      width: 10px;
      height: 10px;
      border-radius: 5px;
      border: 2px solid #fff;
      position: relative;
      color: #fff;
      &:hover{
        cursor: pointer;
      }
    }

    .dot-data {
      position: absolute;
      left: -30%;
    }
    .active {
      // border: 2px solid rgb(255, 107, 53);
      // background-color: rgb(255, 107, 53);

      position: relative;
    }
    .active::after {
      content: '';
      position: absolute;
      width: 13px;
      height: 13px;
      top: -3px;
      left: -3px;
      transition: 0.3s;
      border: 3px solid #ff6b35;
      transform: scale(2);
      border-radius: 50%;
    }
    .up {
      bottom: 8px;
    }
    .down {
      top: 10px;
    }
  }
}
</style>