<template>
  <div class="jimu-widget wrap">
    <div class="select-item" v-if="list.length > 0">
      <a-select :defaultValue="selected" show-search placeholder="请选择任务" style="width: 100%" @change="handleChange" allowClear>
        <a-select-option :value="item[config.valueField]" v-for="item in list" :key="item[config.valueField]">
          {{ item[config.valueField] }}
        </a-select-option>
      </a-select>
    </div>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
export default {
  name: "InvasionSpeciesSelectYJJC",
  mixins: [BaseWidget],
  data() {
    return {
      list: [],
      selected: "",
    };
  },
  configLoaded() {
    this.init();
  },
  methods: {
    async init() {
      let res = await this.$apis.feature.query(this.config.table, {
        format: "json",
      });
      console.log(res, "res");
      this.list = res;

      let defaultValue = res[0][this.config.valueField];

      for (let i = 0; i < this.config.filterTables.length; i++) {
        const f = this.config.filterTables[i];
        if (defaultValue) {
          const filter = ["==", ["get", f.field], defaultValue];
          this.setModuleLayerFilter(f.key, "task", filter);
          this.bus.$emit(this.$events.map.SET_LAYER_FILTER, f.key);
        } else {
          this.setModuleLayerFilter(f.key, "task", null);
          this.bus.$emit(this.$events.map.SET_LAYER_FILTER, f.key);
        }
        this.bus.$emit("setJCYJTaskFilter", defaultValue);
      }
      this.selected = defaultValue;
      console.log(this.selected, "this.selected");
    },
    async handleChange(e) {
      console.log(e);
      for (let i = 0; i < this.config.filterTables.length; i++) {
        const f = this.config.filterTables[i];
        if (e) {
          const filter = ["==", ["get", f.field], e];
          this.setModuleLayerFilter(f.key, "task", filter);
          this.bus.$emit(this.$events.map.SET_LAYER_FILTER, f.key);
        } else {
          this.setModuleLayerFilter(f.key, "task", null);
          this.bus.$emit(this.$events.map.SET_LAYER_FILTER, f.key);
        }
        this.bus.$emit("setJCYJTaskFilter", e);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.wrap {
  display: flex;
  .select-item {
    background: #ffffff;
    border-radius: 4px;
    padding: 3px;
    box-shadow: 2px 2px 3px rgb(0 0 0 / 20%);
    margin-right: 12px;
    flex: 1;
  }
  /deep/.ant-select {
    min-width: 120px;
  }
  /deep/.ant-select-selection--single {
    border: none;
  }
}
</style>
