<template>
  <div class="jimu-widget jimu-tool-widget wrap">
    <div class="jimu-widget-title-ui">对比两次划分结果</div>
    <div class="data">
      <div class="block" v-for="item in changeDataValue" :key="item.value">
        <div class="label">{{ item.label }}</div>
        <div>
          <span class="value">{{ item.value }}</span
          ><span class="unit">{{ item.unit }}</span>
        </div>
        <div class="icon">
          <svg v-if="item.state == 'up'" width="16" height="16" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M5 24L24 6L43 24H31V42H17V24H5Z" fill="#333" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" />
          </svg>
          <svg v-if="item.state == 'down'" width="16" height="16" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M5 24L24 42L43 24H31V6H17V24H5Z" fill="#333" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" />
          </svg>
        </div>
      </div>
    </div>
    <div style="height: 100%">
      <div class="slot-title">
        <div class="jimu-widget-title-ui">对比两次划分面积变化情况</div>
        <div class="toggleTable" @click="toggleTable = !toggleTable">
          <span v-show="toggleTable" class="gt-icon gt-icon-table icon"></span>
          <span v-show="!toggleTable" class="gt-icon gt-icon-echarts icon"></span>
        </div>
      </div>
      <div class="select">
        <div class="label">质量类别：</div>
        <a-select v-model="classSelected" @change="onChange">
          <a-select-option v-for="(i, n) in columns" :key="n" :value="i.value">
            {{ i.label }}
          </a-select-option>
        </a-select>
      </div>
      <div class="chart-wrap" v-if="dataList.length > 0" :style="{ height: chartHeight + 'px' }">
        <Chart v-if="toggleTable" ref="chart" :options="option" />
        <a-table
          v-if="!toggleTable"
          :scroll="{ x: true, y: chartHeight - 70 + 'px' }"
          :columns="tableConfig"
          :data-source="tableData"
          @change="handleChange"
          :pagination="false"
        />
      </div>
    </div>
  </div>
</template>

<script>
import Chart from "@/components/Chart";
import BaseWidget from "@/BaseWidget";
export default {
  name: "CompareDataView",
  mixins: [BaseWidget],
  components: {
    Chart,
  },
  data() {
    return {
      isLoad: false,
      dataList: [],
      name: ["PXZQMC", "CXZQMC", "FXZQMC", "TXZQMC", "XZQMC", "XZQMC"],
      code: ["PXZQDM", "CXZQDM", "FXZQDM", "TXZQDM", "XZQDM", "XZQDM"],
      classSelected: "yxbh",
      typeSelected: "land",
      columns: [
        { label: "优先保护", value: "yxbh" },
        { label: "安全利用", value: "aqly" },
        { label: "严格管控", value: "yggk" },
      ],
      changeDataValue: [
        {
          label: "2020年类别划分",
          value: "0",
          unit: "万亩",
          state: "none",
        },
        {
          label: "2023年动态调整",
          value: "0",
          unit: "万亩",
          state: "none",
        },
        {
          label: "面积变化情况",
          value: "0",
          unit: "万亩",
          state: "down",
        },
      ],
      toggleTable: true,
      tableConfig: [
        {
          title: "行政区划",
          dataIndex: "name",
          fixed: true,
          key: "name",
        },
        {
          title: "2020年类别划分",
          dataIndex: "lbhf",
          key: "lbhf",
        },
        {
          title: "2023年动态调整",
          dataIndex: "dttz",
          key: "dttz",
        },
        {
          title: "面积变化情况",
          key: "area",
          dataIndex: "area",
        },
      ],
      tableData: [],
    };
  },
  computed: {
    option() {
      return {
        legend: {
          show: false,
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        grid: {
          left: "1%",
          right: "12%",
          bottom: "3%",
          top: "5%",
          containLabel: true,
        },
        xAxis: {
          name: "万亩",
          type: "value",
          position: "top",
          axisLabel: {
            formatter: "{value}",
          },
        },
        yAxis: {
          type: "category",
          data: this.dataList.map((item) => {
            let level = this.regionFilter ? this.regionFilter.level : 0;
            return item[this.name[level + 1]];
          }),
          axisLabel: {
            show: true,
            textStyle: {
              fontSize: 15, //更改坐标轴文字大小
            },
          },
          formatter: function (params) {
            return params.slice(0, 3) + "...";
          },
        },
        series: [
          {
            name: this.columns.find((item) => this.classSelected == item.value).label,
            type: "bar",
            stack: "total",
            data: this.dataList.map((item) => (item[`${this.typeSelected}_${this.classSelected}_cz`] / 10000).toFixed(2)),
            color: "rgb(0,179,0)",
            barWidth: 15,
          },
        ],
      };
    },
  },

  mounted() {
    this.chartHeight = window.innerHeight - 450;
    console.log(this.chartHeight);
    this.bus.$on(this.$events.admin.CHANGE_ADMIN, this.areaChange);
    this.bus.$on("compareSelect", this.typeChange);
    this.getData();
  },
  methods: {
    typeChange(e) {
      this.typeSelected = e.value;
      this.getData();
    },
    handleChange(pagination, filters, sorter) {
      // this.filteredInfo = filters;
      this.sortedInfo = sorter;
    },
    onChange(e) {
      this.classSelected = e;
      this.getData();
    },
    areaChange(e) {
      this.regionFilter = e ? e : null;
      this.getData();
    },
    getParams() {
      let param = {
        returnGeometry: false,
        format: "json",
      };
      if (this.regionFilter) {
        let dataLevel = this.code[this.regionFilter.level];
        param.filter = ["all", ["=", dataLevel, this.regionFilter.code], ["!=", "CXZQDM", "4500"]];
      } else {
        param.filter = ["!=", "CXZQDM", "4500"];
      }
      return param;
    },
    async getData() {
      let param = this.getParams();
      param.orderField = `${this.typeSelected}_${this.classSelected}_cz`;
      param.orderType = "DESC";
      let region = this.regionFilter ? this.regionFilter.nextRegion : "city";
      this.dataList = await this.$apis.feature.query(`land_plough_cz_${region}`, param);
      this.getTableData();
    },
    async getTableData() {
      let param = this.getParams();
      let region = this.regionFilter ? this.regionFilter.nextRegion : "city";
      let data = await this.$apis.feature.query(`land_plough_${region}`, param);
      // 统计表格数据
      let ary = [];
      let level = this.regionFilter ? this.regionFilter.level : 0;
      let name = this.name[level + 1];
      this.dataList.forEach((item) => {
        let obj = {
          name: item[name],
          area: item[`${this.typeSelected}_${this.classSelected}_cz`],
          lbhf: 0,
          dttz: 0,
        };
        data.forEach((items) => {
          if (items[name] == item[name]) {
            if (items.date == "2020") {
              obj.lbhf = items.land_area;
            } else if (items.date == "2023") {
              obj.dttz = items.land_area;
            }
          }
        });
        ary.push(obj);
      });
      this.tableData = ary;
      // 统计总数
      let area_2020 = 0;
      let area_2023 = 0;
      let area_cz = 0;
      this.dataList.forEach((item) => {
        area_cz += item.land_area_cz;
      });
      data.forEach((item) => {
        if (item.date == "2020") {
          area_2020 += Number(item.land_area);
        } else if (item.date == "2023") {
          area_2023 += Number(item.land_area);
        }
      });
      area_2020 = (area_2020 / 10000).toFixed(2);
      area_2023 = (area_2023 / 10000).toFixed(2);
      area_cz = area_cz / 10000;
      this.changeDataValue = [
        {
          label: "2020年类别划分",
          value: area_2020,
          unit: "万亩",
          state: "none",
        },
        {
          label: "2023年动态调整",
          value: area_2023,
          unit: "万亩",
          state: "none",
        },
        {
          label: "面积变化情况",
          value: Math.abs(area_cz).toFixed(2),
          unit: "万亩",
          state: area_cz == 0 ? "none" : area_cz < 0 ? "down" : "up",
        },
      ];
    },
  },
};
</script>

<style lang="less" scoped>
.wrap {
  padding-top: 20px;
  .select {
    margin: 20px 10px 0 10px;
    display: flex;
    .label {
      font-size: 20px;
      font-weight: bold;
    }
  }
  .data {
    background-color: #f8f8f8;
    padding: 20px;
    margin: 20px 10px;
    .block {
      display: flex;
      align-items: center;
      width: 100%;
      color: #000;
      .label {
        font-size: 20px;
        font-weight: bold;
        width: 45%;
      }
      .value {
        font-size: 30px;
        font-weight: bold;
        color: #2883e5;
      }
      .unit {
        margin-left: 10px;
      }
      .icon {
        margin-top: 10px;
        margin-left: 5px;
        display: flex;
        align-items: flex-end;
      }
    }
  }
  .slot-title {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .toggleTable {
    cursor: pointer;
    height: 25px;
    width: 25px;
    transform: translateY(-7px);
    .icon {
      font-size: 20px;
      font-weight: 800;
    }
  }
  .chart-wrap {
    width: 100%;
    // overflow-y: scroll;
  }
  /deep/.ant-select {
    min-width: 120px;
    flex: 1;
  }
  /deep/.ant-table td {
    white-space: nowrap;
  }
  /deep/.ant-table th {
    white-space: nowrap;
  }
}
</style>
