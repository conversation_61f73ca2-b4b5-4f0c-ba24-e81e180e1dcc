<template>
  <div class="jimu-widget jimu-tool-widget">
    <StatisticsSlot>
      <template>
        <span slot="title" class="jimu-widget-title-ui">规模种植主体情况</span>
      </template>
      <template>
        <div slot="contain" class="contain">
          <div class="data-content">
            <div
              class="data-item"
              v-for="(item, index) in plant"
              :key="item.label"
            >
              <div
                :style="{
                  backgroundColor:
                    index % 2 == 0
                      ? 'rgba(71, 215, 234, 0.1)'
                      : 'rgba(64, 101, 224, 0.1)',
                  color: item.color,
                }"
                class="icon-box"
              >
                <span
                  class="icon gt-icon"
                  :class="`gt-icon-${item.icon}`"
                ></span>
              </div>
              <div style="float: left">
                <div class="title">{{ item.label }}</div>
                <span class="value">{{ item.value }}</span>
                <span class="danwe">{{ item.unit }}</span>
              </div>
            </div>
          </div>
        </div>
      </template>
    </StatisticsSlot>
    <StatisticsSlot>
      <template>
        <span slot="title" class="jimu-widget-title-ui"
          >不同坡度耕地和园地面积</span
        >
      </template>
      <template>
        <div slot="contain" class="contain">
          <div class="data-content">
            <div
              class="data-item"
              v-for="(item, index) in diff.data"
              :key="item.label"
            >
              <div
                :style="{
                  backgroundColor:
                    index % 2 == 0
                      ? 'rgba(71, 215, 234, 0.1)'
                      : 'rgba(64, 101, 224, 0.1)',
                  color: item.color,
                }"
                class="icon-box"
              >
                <span
                  class="icon gt-icon"
                  :class="`gt-icon-${item.icon}`"
                ></span>
              </div>
              <div class="title">{{ item.label }}</div>
              <!-- <div class="value">{{ item.value }}{{ item.unit }}</div> -->
              <span class="value">{{ item.value }}</span>
              <span class="danwe">{{ item.unit }}</span>
            </div>
          </div>
          <Echart class="diff-chart" :options="diff.options"></Echart>
        </div>
      </template>
    </StatisticsSlot>
    <StatisticsSlot>
      <template>
        <span slot="title" class="jimu-widget-title-ui">耕地面积</span>
      </template>
      <template>
        <div slot="contain" class="contain">
          <div class="data-content">
            <div
              class="data-item"
              v-for="(item, index) in farm"
              :key="item.label"
            >
              <div
                :style="{
                  backgroundColor:
                    index % 2 == 0
                      ? 'rgba(71, 215, 234, 0.1)'
                      : 'rgba(64, 101, 224, 0.1)',
                  color: item.color,
                }"
                class="icon-box"
              >
                <span
                  class="icon gt-icon"
                  :class="`gt-icon-${item.icon}`"
                ></span>
              </div>
              <div class="title">{{ item.label }}</div>
              <!-- <div class="value">{{ item.value }}{{ item.unit }}</div> -->
              <span class="value">{{ item.value }}</span>
              <span class="danwe">{{ item.unit }}</span>
            </div>
          </div>
        </div>
      </template>
    </StatisticsSlot>
    <StatisticsSlot>
      <template>
        <span slot="title" class="jimu-widget-title-ui">园地面积</span>
      </template>
      <template>
        <div slot="contain" class="contain">
          <Echart class="garden-chart" :options="garden.options"></Echart>
        </div>
      </template>
    </StatisticsSlot>
  </div>
</template>

<script>
import StatisticsSlot from "@/app/StatisticsSlot";
import Echart from "@/components/Chart";
import BaseWidget from "@/BaseWidget";
const REGION_LIST = ["PXZQDM", "CXZQDM", "FXZQDM", "TXZQDM", "XZQDM"];
export default {
  name: "TypicalFieldAnalysisWidget",

  mixins: [BaseWidget],

  components: {
    StatisticsSlot,
    Echart,
  },

  data() {
    return {
      plant: [
        {
          label: "数量",
          icon: "dksl",
          value: "0",
          unit: "个",
          color: "#47D7EA",
        },
        {
          label: "面积",
          icon: "gdmj",
          value: "0",
          unit: "亩",
          color: "#4065E0",
        },
        {
          label: "粮食作物面积",
          icon: "hd",
          value: "0",
          unit: "亩",
          color: "#7ECF52",
        },
        {
          label: "经济作物面积",
          icon: "sjd",
          value: "0",
          unit: "亩",
          color: "#EDCC5F",
        },
        {
          label: "蔬菜瓜果面积",
          icon: "nydaqly",
          value: "0",
          unit: "亩",
          color: "#ee6860",
        },
        {
          label: "园地面积",
          icon: "st",
          value: "0",
          unit: "亩",
          color: "#70f0d1",
        },
      ],
      diff: {
        data: [
          {
            label: "平地面积",
            icon: "gdmj",
            value: "0",
            unit: "亩",
            color: "#4065E0",
          },
          {
            label: "缓坡地面积",
            icon: "sjd",
            value: "0",
            unit: "亩",
            color: "#EDCC5F",
          },
          {
            label: "陡坡地面积",
            icon: "hd",
            value: "0",
            unit: "亩",
            color: "#7ECF52",
          },
        ],
        options: {
          tooltip: {
            trigger: "axis",
          },
          grid: {
            left: "4%",
            right: "4%",
            bottom: "3%",
            top: "7%",
            containLabel: true,
          },
          xAxis: {
            type: "category",
            data: ["平地面积", "缓坡地面积", "陡坡地面积"],
            axisTick: {
              show: false,
            },
            axisLine: {
              lineStyle: {
                width: 0.5,
              },
            },
          },
          yAxis: {
            type: "value",
          },
          series: [
            {
              name: "面积",
              type: "bar",
              barMaxWidth: 50,
              data: [1, 2, 3, 4, 5],
            },
          ],
        },
      },
      farm: [
        {
          label: "耕地面积",
          icon: "gdmj",
          value: "0",
          unit: "亩",
          color: "#4065E0",
        },
        {
          label: "旱地面积",
          icon: "hd",
          value: "0",
          unit: "亩",
          color: "#7ECF52",
        },
        {
          label: "水田面积",
          icon: "st",
          value: "0",
          unit: "亩",
          color: "#70f0d1",
        },
      ],
      garden: {
        options: {
          legend: {
            data: ["果园", "茶园", "桑园", "其他园地"],
            orient: "vertical",
            left: 10,
            top: 20,
            show: true,
          },
          tooltip: {
            trigger: "item",
            formatter: "{a} <br/>{b} : {c} ({d}%)",
          },
          textStyle: {
            color: "#fff",
          },
          series: [
            {
              stillShowZeroSum: false,
              name: "园地面积",
              type: "pie",
              radius: ["45%", "70%"],
              center: ["70%", "50%"],
              labelLine: {
                show: false,
              },
              label: {
                show: false,
                position: "center",
              },
              data: [],
            },
          ],
        },
      },
      regionFilter: [],
      typeFilter: [["=", "date", "2022"]],
      region: "city",
    };
  },
  methods: {
    adminChange(admin) {
      let region = "city";
      let filter = [];
      if (admin) {
        region = admin.region;
        filter = ["=", REGION_LIST[admin.level], admin.code];
      }
      this.region = region;
      this.regionFilter = filter;

      this.getData();
    },
    upDataChange(e) {
      console.log("upDataChange", e);
      this.typeFilter = e.filters;
      this.getData();
    },
    async getData() {
      const dkmj = await this.getStatisticData("dlls_dk", [
        ["地块编码", "count"],
        ["种植面积", "sum"],
      ]);
      const dkzw = await this.getStatisticData(`dlls_zw_${this.region}`, [
        ["粮食作物面积", "sum"],
        ["经济作物面积", "sum"],
        ["蔬菜瓜果面积", "sum"],
        ["园地面积", "sum"],
        ["平地面积", "sum"],
        ["缓坡地面积", "sum"],
        ["陡坡地面积", "sum"],
        ["耕地总面积", "sum"],
        ["旱地面积", "sum"],
        ["水田面积", "sum"],
        ["果园面积", "sum"],
        ["茶园面积", "sum"],
        ["桑园面积", "sum"],
        ["其他园地面积", "sum"],
      ]);
      console.log("zw", dkzw, dkmj);
      function floatNum(num, division = true) {
        let result = parseFloat(num).toFixed(2);
        if (division)
          result = result > 10000 ? (result / 10000).toFixed(2) + "万" : result;
        return result;
      }
      this.plant[0].value = dkmj[0].地块编码_count;
      this.plant[1].value = floatNum(dkmj[0].种植面积_sum);
      this.plant[2].value = floatNum(dkzw[0].粮食作物面积_sum);
      this.plant[3].value = floatNum(dkzw[0].经济作物面积_sum);
      this.plant[4].value = floatNum(dkzw[0].蔬菜瓜果面积_sum);
      this.plant[5].value = floatNum(dkzw[0].园地面积_sum);
      this.diff.data[0].value = floatNum(dkzw[0].平地面积_sum);
      this.diff.data[1].value = floatNum(dkzw[0].缓坡地面积_sum);
      this.diff.data[2].value = floatNum(dkzw[0].陡坡地面积_sum);
      this.farm[0].value = floatNum(dkzw[0].耕地总面积_sum);
      this.farm[1].value = floatNum(dkzw[0].旱地面积_sum);
      this.farm[2].value = floatNum(dkzw[0].水田面积_sum);
      // 坡度柱状图赋值
      this.diff.options.series[0].data = [
        floatNum(dkzw[0].平地面积_sum, false),
        floatNum(dkzw[0].缓坡地面积_sum, false),
        floatNum(dkzw[0].陡坡地面积_sum, false),
      ];
      // 园地面积echart赋值
      this.garden.options.series[0].data = [
        {
          value: floatNum(dkzw[0].果园面积_sum, false),
          name: "果园",
          itemStyle: {
            color: "#5087EC",
          },
        },
        {
          value: floatNum(dkzw[0].茶园面积_sum, false),
          name: "茶园",
          itemStyle: {
            color: "#68BBC4",
          },
        },
        {
          value: floatNum(dkzw[0].桑园面积_sum, false),
          name: "桑园",
          itemStyle: {
            color: "#58A55C",
          },
        },
        {
          value: floatNum(dkzw[0].其他园地面积_sum, false),
          name: "其他园地",
          itemStyle: {
            color: "#F2BD42",
          },
        },
      ];
    },
    // 获取统计数据
    async getStatisticData(formName, aggregates, groupFields) {
      const filter = this.handleFilter();
      // 统计地块主体情况
      let params = {
        returnGeometry: false,
        format: "json",
        aggregates,
        groupFields,
      };
      if (filter.length > 0) params.filter = filter;
      let res = await this.$apis.feature.aggregate(formName, params);
      return res;
    },
    handleFilter() {
      let filter = [];
      const region = this.regionFilter;
      const type = this.typeFilter;
      if (region.length > 0) {
        filter = region;
      }
      if (type.length > 0) {
        type.map((item) => {
          item[1] = item[1] == "date" ? "date" : item[1];
        });
        if (filter.length > 0) {
          filter = ["all", filter, ...type];
        } else {
          if (type.length > 1) {
            filter = ["all", ...type];
          } else {
            filter = type[0];
          }
        }
      }
      return filter;
    },
  },
  async mounted() {
    this.bus.$on("RegionCHART_UPDATE", this.upDataChange);
    this.bus.$on(this.$events.admin.CHANGE_ADMIN, this.adminChange);
    this.getData();
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";
.data-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 0 30px;
  .data-item {
    // width: 33%;
    width: 50%;
    margin-bottom: 20px;
    .title {
      // font-size: 16px;
      font-size: 14px;
      // color: #333;
      font-weight: 400;
      line-height: 15px;
      color: #002159;
      margin-bottom: 10px;
    }
    .value {
      // color: #8a8a8a;
      color: #002159;
      font-weight: 600;
      font-size: 20px;
      line-height: 25px;
    }
    .danwe {
      font-size: 12px;
      font-weight: 400;
      color: #002159;
      margin-left: 4px;
      line-height: 13px;
    }
  }
}
.diff-chart {
  width: 100%;
  height: 350px;
}
.garden-chart {
  width: 370px;
  height: 300px;
  margin: 0 auto;
}

.icon-box {
  border-radius: 50%;
  background-color: rgba(64, 101, 224, 0.1);
  float: left;
  height: 50px;
  width: 50px;
  margin: 10px 15px 0px 0px;
  text-align: center;
  line-height: 50px;
  .icon {
    font-size: 30px;
  }
}

.jimu-widget {
  overflow: scroll;
}
</style>
