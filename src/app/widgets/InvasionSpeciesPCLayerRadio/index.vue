<template>
  <div class="jimu-widget">
    <Card
      class="card"
      :title="item.title"
      v-for="item in initConfig"
      :key="item.key"
    >
      <PlainStatistic :value="item.num" unit="个" :color="color" />
    </Card>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
import PlainStatistic from "@/app/Components/PlainStatistic";
import Card from "@/app/Components/Card";
import { mapState } from "vuex";
export default {
  name: "InvasionSpeciesPCLayerRadio",
  mixins: [BaseWidget],
  components: {
    Card,
    PlainStatistic,
  },
  data() {
    return {
      countMap: {},
      color: "#4065E0",
      filterMap: {},
      initConfig: [
        {
          key: "zdNum",
          title: "重点管理物种",
          num: 0,
        },
        {
          key: "unZdNum",
          title: "非重点管理物种",
          num: 0,
        },
      ],
    };
  },
  computed: {
    ...mapState({
      adminData: (state) => state.data?.admin,
    }),
  },
  async mounted() {
    this.bus.$on(this.$events.admin.CHANGE_ADMIN, this.setAdminFilter);
    this.bus.$on("setPCTableFilter", this.setSpeciesFilter);
    await this.init();
  },
  methods: {
    async init() {
      const p = {
        province: {
          table: "species_city",
          labelField: "CXZQMC",
          valueField: "CXZQDM",
          table2: "species_occurred_province",
        },
        city: {
          table: "species_county",
          filterField: "CXZQDM",
          labelField: "FXZQMC",
          valueField: "FXZQDM",
          table2: "species_occurred_city",
        },
        county: {
          table: "species_town",
          filterField: "FXZQDM",
          labelField: "TXZQMC",
          valueField: "TXZQDM",
          table2: "species_occurred_county",
        },
      };
      const f = p[this.adminData ? this.adminData.region : "province"];
      if (!f) return;
      let res = await this.$apis.feature.query(f.table2, {
        format: "json",
        filter: this.adminData
          ? ["=", f.filterField, this.adminData[f.filterField]]
          : null,
      });
      console.log("rwadwa", res);

      this.initConfig.forEach((item) => {
        item.num = 0;
        res.forEach((r) => {
          if (r.wzlx === item.title) {
            item.num++;
          }
        });
      });
    },
  },
  watch: {
    async adminData() {
      await this.init();
    },
  },
};
</script>

<style lang="less" scoped>
.card {
  margin-bottom: 10px;
}
</style>
