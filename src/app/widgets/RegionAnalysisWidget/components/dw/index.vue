<template>
  <div>
    <StatisticsSlot>
      <template>
        <span slot="title" class="jimu-widget-title-ui">土壤点位统计</span>
      </template>
      <template>
        <div slot="contain" class="contain">
          <template v-for="(item, index) in trList">
            <div :key="index">
              <template v-if="item.option">
                <div class="content">
                  <div class="title">{{ item.dwlx }}-{{ item.date }}</div>
                  <div class="left">
                    <a-statistic
                      :value="
                        Number(item.yxbh_num) +
                        Number(item.aqly_num) +
                        Number(item.yggk_num)
                      "
                    >
                      <span slot="title" style="color: rgb(94, 171, 227)"
                        >点位数量</span
                      >
                      <span slot="suffix" class="suffix-unit">个</span>
                    </a-statistic>
                    <a-statistic :value="item.yxbh_num">
                      <span slot="title" style="color: #7ecf52">优先保护</span>
                      <span slot="suffix" class="suffix-unit">个</span>
                    </a-statistic>
                    <a-statistic :value="item.aqly_num">
                      <span slot="title" style="color: #edcc5f">安全利用</span>
                      <span slot="suffix" class="suffix-unit">个</span>
                    </a-statistic>
                    <a-statistic :value="item.yggk_num">
                      <span slot="title" style="color: #ee6860">严格管控</span>
                      <span slot="suffix" class="suffix-unit">个</span>
                    </a-statistic>
                  </div>
                  <div class="right">
                    <Chart class="chart" :options="item.option" />
                  </div>
                </div>
              </template>
            </div>
          </template>
        </div>
      </template>
    </StatisticsSlot>
    <StatisticsSlot>
      <template>
        <span slot="title" class="jimu-widget-title-ui">农产品点位统计</span>
      </template>
      <template>
        <div slot="contain" class="contain">
          <template v-for="(item, index) in ncpList">
            <div :key="index">
              <template v-if="item.option">
                <div class="content">
                  <div class="title">{{ item.dwlx }}-{{ item.date }}</div>
                  <div class="left">
                    <a-statistic
                      :value="Number(item.cb_num) + Number(item.bcb_num)"
                    >
                      <span slot="title" style="color: rgb(94, 171, 227)"
                        >点位数量</span
                      >
                      <span slot="suffix" class="suffix-unit">个</span>
                    </a-statistic>
                    <a-statistic :value="item.bcb_num">
                      <span slot="title" style="color: #7ecf52">未超标</span>
                      <span slot="suffix" class="suffix-unit">个</span>
                    </a-statistic>
                    <a-statistic :value="item.cb_num">
                      <span slot="title" style="color: #edcc5f">超标</span>
                      <span slot="suffix" class="suffix-unit">个</span>
                    </a-statistic>
                  </div>
                  <div class="right">
                    <Chart class="chart" :options="item.option" />
                  </div>
                </div>
              </template>
            </div>
          </template>
        </div>
      </template>
    </StatisticsSlot>
  </div>
</template>

<script>
import StatisticsSlot from "@/app/StatisticsSlot";
import Chart from "@/components/Chart";
import BaseWidget from "@/BaseWidget";
const REGION_LIST = {
  city: "CXZQDM",
  county: "FXZQDM",
  town: "TXZQDM",
  village: "XZQDM",
};
export default {
  name: "RegionAnalysisWidget",

  mixins: [BaseWidget],

  components: {
    StatisticsSlot,
    Chart,
  },

  data() {
    return {
      trList: [
        {
          dwlx: "普查土壤点",
          date: "2020",
          option: null,
          yggk_num: 0,
          aqly_num: 0,
          yxbh_num: 0,
        },
        {
          dwlx: "普查土壤点",
          date: "普查时期",
          option: null,
          yggk_num: 0,
          aqly_num: 0,
          yxbh_num: 0,
        },
        {
          dwlx: "详查土壤点",
          date: "详查时期",
          option: null,
          yggk_num: 0,
          aqly_num: 0,
          yxbh_num: 0,
        },
        {
          dwlx: "监测土壤点",
          date: "监测时期",
          option: null,
          yggk_num: 0,
          aqly_num: 0,
          yxbh_num: 0,
        },
        {
          dwlx: "定位预警监测",
          date: "2021",
          option: null,
          yggk_num: 0,
          aqly_num: 0,
          yxbh_num: 0,
        },
        {
          dwlx: "定位预警监测",
          date: "2022",
          option: null,
          yggk_num: 0,
          aqly_num: 0,
          yxbh_num: 0,
        },
      ],
      ncpList: [
        {
          dwlx: "普查农产品",
          date: "普查时期",
          option: null,
          bcb_num: 0,
          cb_num: 0,
        },
        {
          dwlx: "详查农产品",
          date: "详查时期",
          option: null,
          bcb_num: 0,
          cb_num: 0,
        },
        {
          dwlx: "定位预警监测",
          date: "2017",
          option: null,
          bcb_num: 0,
          cb_num: 0,
        },
        {
          dwlx: "定位预警监测",
          date: "2018",
          option: null,
          bcb_num: 0,
          cb_num: 0,
        },
        {
          dwlx: "定位预警监测",
          date: "2019",
          option: null,
          bcb_num: 0,
          cb_num: 0,
        },
        {
          dwlx: "定位预警监测",
          date: "2020",
          option: null,
          bcb_num: 0,
          cb_num: 0,
        },
        {
          dwlx: "定位预警监测",
          date: "2021",
          option: null,
          bcb_num: 0,
          cb_num: 0,
        },
        {
          dwlx: "定位预警监测",
          date: "2022",
          option: null,
          bcb_num: 0,
          cb_num: 0,
        },
      ],
      date: "2020",
      adminInfo: null,
    };
  },
  methods: {
    async upDataChange(e) {
      console.log("dw time", e);
      this.filter = e[1];
      this.fieldsKey = e[2];
      this.getData();
    },
    adminChange(admin, value = "city") {
      console.log("region选项", admin);
      const init = {
        code: 4500,
        level: 1,
        region: "city",
      };
      if (admin) {
        if (admin.code == 45) {
          this.adminInfo = init;
        } else {
          admin.region = value;
        }
      }
      this.adminInfo = admin;
      console.log("region信息", this.adminInfo);
      this.getData();
    },
    async getDwData(value, params) {
      this.trList.forEach((item) => {
        item.option = null;
      });
      let trData = await this.$apis.feature.query(
        `land_tr_dw_${value}`,
        params
      );
      console.log("土壤点位统计数据", trData);
      trData.forEach((item) => {
        this.trList.forEach((dateItem) => {
          if (dateItem.dwlx == item.dwlx && dateItem.date == item.date) {
            dateItem.yxbh_num = item.zh5_yxbh;
            dateItem.aqly_num = item.zh5_aqly;
            dateItem.yggk_num = item.zh5_yggk;
            dateItem.option = {
              tooltip: {
                trigger: "axis",
              },
              grid: {
                left: "4%",
                right: "4%",
                bottom: "3%",
                top: "7%",
                containLabel: true,
              },
              xAxis: {
                type: "category",
                data: ["镉", "汞", "砷", "铅", "铬"],
                axisTick: {
                  show: false,
                },
                axisLine: {
                  lineStyle: {
                    width: 0.5,
                  },
                },
              },
              yAxis: {
                type: "value",
              },
              series: [
                {
                  name: "优先保护",
                  type: "bar",
                  stack: "总量",
                  barMaxWidth: 15,
                  data: [
                    item.cd_yxbh,
                    item.hg_yxbh,
                    item.as_yxbh,
                    item.pb_yxbh,
                    item.cr_yxbh,
                  ],
                  itemStyle: {
                    color: "#7ECF52",
                  },
                },
                {
                  name: "安全利用",
                  type: "bar",
                  stack: "总量",
                  barMaxWidth: 15,
                  data: [
                    item.cd_aqly,
                    item.hg_aqly,
                    item.as_aqly,
                    item.pb_aqly,
                    item.cr_aqly,
                  ],
                  itemStyle: {
                    color: "#EDCC5F",
                  },
                },

                {
                  name: "严格管控",
                  type: "bar",
                  stack: "总量",
                  data: [
                    item.cd_yggk,
                    item.hg_yggk,
                    item.as_yggk,
                    item.pb_yggk,
                    item.cr_yggk,
                  ],
                  itemStyle: {
                    color: "#EE6860",
                  },
                },
              ],
            };
          }
        });
      });
    },
    async getNcpData(value, params) {
      this.ncpList.forEach((item) => {
        item.option = null;
      });
      let ncpData = await this.$apis.feature.query(
        `land_ncp_dw_${value}`,
        params
      );
      console.log("农产品点位统计数据", ncpData);
      ncpData.forEach((item) => {
        this.ncpList.forEach((dateItem) => {
          if (dateItem.dwlx == item.dwlx && dateItem.date == item.date) {
            dateItem.bcb_num = item.zh5_bcb;
            dateItem.cb_num = item.zh5_cb;
            dateItem.option = {
              tooltip: {
                trigger: "axis",
              },
              grid: {
                left: "4%",
                right: "4%",
                bottom: "3%",
                top: "7%",
                containLabel: true,
              },
              xAxis: {
                type: "category",
                data: ["镉", "汞", "砷", "铅", "铬"],
                axisTick: {
                  show: false,
                },
                axisLine: {
                  lineStyle: {
                    width: 0.5,
                  },
                },
              },
              yAxis: {
                type: "value",
              },
              series: [
                {
                  name: "未超标",
                  type: "bar",
                  stack: "总量",
                  barMaxWidth: 15,
                  data: [
                    item.cd_bcb,
                    item.hg_bcb,
                    item.as_bcb,
                    item.pb_bcb,
                    item.cr_bcb,
                  ],
                  itemStyle: {
                    color: "#7ECF52",
                  },
                },
                {
                  name: "超标",
                  type: "bar",
                  stack: "总量",

                  barMaxWidth: 15,
                  data: [
                    item.cd_cb,
                    item.hg_cb,
                    item.as_cb,
                    item.pb_cb,
                    item.cr_cb,
                  ],
                  itemStyle: {
                    color: "#EDCC5F",
                  },
                },
              ],
            };
          }
        });
      });
    },
    async getData() {
      let params = {};
      const admin = this.adminInfo;
      const value = admin ? admin.region : "city";
      if (admin) {
        params = {
          returnGeometry: false,
          format: "json",
          filter: [
            "all",
            ["=", REGION_LIST[admin.region], admin.code],
            ...this.filter,
          ],
        };
      } else {
        params = {
          returnGeometry: false,
          format: "json",
          filter: ["all", ["=", "CXZQDM", 4500], ...this.filter],
        };
      }
      const filter =
        this.fieldsKey == "land_area" ? params.filter[1] : params.filter;
      params.filter = filter;
      this.getDwData(value, params);
      this.getNcpData(value, params);
    },
  },
  mounted() {
    this.bus.$on(this.$events.special.CHART_UPDATE, this.upDataChange);
    this.bus.$on(this.$events.admin.CHANGE_ADMIN, this.adminChange);
  },
};
</script>

<style lang="less" scoped>
.contain {
  width: 100%;
  .content {
    width: 100%;
    display: flex;
    flex-direction: column;
    .title {
      font-size: 16px;
      margin-bottom: 12px;
      text-align: center;
      color: #002159;
    }
    .left {
      display: flex;
      padding: 0 20px;
      justify-content: space-between;
    }
    .right {
      width: 100%;
      height: 200px;
      margin-top: 10px;
      margin-bottom: 30px;
      padding: 0 30px;
      .chart {
        width: 100%;
        height: 100%;
      }
    }
  }
}
/deep/.ant-statistic-content {
  text-align: center;
}
/deep/.ant-statistic-title {
  margin-bottom: 0;
  text-align: center;
}
/deep/.ant-statistic-content-value-int {
  font-size: 20px;
}
/deep/.suffix-unit {
  font-size: 12px;
}
.type {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #002159;
  line-height: 15px;
}
.unit {
  font-size: 6px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #002159;
  line-height: 13px;
}
.num {
  font-size: 10px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: #002159;
  line-height: 25px;
}
//种植面积
</style>
