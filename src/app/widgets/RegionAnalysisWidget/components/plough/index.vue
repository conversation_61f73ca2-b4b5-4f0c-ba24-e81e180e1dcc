<template>
  <div>
    <StatisticsSlot>
      <template>
        <span slot="title" class="jimu-widget-title-ui">面积和评价单元数量统计</span>
      </template>
      <template>
        <div slot="contain" class="contain">
          <div class="pjdy-nums">
            <div v-for="(item, index) in pjdy.datas" :key="index" class="pjdy-num">
              <div
                :style="{
                  backgroundColor: index % 2 == 0 ? 'rgba(71, 215, 234, 0.1)' : 'rgba(64, 101, 224, 0.1)',
                  color: item.color,
                }"
                class="icon-box"
              >
                <span class="icon gt-icon" :class="`gt-icon-${item.icon}`"></span>
              </div>
              <div style="float: left">
                <span class="type">{{ item.type }}</span>
                <div class="num">
                  {{ format(item.data) }}<span class="unit">{{ unit(item.unit, item.data) }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="types">
            <div class="types-title">
              <span>类型</span>
              <span>耕地面积</span>
              <span v-if="time == '2023' && tableType == true">按照水田评价</span>
              <span v-if="time == '2023' && tableType == true">按照旱地评价</span>
              <span v-else>评价单元</span>
            </div>
            <ul>
              <li v-for="(item, index) in pjdy.levels" :key="index" :style="{ color: item.color, backgroundColor: item.background }">
                <span>{{ item.type }}</span>
                <span>{{ numUnit("land", item.gdmj) }}</span>
                <span v-if="time == '2023' && tableType == true">{{ numUnit("num", item.stpj) }}</span>
                <span v-if="time == '2023' && tableType == true">{{ numUnit("num", item.hdpj) }}</span>
                <span v-else>{{ numUnit("num", item.pjdy) }}</span>
              </li>
            </ul>
          </div>
        </div>
      </template>
    </StatisticsSlot>
    <StatisticsSlot>
      <template>
        <span slot="title" class="jimu-widget-title-ui">耕地面积类型统计</span>
      </template>
      <template>
        <div slot="contain" class="contain">
          <div class="gdmj-datas">
            <div v-for="(item, index) in gdmj.datas" :key="index" class="gdmj-data">
              <div class="box">
                <div style="float: left; text-align: center">
                  <span class="icon gt-icon" :class="`gt-icon-${item.icon}`"></span>
                </div>
                <span class="gdmj-type">{{ item.type }}</span
                ><br />
              </div>

              <span class="num">{{ format(item.all) }}</span>
              <p class="unit">{{ item.all > 10000 ? "万亩" : "亩" }}</p>
            </div>
          </div>
          <div class="echarts1">
            <Echart :options="gdmj.options"></Echart>
          </div>
          <div class="clear"></div>
        </div>
      </template>
    </StatisticsSlot>
    <!-- <StatisticsSlot>
      <template>
        <span slot="title" class="jimu-widget-title-ui">种植作物面积统计</span>
      </template>
      <template>
        <div slot="contain" class="contain zzmj-contain">
          <div style="display: flex">
            <div v-for="(item, index) in zzmj.datas" :key="index" class="zzmj">
              <div class="box">
                <div style="float: left; text-align: center">
                  <span class="icon gt-icon" :class="`gt-icon-${item.icon}`"></span>
                </div>
                <span class="gdmj-type">{{ item.title }}</span>
              </div>
              <span class="num">{{ format(item.all) }}</span>
              <p class="unit">{{ item.all > 10000 ? "万亩" : "亩" }}</p>
            </div>
          </div>
          <div class="echarts2">
            <Echart :options="zzmj.options"></Echart>
          </div>
        </div>
      </template>
    </StatisticsSlot> -->
  </div>
</template>

<script>
import StatisticsSlot from "@/app/StatisticsSlot";
import Echart from "@/components/Chart";
import BaseWidget from "@/BaseWidget";
export default {
  name: "RegionAnalysisWidget",

  mixins: [BaseWidget],

  components: {
    StatisticsSlot,
    Echart,
  },

  data() {
    return {
      filter: [],
      //面积和评价单元数量统计
      pjdy: {
        datas: [
          {
            type: "耕地面积",
            data: 0,
            unit: "land",
            icon: "gdmj",
            color: "#47D7EA",
          },
          {
            type: "地块数量",
            data: 0,
            unit: "num",
            icon: "dksl",
            color: "#4065E0",
          },
          {
            type: "评价单元数量",
            data: 0,
            unit: "num",
            icon: "pjdysl",
            color: "#47D7EA",
          },
        ],
        levels: [
          {
            type: "优先保护",
            color: "#7ECF52",
            gdmj: 0,
            pjdy: 0,

            stpj: 0,
            hdpj: 0,

            background: "rgba(126, 207, 82, 0.05)",
          },
          {
            type: "安全利用",
            color: "#EDCC5F",
            gdmj: 0,
            pjdy: 0,

            stpj: 0,
            hdpj: 0,

            background: "rgba(237, 204, 95, 0.1)",
          },
          {
            type: "严格管控",
            color: "#ee6860",
            gdmj: 0,
            pjdy: 0,

            stpj: 0,
            hdpj: 0,

            background: "rgba(238, 104, 96, 0.1)",
          },
        ],
        nums: [
          {
            type: "yxbh",
            gdmj: 0,
            pjdy: 0,
          },
          {
            type: "aqly",
            gdmj: 0,
            pjdy: 0,
          },
          {
            type: "yggk",
            gdmj: 0,
            pjdy: 0,
          },
        ],
      },
      //耕地面积类型统计
      gdmj: {
        datas: [
          {
            type: "水田",
            class: "st_",
            yxbh: NaN,
            aqly: NaN,
            yggk: NaN,
            all: NaN,
            unit: "land",
            icon: "st",
          },
          {
            type: "旱地",
            class: "hd_",
            yxbh: NaN,
            aqly: NaN,
            yggk: NaN,
            all: NaN,
            unit: "land",
            icon: "hd",
          },
          {
            type: "水浇地",
            class: "sjd_",
            yxbh: NaN,
            aqly: NaN,
            yggk: NaN,
            all: NaN,
            unit: "land",
            icon: "sjd",
          },
        ],
        options: {
          tooltip: {
            trigger: "item",
            confine: true,
          },
          xAxis: {
            type: "category",
            position: 12,
            data: ["水田", "旱地", "水浇地"],
            axisLabel: {
              interval: 0,
              textStyle: {
                color: "#002159",
                fontSize: 12,
                padding: 18,
                fontFamily: "PingFangSC-Semibold, PingFang SC",
              },
            },
            axisTick: {
              show: false, // 不显示坐标轴刻度线
            },
            axisLine: {
              show: false, //不显示坐标轴线
            },
            nameGap: 30,
          },
          yAxis: {
            type: "value",
          },
          grid: {
            x: 60,
          },
          series: [
            {
              type: "bar",
              stack: "Total",
              barWidth: 15,
              color: "#7ECF52",
              data: [],
            },
            {
              type: "bar",
              stack: "Total",
              barWidth: 15,
              color: "#EDCC5F",
              data: [],
            },
            {
              type: "bar",
              stack: "Total",
              barWidth: 15,
              color: "#EE6860",
              data: [],
            },
          ],
        },
      },
      //种植作物面积统计
      zzmj: {
        datas: [
          {
            title: "水稻",
            class: "sd_",
            yxbh: 0,
            aqly: 0,
            yggk: 0,
            all: 0,
            unit: "land",
            icon: "st",
          },
          {
            title: "小麦",
            class: "ac_",
            yxbh: 0,
            aqly: 0,
            yggk: 0,
            all: 0,
            unit: "land",
            icon: "hd",
          },
          {
            title: "其他作物",
            class: "qtzw_",
            yxbh: 0,
            aqly: 0,
            yggk: 0,
            all: 0,
            unit: "land",
            icon: "qt",
          },
        ],
        options: {
          legend: {
            data: ["优先保护", "安全利用", "严格管控"],
            show: true,
          },
          textStyle: {
            color: "#fff",
          },
          series: [
            {
              stillShowZeroSum: false,
              name: "水稻",
              type: "pie",
              radius: ["35%", "50%"],
              center: ["15%", "50%"],
              labelLine: {
                // show: false
              },
              label: {
                show: false,
                position: "center",
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: "12",
                  fontWeight: "400",
                  fontFamily: "PingFangSC-Semibold, PingFang SC",
                  formatter: (data) => {
                    return `${data.name}\n${data.percent}%`;
                  },
                },
              },
              data: [
                {
                  value: 10000,
                  name: "优先保护",
                  itemStyle: {
                    color: "#7ECF52",
                  },
                },
                {
                  value: 30000,
                  name: "安全利用",
                  itemStyle: {
                    color: "#EDCC5F",
                  },
                },
                {
                  value: 30000,
                  name: "严格管控",
                  itemStyle: {
                    color: "#EE6860",
                  },
                },
              ],
            },
            {
              stillShowZeroSum: false,
              name: "小麦",
              type: "pie",
              radius: ["35%", "50%"],
              center: ["50%", "50%"],
              labelLine: {
                show: false,
              },
              label: {
                show: false,
                position: "center",
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: "12",
                  fontWeight: "bold",
                  fontFamily: "PingFangSC-Semibold, PingFang SC",
                  formatter: (data) => {
                    return `${data.name}\n${data.percent}%`;
                  },
                },
              },
              data: [
                {
                  value: 10000,
                  name: "优先保护",
                  itemStyle: {
                    color: "#7ECF52",
                  },
                },
                {
                  value: 30000,
                  name: "安全利用",
                  itemStyle: {
                    color: "#EDCC5F",
                  },
                },
                {
                  value: 30000,
                  name: "严格管控",
                  itemStyle: {
                    color: "#EE6860",
                  },
                },
              ],
            },
            {
              stillShowZeroSum: false,
              name: "其他",
              type: "pie",
              radius: ["35%", "50%"],
              center: ["85%", "50%"],
              labelLine: {
                show: false,
              },
              label: {
                show: false,
                position: "center",
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: "12",
                  fontWeight: "bold",
                  fontFamily: "PingFangSC-Semibold, PingFang SC",
                  formatter: (data) => {
                    return `${data.name}\n${data.percent}%`;
                  },
                },
              },
              data: [
                {
                  value: 10000,
                  name: "优先保护",
                  itemStyle: {
                    color: "#7ECF52",
                  },
                },
                {
                  value: 30000,
                  name: "安全利用",
                  itemStyle: {
                    color: "#EDCC5F",
                  },
                },
                {
                  value: 30000,
                  name: "严格管控",
                  itemStyle: {
                    color: "#EE6860",
                  },
                },
              ],
            },
          ],
        },
      },
      //区域级别
      district: ["province", "city", "county", "town", "village"],
      //编码
      dqbm: ["PXZQDM", "CXZQDM", "FXZQDM", "TXZQDM", "XZQDM"],
      pjdmbm: ["PXZQDM", "CXZQDM", "FXZQDM", "TXZQDM", "XZQDM"],

      time: "2020",

      admin: null,

      tableType: false,
    };
  },
  methods: {
    //耕地面积类型统计和种植作物面积统计
    async gdmjGetData(admin, value = "city") {
      let params = {};
      let queryData = {};
      let filter = this.filter.filter((item) => item[1] != "dwlx");
      if (admin) {
        if (admin.code == 45) {
          admin.code = 4500;
          admin.level = 1;
        }
        this.admin = admin;
        params = {
          returnGeometry: false,
          format: "json",
          filter: ["all", ["=", this.dqbm[admin.level], admin.code], ...filter],
        };
        value = this.district[admin.level];
      } else {
        params = {
          returnGeometry: false,
          format: "json",
          filter: ["all", ["=", "CXZQDM", "4500"], ...filter],
        };
      }
      try {
        let gdmjData = await this.$apis.feature.query(`land_plough_${value}`, params);
        queryData = gdmjData ? gdmjData[0] : null;
      } catch (error) {
        console.log(error);
      }

      this.gdmjSetQueryData(queryData);
    },
    gdmjSetQueryData(queryData) {
      console.log("queryData574", queryData);
      if (!queryData) return;
      this.pjdy.datas[0].data = queryData["land_area"];
      this.pjdy.datas[1].data = queryData["land_num"];

      this.pjdy.levels[0].gdmj = queryData["land_yxbh"];
      this.pjdy.levels[1].gdmj = queryData["land_aqly"];
      this.pjdy.levels[2].gdmj = queryData["land_yggk"];

      this.gdmj.datas.forEach((item) => {
        item.yxbh = queryData[item.class + "yxbh"];
        item.aqly = queryData[item.class + "aqly"];
        item.yggk = queryData[item.class + "yggk"];
        item.all = +item.yxbh + +item.aqly + +item.yggk;
      });

      this.gdmj.options.series[0].data = [
        (this.gdmj.datas[0].yxbh / 10000).toFixed(2),
        (this.gdmj.datas[1].yxbh / 10000).toFixed(2),
        (this.gdmj.datas[2].yxbh / 10000).toFixed(2),
      ];
      this.gdmj.options.series[1].data = [
        (this.gdmj.datas[0].aqly / 10000).toFixed(2),
        (this.gdmj.datas[1].aqly / 10000).toFixed(2),
        (this.gdmj.datas[2].aqly / 10000).toFixed(2),
      ];
      this.gdmj.options.series[2].data = [
        (this.gdmj.datas[0].yggk / 10000).toFixed(2),
        (this.gdmj.datas[1].yggk / 10000).toFixed(2),
        (this.gdmj.datas[2].yggk / 10000).toFixed(2),
      ];

      //种植面积
      this.zzmj.datas.forEach((item) => {
        item.yxbh = queryData[item.class + "yxbh"];
        item.aqly = queryData[item.class + "aqly"];
        item.yggk = queryData[item.class + "yggk"];

        item.all = +item.yxbh + +item.aqly + +item.yggk;
      });

      this.zzmj.options.series.forEach((item, index) => {
        item.data[0].value = this.zzmj.datas[index].yxbh;
        item.data[1].value = this.zzmj.datas[index].aqly;
        item.data[2].value = this.zzmj.datas[index].yggk;
      });
    },

    //评价单元数据
    async pjdyGetData(admin, value = "province") {
      let params = {};
      let queryData = {};
      let filter = this.filter.filter((item) => item[1] != "dwlx");

      if (admin) {
        if (admin.code == 45) {
          admin.code = 45;
          admin.level = 1;
        }
        this.admin = admin;
        params = {
          returnGeometry: false,
          format: "json",
          filter: ["all", ["=", this.pjdmbm[admin.level], admin.code], ...filter],
        };

        value = this.district[admin.level];
      } else {
        params = {
          returnGeometry: false,
          format: "json",
          filter: ["all", ["=", "PXZQDM", 45], ...filter],
        };
      }
      let gdmjData = await this.$apis.feature.query(`land_pjdy_${value}_gx`, params);

      queryData = gdmjData ? gdmjData[0] : null;

      this.pjdySetQueryData(queryData);
    },
    pjdySetQueryData(queryData) {
      console.log("queryData661", queryData);
      if (!queryData) return;
      if (this.time == "2023" && this.tableType == true) {
        // queryData["pjdy_num"] 为字符串，如何转成数字

        this.pjdy.datas[2].data = +queryData["pjdy_num"];

        this.pjdy.levels[0].stpj = +queryData["pjdy_yggk_num_st"];
        this.pjdy.levels[0].hdpj = +queryData["pjdy_yggk_num_hd"];

        this.pjdy.levels[1].stpj = +queryData["pjdy_aqly_num_st"];
        this.pjdy.levels[1].hdpj = +queryData["pjdy_aqly_num_hd"];

        this.pjdy.levels[2].stpj = +queryData["pjdy_yxbh_num_st"];
        this.pjdy.levels[2].hdpj = +queryData["pjdy_yxbh_num_hd"];

        console.log("this.pjdy.levels", this.pjdy.levels);
      } else {
        this.pjdy.datas[2].data = +queryData["pjdy_num"];

        this.pjdy.levels[0].pjdy = +queryData["pjdy_yxbh_num"];
        this.pjdy.levels[1].pjdy = +queryData["pjdy_aqly_num"];
        this.pjdy.levels[2].pjdy = +queryData["pjdy_yggk_num"];
        console.log("this.pjdy.levels", this.pjdy.levels);
      }
    },
    format(data) {
      data = parseInt(data);
      if (data > 10000) {
        return (data / 10000).toFixed(2);
      } else {
        return data;
      }
    },
    unit(unit, data) {
      data = parseInt(data);
      if (unit == "land" && data > 10000) {
        return "万亩";
      } else if (unit == "land" && data < 10000) {
        return "亩";
      } else if (unit == "num" && data > 10000) {
        return "万个";
      } else if (unit == "num" && data < 10000) {
        return "个";
      }
    },
    numUnit(unit, data) {
      data = parseInt(data);
      if (unit == "land" && data > 10000) {
        return (data / 10000).toFixed(2) + "万亩";
      } else if (unit == "land" && data < 10000) {
        return data.toFixed(2) + "亩";
      } else if (unit == "num" && data > 10000) {
        return (data / 10000).toFixed(2) + "万个";
      } else {
        return data + "个";
      }
    },
    async upDataChange(e) {
      this.filter = e[1];
      e[1].forEach((item) => {
        if (item[1] === "date") {
          this.time = item[2];
        } else {
          this.time = "";
        }
      });
      this.trys = ["类型", "地块面积", "评价单元"];
      this.pjdy.levels = [
        {
          type: "优先保护",
          color: "#7ECF52",
          gdmj: 0,
          pjdy: 0,
          background: "rgba(126, 207, 82, 0.05)",
        },
        {
          type: "安全利用",
          color: "#EDCC5F",
          gdmj: 0,
          pjdy: 0,
          background: "rgba(237, 204, 95, 0.1)",
        },
        {
          type: "严格管控",
          color: "#ee6860",
          gdmj: 0,
          pjdy: 0,
          background: "rgba(238, 104, 96, 0.1)",
        },
      ];
      this.tableType = e[0] === "area_ratio" || e[0] === "area" ? true : false;
      await this.pjdyGetData(this.admin, "province");
      await this.gdmjGetData(this.admin, "city");
    },
  },
  mounted() {
    this.bus.$on(this.$events.special.CHART_UPDATE, this.upDataChange);

    this.bus.$on(this.$events.admin.CHANGE_ADMIN, this.gdmjGetData);
    this.bus.$on(this.$events.admin.CHANGE_ADMIN, this.pjdyGetData);
    this.gdmjGetData();
    this.pjdyGetData();
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";

.title-icon {
  transform: translateX(-10px) translateY(-5px);
}
// 耕地面积类型统计
div {
  .pjdy-nums {
    padding: 0px 12px;
    overflow: hidden;
    .pjdy-num {
      float: left;
      width: 48%;
      border: 1px solid white;
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      .num {
        margin-top: 4px;
      }
    }
  }
  .types {
    width: 100%;
    padding: 0px 12px;
    margin-bottom: 30px;
    .types-title {
      display: flex;
      justify-content: space-between;
      height: 30px;
      background: #e7ebf4;
      span {
        display: inline-block;
        width: 33%;
        height: 30px;
        font-size: 14px;
        font-weight: 600;
        color: #002159;
        line-height: 30px;
        text-align: center;
      }
    }
    ul {
      width: 100%;
      list-style: none;
      padding-left: 0px;
      li {
        width: 100%;
        display: flex;
        justify-content: space-between;
        margin: 3px auto;
        line-height: 30px;
        span {
          width: 33%;
          text-align: center;
          font-size: 14px;
          font-weight: 600;
        }
      }
    }
  }
  .pjdy-level {
    float: left;
    padding-top: 10px;
    display: inline-block;
    text-align: center;
    width: 30%;
    .levels {
      width: 100%;
      font-size: 20px;
      font-weight: 600;
      .level {
        height: 50px;
      }
    }
  }
  .pjdy-datas {
    width: 70%;
    margin-left: 30%;
    height: 100px;
    display: flex;
    justify-content: space-between;
    .pjdy-data {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      width: 100%;
      margin-top: 50px;
      color: #002159;
      span {
        line-height: 50px;
        text-align: center;
      }
    }
  }
}
.gdmj-datas {
  display: flex;
  justify-content: space-between;
  padding: 0px 12px;
  width: 100%;
  .gdmj-data {
    width: 100%;
    padding: 5px 5px;
    text-align: center;
  }
}
.echarts1 {
  float: right;
  height: 300px;
  width: 100%;
  padding: 0px 12px;
}
.clear {
  clear: both;
}
//种植面积
.zzmj-contain {
  display: flex;
  flex-direction: column;
  .zzmj {
    width: 32%;
    text-align: center;
  }
  .echarts2 {
    width: 100%;
    height: 200px;
    padding: 0px 12px;
  }
}
.type {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: @primaryTextColor;
  line-height: 15px;
}
.unit {
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: @primaryTextColor;
  line-height: 13px;
  margin-left: 4px;
}
.num {
  font-size: 20px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: @primaryTextColor;
  line-height: 25px;
  margin-top: 10px;
}
.icon-box {
  border-radius: 50%;
  background-color: rgba(64, 101, 224, 0.1);
  float: left;
  height: 50px;
  width: 50px;
  margin: 10px 15px 0px 0px;
  text-align: center;
  line-height: 50px;
  .icon {
    font-size: 30px;
  }
}

.gdmj-type {
  float: left;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: @primaryTextColor;
  line-height: 15px;
}
.gdmj-svg {
  height: 20px;
  width: 20px;
  margin: 0px 5px;
}
.box {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px;
  .icon {
    font-size: 24px;
    color: #70f0d1;
    margin-right: 5px;
  }
}
</style>
