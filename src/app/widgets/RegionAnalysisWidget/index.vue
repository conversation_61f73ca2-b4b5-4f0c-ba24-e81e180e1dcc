<template>
  <div class="jimu-widget jimu-tool-widget charts">
    <div class="tabs-content">
      <div
        :class="['tabs', index == tabIndex ? 'active' : '']"
        v-for="(item, index) in tabs"
        :style="{ width: `${100 / tabs.length}%` }"
        :key="index"
        @click="tabIndex = index"
      >
        {{ item }}
      </div>
    </div>
    <!-- <template>
      <Aqly v-show="tabIndex == 0" :file="config.file" />
    </template>
    <template>
      <Plough v-show="tabIndex == 1" />
      <Dw v-show="tabIndex == 1" />
    </template> -->

    <template>
      <Plough />
      <Dw />
    </template>
  </div>
</template>

<script>
import Plough from "./components/plough";
import Dw from "./components/dw";
// import Aqly from "./components/aqly";
// import StatisticsSlot from '@/app/StatisticsSlot'
// import Echart from "@/components/Chart"
import BaseWidget from "@/BaseWidget";
export default {
  name: "RegionAnalysisWidget",

  mixins: [BaseWidget],
  data() {
    return {
      tabIndex: 0,
      // tabs: ["农用地安全利用率", "数据统计"],
      tabs: ["数据统计"],
    };
  },
  components: {
    Plough,
    Dw,
    // Aqly,
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";
.tabs-content {
  display: flex;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #e7ebf4;
  overflow: hidden;
  margin-bottom: 10px;
}
.tabs {
  width: 50%;
  height: 40px;
  text-align: center;
  // border: 1px solid #ccc;
  line-height: 40px;
  cursor: pointer;
}
.content {
  height: calc(100% - 65px);
  display: flex;
  flex-direction: column;
}
.active {
  border-bottom: none;
  // color: rgb(62, 145, 247);
  color: #fff;
  background-color: @primaryColor;
}

.charts {
  overflow-y: scroll;
}
// 耕地面积类型统计
div {
  .pjdy-nums {
    display: flex;
    justify-content: space-around;
    border-bottom: 1px solid gray;
    .pjdy-num {
      .num {
        text-align: center;
        margin: 15px 0px;
      }
    }
  }
  .types {
    width: 70%;
    margin-left: 30%;
    display: flex;
    justify-content: space-around;
    height: 50px;
    line-height: 50px;
  }
  .pjdy-level {
    float: left;
    padding-top: 10px;
    display: inline-block;
    text-align: center;
    width: 30%;
    .levels {
      width: 100%;
      font-size: 20px;
      font-weight: 600;
      .level {
        height: 50px;
      }
    }
  }
  .pjdy-datas {
    width: 70%;
    margin-left: 30%;
    height: 100px;
    display: flex;
    justify-content: space-between;
    .pjdy-data {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      width: 100%;
      margin-top: 50px;
      span {
        line-height: 50px;
        text-align: center;
      }
    }
  }
}
.gdmj-datas {
  float: left;
  width: 30%;
  .gdmj-data {
    width: 100%;
    padding: 5px 5px;
  }
}
.echarts1 {
  float: right;
  height: 300px;
  width: 70%;
}
.clear {
  clear: both;
}
//种植面积
.zzmj-contain {
  display: flex;
  justify-content: space-between;
  .zzmj {
    width: 32%;
    height: 200px;
    text-align: center;
  }
  .echarts2 {
    width: 100%;
    height: 100px;
  }
}
</style>
