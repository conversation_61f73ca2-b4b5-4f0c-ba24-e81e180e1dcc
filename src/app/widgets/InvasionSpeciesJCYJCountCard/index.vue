<template>
  <div class="jimu-widget">
    <Card
      class="card"
      :title="item.title"
      v-for="item in config"
      :key="item.title"
    >
      <PlainStatistic :value="countMap[item.title]" unit="个" :color="color" />
    </Card>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
import PlainStatistic from "@/app/Components/PlainStatistic";
import Card from "@/app/Components/Card";
import { mapState } from "vuex";
export default {
  name: "InvasionSpeciesPCLayerRadio",
  mixins: [BaseWidget],
  components: {
    Card,
    PlainStatistic,
  },
  data() {
    return {
      countMap: {},
      color: "#4065E0",
      filterMap: {},
    };
  },
  computed: {
    ...mapState({
      adminData: (state) => state.data?.admin,
    }),
  },
  configLoaded() {
    this.init();
    this.bus.$on(this.$events.admin.CHANGE_ADMIN, this.setAdminFilter);
    this.bus.$on("setJCYJTaskFilter", this.setJCYJTaskFilter);
  },
  methods: {
    async init() {
      this.config.forEach(async (conf) => {
        let filters = Object.keys(this.filterMap)
          .filter((k) => {
            return this.filterMap[k] && conf.filtersKey.indexOf(k) !== -1;
          })
          .map((k) => {
            return this.filterMap[k];
          });
        console.log(filters);
        let filter = null;
        if (filters.length === 1) {
          filter = filters[0];
        } else if (filters.length > 1) {
          filter = ["all", ...filters];
        }
        const count = await this.$apis.feature.count(
          conf.table,
          conf.primaryKey,
          { filter },
          conf.distinct
        );
        this.$set(this.countMap, conf.title, count);
      });
    },

    setJCYJTaskFilter(e) {
      if (e) {
        const filter = ["=", "任务名称", e];
        this.$set(this.filterMap, "task", filter);
      } else {
        this.$set(this.filterMap, "task", null);
      }

      this.init();
    },
    setAdminFilter(e) {
      const map = {
        city: "CXZQDM",
        county: "FXZQDM",
        town: "TXZQDM",
      };
      if (e) {
        const filter = ["=", map[e.region], e[map[e.region]]];
        this.$set(this.filterMap, "admin", filter);
      } else {
        this.$set(this.filterMap, "admin", null);
      }

      this.init();
    },
  },
};
</script>

<style lang="less" scoped>
.card {
  margin-bottom: 10px;
}
</style>
