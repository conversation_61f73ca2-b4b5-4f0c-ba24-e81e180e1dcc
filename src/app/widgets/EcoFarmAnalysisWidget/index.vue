<template>
  <div class="jimu-widget jimu-tool-widget">
    <TitleBar
      :title="`生态农场分布情况`"
      style="margin-bottom: 15px"
    ></TitleBar>
    <div class="chart-wrap">
      <Chart ref="chart" :options="option" />
    </div>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
import TitleBar from "../../Components/TitleBar";
import Chart from "@/components/Chart";
export default {
  name: "EcoFarmAnalysisWidget",

  mixins: [BaseWidget],

  components: {
    TitleBar,
    Chart,
  },

  data() {
    return {
      region: "city",
      adminFilter: null,
      regionParam: {
        city: "CXZQDM",
        county: "FXZQDM",
        town: "TXZQDM",
        village: "XZQDM",
      },

      filters: [],
      gjData: [],
      zzqData: [],
      mergedData: [],
    };
  },

  computed: {
    option() {
      const sortedData = [...this.mergedData].sort((a, b) => {
        return a.gjj_num + a.zzqj_num - (b.gjj_num + b.zzqj_num);
      });

      return {
        legend: {
          show: true,
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        grid: {
          left: "1%",
          right: "12%",
          bottom: "3%",
          top: "5%",
          containLabel: true,
        },
        xAxis: {
          name: "个",
          type: "value",
          position: "top",
          axisLabel: {
            formatter: "{value}",
          },
        },
        yAxis: {
          type: "category",
          data: sortedData.map((item) =>
            this.region === "city" ? item.city : item.county
          ),
          axisLabel: {
            show: true,
            textStyle: {
              fontSize: 15,
            },
            // formatter: function (params) {
            //   return params.slice(0, 3) + "...";
            // },
          },
        },
        series: [
          {
            name: "国家级生态农场",
            type: "bar",
            stack: "total",
            data: sortedData.map((item) => item.gjj_num),
            color: "#5a88e5",
            barWidth: 15,
          },
          {
            name: "自治区级生态农场",
            type: "bar",
            stack: "total",
            data: sortedData.map((item) => item.zzqj_num),
            color: "#7bb9e2",
            barWidth: 15,
          },
        ],
      };
    },
  },

  mounted() {
    this.bus.$on(
      this.$events.admin.CHANGE_ADMIN,
      this.adminFilterUpdateHandler
    );
    this.bus.$on("setAnalysisFilterCount", this.setFilter);
    this.loadAndProcessData();
  },
  methods: {
    setFilter(e) {
      console.log("setFilter", e);
      this.filters = e;
      if (e) {
        this.loadAndProcessData();
      }
    },
    adminFilterUpdateHandler(e) {
      if (e) {
        this.adminFilter = ["=", this.regionParam[e.region], e.code];

        this.region = "county";
      } else {
        this.adminFilter = null;
        this.region = "city";
      }
      this.loadAndProcessData();
    },

    async loadAndProcessData() {
      await Promise.all([this.getGJData(), this.getZZQData()]);
      this.changeData();
    },

    async fetchData(level) {
      let filter = ["all", ["=", "level", level], ...this.filters];

      this.filter = filter;

      // if (this.date !== "全部" && this.date !== null) {
      //   filter.push(["=", "date", this.date]);
      // }
      if (this.adminFilter !== null) {
        filter.push(this.adminFilter);
      }

      if (filter.length == 2) {
        filter = filter[1];
      }

      const params = {
        returnGeometry: false,
        format: "json",
        filter,
        groupFields: ["CXZQMC", "FXZQMC"],
        aggregates: [["ncbm", "count", null, "distinct"]],
      };

      const res = await this.$apis.feature.aggregate("farm_gx", params);

      return res.map((item) => ({
        city: item.CXZQMC,
        county: item.FXZQMC,
        count: item.ncbm_count,
      }));
    },

    async getGJData() {
      this.gjData = await this.fetchData("国家级");
    },

    async getZZQData() {
      this.zzqData = await this.fetchData("自治区级");
    },

    changeData() {
      const dataMap = {};
      if (this.region === "city") {
        // Aggregate all county-level data into city-level data
        this.gjData.forEach((gjItem) => {
          if (!dataMap[gjItem.city]) {
            dataMap[gjItem.city] = {
              city: gjItem.city,
              gjj_num: 0, // Initialize gjj_num to 0
              zzqj_num: 0, // Initialize zzqj_num to 0
            };
          }
          dataMap[gjItem.city].gjj_num += gjItem.count;
        });

        this.zzqData.forEach((zzqItem) => {
          if (!dataMap[zzqItem.city]) {
            dataMap[zzqItem.city] = {
              city: zzqItem.city,
              gjj_num: 0, // Initialize gjj_num to 0
              zzqj_num: 0, // Initialize zzqj_num to 0
            };
          }
          dataMap[zzqItem.city].zzqj_num += zzqItem.count;
        });

        // 添加上北海市的数据
        console.log("this.filters", this.filters);
        console.log("this.filter", this.filter);

        const isshow = !this.filter.some((item) => {
          return (
            Array.isArray(item) &&
            (item[1] === "date" ||
              item[1] === "nclx" ||
              item[1] === "jyms" ||
              item[1] === "zzms")
          );
        });
        console.log("isshow", isshow);

        if (!dataMap["北海市"] && isshow) {
          dataMap["北海市"] = {
            city: "北海市",
            gjj_num: 0,
            zzqj_num: 0,
          };
        }
      } else if (this.region === "county") {
        // Handle data at the county level (default behavior)
        const getKey = (city, county) => `${city}_${county}`;

        this.gjData.forEach((gjItem) => {
          const key = getKey(gjItem.city, gjItem.county);
          dataMap[key] = {
            city: gjItem.city,
            county: gjItem.county,
            gjj_num: gjItem.count,
            zzqj_num: 0,
          };
        });

        this.zzqData.forEach((zzqItem) => {
          const key = getKey(zzqItem.city, zzqItem.county);
          if (dataMap[key]) {
            dataMap[key].zzqj_num = zzqItem.count;
          } else {
            dataMap[key] = {
              city: zzqItem.city,
              county: zzqItem.county,
              gjj_num: 0,
              zzqj_num: zzqItem.count,
            };
          }
        });
      }

      this.mergedData = Object.values(dataMap);

      console.log("mergedData", this.mergedData);
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";

.chart-wrap {
  width: 95%;
  height: 90%;
}
</style>
