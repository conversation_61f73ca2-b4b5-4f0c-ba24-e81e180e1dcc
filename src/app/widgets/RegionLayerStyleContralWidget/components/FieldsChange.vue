<template>
  <div class="wrap">
    <a-select v-model="fields">
      <template v-for="(item, index) in typeList">
        <a-select-option :value="item.value" :key="index">
          {{ item.label }}
        </a-select-option>
      </template>
    </a-select>
  </div>
</template>

<script>
export default {
  props: {
    fieldsKey: {
      type: String
    }
  },
  data() {
    return {
      fields: 'land_area',
      list: [
        { label: '总计', value: 'land_area', type: 'area' },
        { label: '优先保护', value: 'land_yxbh', type: 'area' },
        { label: '安全利用', value: 'land_aqly', type: 'area' },
        { label: '严格管控', value: 'land_yggk', type: 'area' },
        { label: '优先保护', value: 'land_yxbh_ratio', type: 'area_ratio' },
        { label: '安全利用', value: 'land_aqly_ratio', type: 'area_ratio' },
        { label: '严格管控', value: 'land_yggk_ratio', type: 'area_ratio' },
        { label: '镉', value: 'cd_cbl', type: 'cbl' },
        { label: '汞', value: 'hg_cbl', type: 'cbl' },
        { label: '砷', value: 'as_cbl', type: 'cbl' },
        { label: '铅', value: 'pb_cbl', type: 'cbl' },
        { label: '铬', value: 'cr_cbl', type: 'cbl' },
        { label: '镉', value: 'cd_cbl', type: 'ncp_cbl' },
        { label: '汞', value: 'hg_cbl', type: 'ncp_cbl' },
        { label: '砷', value: 'as_cbl', type: 'ncp_cbl' },
        { label: '铅', value: 'pb_cbl', type: 'ncp_cbl' },
        { label: '铬', value: 'cr_cbl', type: 'ncp_cbl' }
      ]
    }
  },
  computed: {
    typeList() {
      return this.list.filter(item => {
        return item.type == this.fieldsKey
      })
    }
  },

  watch: {
    typeList(e) {
      this.fields = e[0].value
    },
    fields(e) {
      this.$emit('fieldsChangeEvent', e)
    }
  }
}
</script>

<style lang="less" scoped>
.wrap {
  background: #ffffff;
  border-radius: 4px;
  padding: 3px;
  box-shadow: 2px 2px 3px rgb(0 0 0 / 20%);
}
/deep/.ant-select-selection--single {
  border: none;
}
/deep/.ant-select{
  min-width: 120px;
}
  /deep/.ant-select-selection{
    height: 39px;
  }
  /deep/.ant-select-selection__rendered{
    line-height: 39px;
  }
</style>