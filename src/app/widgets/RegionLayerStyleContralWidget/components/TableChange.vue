<template>
  <div class="wrap">
    <a-select v-model="tableType" @change="changeHandler">
      <template v-for="(item, index) in list">
        <a-select-option :value="item.value" :key="index">
          {{ item.label }}
        </a-select-option>
      </template>
    </a-select>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tableType: 'area',
      list: [
        { label: '面积统计', value: 'area' },
        { label: '点位超标率', value: 'cbl' }
      ]
    }
  },
  methods: {
    changeHandler(e) {
      this.$emit('tableChangeEvent', e)
    }
  }
}
</script>

<style lang="less" scoped>
.wrap {
  background: #ffffff;
  border-radius: 4px;
  padding: 3px;
  box-shadow: 2px 2px 3px rgb(0 0 0 / 20%);
}
/deep/.ant-select-selection--single {
  border: none;
}
/deep/.ant-select{
  min-width: 120px;
}
/deep/.ant-select-selection{
    height: 38px;
    /deep/.ant-select-selection-selected-value{
      line-height: 40px;
    }
  }
  /deep/.ant-select-selection__rendered{
    line-height: 40px;
  }
</style>