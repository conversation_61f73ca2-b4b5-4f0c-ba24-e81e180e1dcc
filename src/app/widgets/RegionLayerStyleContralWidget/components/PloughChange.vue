<template>
  <div class="wrap">
    <a-cascader
      :allowClear="false"
      :options="options"
      :value="value"
      @change="onChange"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      value: null,
      options: [
        {
          value: "area",
          label: "耕地面积",
          children: [
            {
              value: "2020",
              label: "2020类别划分",
            },
            {
              value: "2023",
              label: "2023年动态调整",
            },
          ],
        },
        {
          value: "area_ratio",
          label: "耕地面积比例",
          children: [
            {
              value: "2020",
              label: "2020类别划分",
            },
            {
              value: "2023",
              label: "2023年动态调整",
            },
          ],
        },
      ],
    };
  },

  mounted() {
    setTimeout(() => {
      this.value = ["area", "2020"];
    }, 1000);
  },
  methods: {
    onChange(e) {
      this.value = e;
    },
  },
  watch: {
    value(e) {
      this.$emit("ploughChangeEvent", e);
    },
  },
};
</script>

<style lang="less" scoped>
.wrap {
  background: #ffffff;
  border-radius: 4px;
  padding: 3px;
  box-shadow: 2px 2px 3px rgb(0 0 0 / 20%);
  /deep/.ant-input {
    border: 1px solid #fff;
  }

  .ant-cascader-picker {
    min-width: 220px;
  }
}
/deep/.ant-input {
  height: 38px;
}
</style>
