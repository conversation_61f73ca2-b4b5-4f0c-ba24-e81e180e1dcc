<template>
  <div class="wrap">
    <a-cascader :allowClear="false" :value="value" :options="options" @change="onChange" />
  </div>
</template>

<script>
// const layerParam={
//   普查土壤点:""
// }
export default {
  props: {
    layerConfig: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      value: null,
      // options: [
      //   {
      //     value: "定位预警土壤",
      //     label: "定位预警土壤",
      //     children: [
      //       {
      //         value: "2017",
      //         label: "2017",
      //       },
      //       {
      //         value: "2018",
      //         label: "2018",
      //       },
      //       {
      //         value: "2019",
      //         label: "2019",
      //       },
      //       {
      //         value: "2020",
      //         label: "2020",
      //       },
      //       {
      //         value: "2021",
      //         label: "2021",
      //       },
      //       {
      //         value: "2022",
      //         label: "2022",
      //       },
      //     ],
      //   },
      //   {
      //     value: "定位预警监测",
      //     label: "定位预警农产品",
      //     children: [
      //       {
      //         value: "2017",
      //         label: "2017",
      //       },
      //       {
      //         value: "2018",
      //         label: "2018",
      //       },
      //       {
      //         value: "2019",
      //         label: "2019",
      //       },
      //       {
      //         value: "2020",
      //         label: "2020",
      //       },
      //       {
      //         value: "2021",
      //         label: "2021",
      //       },
      //       {
      //         value: "2022",
      //         label: "2022",
      //       },
      //     ],
      //   },
      //   {
      //     value: "自证农产品",
      //     label: "自证采样农产品",
      //     children: [
      //       {
      //         value: "2020",
      //         label: "2020",
      //       },
      //       {
      //         value: "2021",
      //         label: "2021",
      //       },
      //       {
      //         value: "2022",
      //         label: "2022",
      //       },
      //     ],
      //   },
      //   {
      //     value: "普查农产品",
      //     label: "普查农产品",
      //   },
      //   {
      //     value: "普查土壤点",
      //     label: "普查土壤点",
      //   },
      //   {
      //     value: "详查农产品",
      //     label: "详查农产品",
      //   },
      //   {
      //     value: "详查土壤点",
      //     label: "详查土壤点",
      //   },
      // ],
      options: [],
    };
  },
  methods: {
    onChange(e) {
      this.value = e;
    },
    handleOptions() {
      let ary = ["land_area"];
      let result = this.layerConfig.filter((item) => {
        return !ary.includes(item.value);
      });
      this.options = result;
    },
  },
  mounted() {
    this.handleOptions();
    this.value = ["普查土壤点"];
  },
  watch: {
    value(e) {
      console.log("value---->", e);
      this.$emit("dwDateChangeEvent", e);
      let info = this.layerConfig.find((item) => item.value == e[0]);
      let type = "cbl";
      if (info) type = info.type;
      this.$emit("dwTableChangeEvent", type);
    },
  },
};
</script>

<style lang="less" scoped>
.wrap {
  background: #ffffff;
  border-radius: 4px;
  padding: 3px;
  box-shadow: 2px 2px 3px rgb(0 0 0 / 20%);
  /deep/.ant-input {
    border: 1px solid #fff;
  }

  .ant-cascader-picker {
    min-width: 220px;
  }
}
/deep/.ant-input {
  height: 39px;
}
</style>
