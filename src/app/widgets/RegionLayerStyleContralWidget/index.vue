<template>
  <div class="jimu-widget">
    <!-- <TypeChange /> -->
    <TableChange class="item" @tableChangeEvent="tableChangeEvent" />
    <template v-if="tableValue == 'area'">
      <PloughChange class="item" @ploughChangeEvent="ploughChangeEvent" />
    </template>
    <template v-else>
      <DwDateChange :layerConfig="this.config.layerConfig" class="item" @dwDateChangeEvent="dwDateChangeEvent" @dwTableChangeEvent="dwTableChangeEvent" />
    </template>
    <FieldsChange class="item" :fieldsKey="fieldsKey" @fieldsChangeEvent="fieldsChangeEvent" />
  </div>
</template>

<script>
const cblParam = {
  cd_cbl: "cd_class",
  hg_cbl: "hg_class",
  as_cbl: "as_class",
  pb_cbl: "pb_class",
  cr_cbl: "cr_class",
};
const ncpCblParam = {
  cd_cbl: "cdn_class",
  hg_cbl: "hgn_class",
  as_cbl: "asn_class",
  pb_cbl: "pbn_class",
  cr_cbl: "crn_class",
};
import BaseWidget from "@/BaseWidget";

import DwDateChange from "./components/DwDateChange.vue";
import FieldsChange from "./components/FieldsChange.vue";
import PloughChange from "./components/PloughChange.vue";
import TableChange from "./components/TableChange.vue";
export default {
  name: "RegionLayerStyleContralWidget",
  components: {
    // TypeChange
    DwDateChange,
    PloughChange,
    TableChange,
    FieldsChange,
  },
  mixins: [BaseWidget],
  data() {
    return {
      level: "city",
      AdminParam: {
        city: "CXZQMC",
        county: "FXZQDM",
        town: "TXZQDM",
        village: "XZQDM",
      },
      filter: null,
      fieldsKey: "area",
      fields: "land_area",
      param: {
        uri: "",
        filter: "",
        labelFields: "",
        DataFields: [],
      },
      tableValue: "area",
      layer: "",
      layerName: "land_lbhf",
    };
  },
  methods: {
    tableChangeEvent(e) {
      this.tableValue = e;
    },
    dwDateChangeEvent(e) {
      let info = this.config.layerConfig.find((item) => item.value == e[0]);
      let filter = [["=", "dwlx", info.dwlx]];
      if (e[1]) {
        filter.push(["=", "date", e[1]]);
      }
      this.fieldsKey = "cbl";
      this.filter = filter;
    },
    ploughChangeEvent(e) {
      console.log("更改选项", e);
      this.fieldsKey = e[0];
      this.filter = [["=", "date", e[1]]];
      switch (e[1]) {
        case "2020":
          this.layerName = "land_lbhf";
          break;
        case "2023":
          this.layerName = "land_dttz";
          break;
        default:
          this.layerName = "land_lbhf";
          break;
      }
      console.log(this.layerName, "操作的图层");
      this.bus.$emit(this.$events.layer.CHECKED_LAYER, this.layerName);
    },
    fieldsChangeEvent(e) {
      this.fields = e;
      this.bus.$emit(this.$events.special.CHART_UPDATE, [this.fieldsKey, this.filter, e]);
      this.changeLayerType(this.fieldsKey, this.filter, e);
    },
    dwTableChangeEvent(e) {
      this.fieldsKey = e;
    },
    findObject(data, key, child) {
      console.log("findObject", data, key, child);
      
      if (child) {
        let info = data.find((item) => item.value == key);
        console.log("find info ", info);
        if (info) return info.children.find((item) => item.value == child);
      } else {
        return data.find((item) => item.value == key);
      }
    },
    changeLayer(a, b) {
      console.log("changeLayer", a, b);
      if (a == "area" || a == "area_ratio") {
        const layer = this.findObject(this.config.layerConfig, "land_area");
        if (this.thisLayer?.key == layer.key) {
          return;
        }
        this.bus.$emit(this.$events.map.LAYER_HIDE, [this.thisLayer]);
        this.bus.$emit(this.$events.map.LAYER_SHOW, [layer]);
        this.thisLayer = this.findObject(this.config.layerConfig, "land_area");
      }
      if (a == "cbl" || a == "ncp_cbl") {
        // this.bus.$emit(this.$events.map.LAYER_SHOW, [layer])
        let layer;
        if (b.length == 1) {
          layer = this.findObject(this.config.layerConfig, b[0][2]);
        } else {
          layer = this.findObject(this.config.layerConfig, b[0][2], [b[1][2]]);
        }
        console.log("设置cbl的filter", layer);
        if (this.thisLayer?.key == layer.key) {
          return;
        }
        this.bus.$emit(this.$events.map.LAYER_HIDE, [this.thisLayer]);
        this.bus.$emit(this.$events.map.LAYER_SHOW, [layer]);
        this.thisLayer = layer;
      }
    },
    setAreaStyle(c) {
      let res;
      if (c == "land_yxbh" || c == "land_yxbh_ratio") {
        res = ["==", ["get", "zh_class"], 1];
      }
      if (c == "land_aqly" || c == "land_aqly_ratio") {
        res = ["==", ["get", "zh_class"], 2];
      }
      if (c == "land_yggk" || c == "land_yggk_ratio") {
        res = ["==", ["get", "zh_class"], 3];
      }
      this.setModuleLayerFilter(this.layerName, "zh_class", res);
      this.bus.$emit(this.$events.map.SET_LAYER_FILTER, this.layerName);
    },
    changeLayerType(a, b, c) {
      console.log("changeLayerType", a, b, c);
      if (a == "area" || a == "area_ratio") {
        this.setAreaStyle(c);
      }
      if (a == "cbl") {
        let layer;
        if (b.length == 1) {
          layer = this.findObject(this.config.layerConfig, b[0][2]);
        } else {
          layer = this.findObject(this.config.layerConfig, b[0][2], [b[1][2]]);
        }
        console.log("设置cbl的filter", layer);
        this.setModuleLayerStyle(layer.key, "circle-color", [
          "match",
          ["get", cblParam[c]],
          1,
          "rgb(0, 179, 0)",
          2,
          "rgb(179, 179, 0)",
          3,
          "rgb(179, 0, 0)",
          "rgb(0, 179, 0)",
        ]);
        this.bus.$emit(this.$events.map.SET_LAYER_STYLE, layer.key);
      }
      if (a == "ncp_cbl") {
        let layer;
        if (b.length == 1) {
          layer = this.findObject(this.config.layerConfig, b[0][2]);
          this.setModuleLayerStyle(layer.key, "circle-color", [
            "match",
            ["get", ncpCblParam[c]],
            1,
            "rgb(0, 179, 0)",
            2,
            "rgb(179, 179, 0)",
            3,
            "rgb(179, 0, 0)",
            "rgb(0, 179, 0)",
          ]);
        } else {
          layer = this.findObject(this.config.layerConfig, b[0][2], [b[1][2]]);
          this.setModuleLayerStyle(layer.key, "circle-color", [
            "match",
            ["get", ncpCblParam[c]],
            1,
            "rgb(0, 179, 0)",
            2,
            "rgb(179, 179, 0)",
            3,
            "rgb(179, 0, 0)",
            "rgb(0, 179, 0)",
          ]);
        }

        this.bus.$emit(this.$events.map.SET_LAYER_STYLE, layer.key);
      }
    },
  },
  watch: {
    filter(e) {
      this.bus.$emit(this.$events.special.CHART_UPDATE, [this.fieldsKey, e, this.fields]);
      this.changeLayer(this.fieldsKey, e, this.fields);

      this.changeLayerType(this.fieldsKey, e, this.fields);
    },
  },
};
</script>

<style lang="less" scoped>
.jimu-widget {
  display: flex;
  height: 42px;
  .item {
    margin-right: 10px;
    height: 42px;
  }
}
</style>
