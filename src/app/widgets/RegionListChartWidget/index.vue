<template>
  <div class="jimu-widget wrap">
    <div class="jimu-tool-widget">
      <div class="jimu-widget-title-ui">{{ chartTitle }}</div>
      <a-radio-group class="btn-list" v-model="index" button-style="solid">
        <a-radio-button value="city"> 市 </a-radio-button>
        <a-radio-button value="county"> 县 </a-radio-button>
        <a-radio-button value="town"> 镇 </a-radio-button>
        <a-radio-button value="village"> 村 </a-radio-button>
      </a-radio-group>
      <Chart class="Chart" @barClick="chartBarClick" :options="option"></Chart>
    </div>
  </div>
</template>

<script>
import Chart from "@/components/Chart/index.vue";
import BaseWidget from "@/BaseWidget";
import Config from "./chart.config.js";

export default {
  name: "RegionListChartWidget",
  components: { Chart },
  mixins: [BaseWidget],
  data() {
    return {
      index: "city",
      Config,
      admin: [],
      scope: "city",
      region: "",
      regionCode: "",
      type: "",
      regionConfig: {
        province: {
          name: "PXZQMC",
          code: "PXZQDM",
        },
        city: {
          name: "CXZQMC",
          code: "CXZQDM",
        },
        county: {
          name: "FXZQMC",
          code: "FXZQDM",
        },
        town: {
          name: "TXZQMC",
          code: "TXZQDM",
        },
        village: {
          name: "XZQMC",
          code: "XZQDM",
        },
      },
      adminData: null,
      fields: null,
      filter: [],
      option: {},
    };
  },
  created() {},
  mounted() {
    this.bus.$on(this.$events.special.CHART_UPDATE, this.upDataChange);
    this.bus.$on(this.$events.admin.CHANGE_ADMIN, this.changeAdmin);
  },
  watch: {
    index(e) {
      this.getData();
      this.bus.$emit("ChartLevelChange", e);
    },
  },
  computed: {
    chartTitle() {
      let admin = this.adminData ? this.adminData.name : "广西";
      let param = {
        cbl: "",
        area: "耕地面积",
        area_ratio: "耕地面积比例",
        ncp_cbl: "",
      };
      let info = param[this.type] ? param[this.type] : "";
      let dw = "";
      if (this.type == "cbl" || this.type == "ncp_cbl") {
        dw =
          this.filter[0][2] +
          (this.filter[1] ? this.filter[1][2] : "") +
          this.Config.fieldsConfig?.[this.fields]?.name;
      }
      return admin + dw + info + "排名";
    },
  },
  methods: {
    changeAdmin(e) {
      if (e) {
        this.adminData = e;
        this.region = e.region;
        this.regionCode = e.code;
        this.index = e.nextRegion;
      } else {
        this.adminData = "";
        this.region = "";
        this.regionCode = "";
        this.index = "city";
      }
      this.getData();
    },
    async getData() {
      console.log(8888888888888888888);
      let param = {
        format: "json",
        returnGeometry: false,
        orderField: this.fields,
        orderType: "DESC",
        limit: 99999,
      };
      let filter = ["all", ...this.filter];
      if (this.regionCode) {
        filter.push([
          "=",
          this.regionConfig[this.region].code,
          this.regionCode,
        ]);
      } else {
        filter.push(["!=", "CXZQDM", "4500"]);
      }
      if (filter.length == 2) {
        filter = filter[1];
      }
      param.filter = filter;
      let url = this.Config.apiListConfig[this.type][this.index].url;
      let data = await this.$apis.feature.query(url, param);

      // data = data.sort((a, b) => {
      //   return Number(b[this.fields]) - Number(a[this.fields])
      // })
      this.setChartData(data);
    },
    setChartData(data) {
      let nameList = data.map((item) => {
        return item[this.regionConfig[this.index].name];
      });

      let series =
        this.fields == "land_area"
          ? ["land_aqly", "land_yxbh", "land_yggk"]
          : [this.fields];
      series = series.map((item) => {
        let result = this.Config.fieldsConfig[item];
        result.data = data.map((a) => {
          if (this.type !== "area") {
            return a[item] * 100;
          }
          return a[item];
        });
        return result;
      });
      let option = this.$utils.object.deepCloneObj(
        this.Config.chartConfig[this.type]
      );
      option.series = this.$utils.object.deepCloneObj(series);
      option.xAxis.data = nameList;
      this.option = option;
      console.log("option", this.option);
      if (nameList.length > 20) {
        this.$set(this.option.dataZoom, "end", (15 / data.length) * 100);
      } else {
        this.$set(this.option, "end", 100);
      }
    },
    upDataChange(e) {
      this.filter = e[1];
      this.type = e[0];
      this.fields = e[2];
      this.getData();
    },
    async chartBarClick(e) {
      let list = {
        city: "gx_land_region_city",
        county: "gx_land_region_county",
        town: "gx_land_region_town",
        village: "gx_land_region_village",
      };
      let params = {
        returnGeometry: false,
        format: "json",
        filter: ["=", this.regionConfig[this.index].name, e.name],
      };
      let data = await this.$apis.feature.query(list[this.index], params);
      let obj = data[0];
      let listData = [];
      if (obj.CXZQDM) {
        listData.push({ index: 0, codeKey: "CXZQDM", code: obj.CXZQDM });
      }
      if (obj.FXZQDM) {
        listData.push({ index: 1, codeKey: "FXZQDM", code: obj.FXZQDM });
      }
      if (obj.TXZQDM) {
        listData.push({ index: 2, codeKey: "TXZQDM", code: obj.TXZQDM });
      }
      if (obj.XZQDM) {
        listData.push({ index: 3, codeKey: "XZQDM", code: obj.XZQDM });
      }
      this.bus.$emit("emit_admin_change", listData);
    },
    onClickChangeArea(val) {
      this.bus.$emit("Area", { Area: val });
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";
.wrap {
  padding: 12px;
  background-color: #eef0f9;
}
.jimu-tool-widget {
  width: 100%;
  height: 100%;
}
.btn-list {
  position: absolute;
  right: 20px;
  top: 20px;
  z-index: 10000;
}
.jimu-widget-title-ui {
  margin: 20px 0 0 20px;
}
/deep/.ant-radio-group-solid
  .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
  background: @primaryColor;
  border-color: @primaryColor;
}
/deep/.ant-radio-button-wrapper {
  padding: 0 35px;
}
</style>
