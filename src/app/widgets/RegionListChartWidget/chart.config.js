export default {
  chartConfig: {
    area: {
      textStyle: {
        color: "#002159",
      },
      legend: {},
      tooltip: {
        trigger: "axis",
        formatter: function (params) {
          // return `<div>${ params[0].name }</div><div>${ params[0].value.toFixed(
          //   2
          // ) }%</div>`
          // return 'xxxx'
          var relVal = params[0].name;
          for (var i = 0, l = params.length; i < l; i++) {
            relVal +=
              "<br/>" +
              params[i].seriesName +
              " : " +
              Number(params[i].value).toFixed(2) +
              "亩";
          }
          return relVal;
          // }
        },
      },
      grid: {
        left: "3%",
        right: "8%",
        bottom: "70",
        containLabel: true,
      },
      xAxis: {
        axisLabel: {
          showMaxLabel: true,
          interval: 0,
          show: true,
        },
        type: "category",
        data: [],
      },
      yAxis: {
        type: "value",
      },
      series: [],
      dataZoom: {
        show: true,
        type: "slider",
        xAxisIndex: [0],
        start: 0,
        height: 15,
        bottom: 50,

        handleSize: 0,
      },
    },
    area_ratio: {
      textStyle: {
        color: "#002159",
      },
      grid: {
        left: "3%",
        right: "8%",
        bottom: "70",
        containLabel: true,
      },
      tooltip: {
        trigger: "axis",
        formatter: function (params) {
          return `<div>${params[0].name}</div><div>${params[0].value.toFixed(
            2
          )}%</div>`;
        },
      },
      xAxis: {
        axisLabel: {
          showMaxLabel: true,
          interval: 0,
          show: true,
        },
        type: "category",
        data: [],
      },
      yAxis: {
        type: "value",
        axisLabel: { formatter: "{value}%" },
      },
      series: [],
      dataZoom: {
        show: true,
        type: "slider",
        xAxisIndex: [0],
        start: 0,
        height: 15,
        bottom: 50,

        handleSize: 0,
      },
    },
    cbl: {
      legend: {},
      tooltip: {
        trigger: "axis",
        formatter: function (params) {
          return `<div>${params[0].name}</div><div>${params[0].value.toFixed(
            2
          )}%</div>`;
        },
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "70",
        containLabel: true,
      },
      xAxis: {
        axisLabel: {
          showMaxLabel: true,
          interval: 0,
          show: true,
        },
        type: "category",
        data: [],
      },
      yAxis: {
        type: "value",
        axisLabel: { formatter: "{value}%" },
      },
      dataZoom: {
        show: true,
        type: "slider",
        xAxisIndex: [0],
        start: 0,
        height: 15,
        bottom: 50,

        handleSize: 0,
      },
      series: [],
    },
    ncp_cbl: {
      textStyle: {
        color: "#002159",
      },
      legend: {},
      tooltip: {
        trigger: "axis",
        formatter: function (params) {
          return `<div>${params[0].name}</div><div>${params[0].value.toFixed(
            2
          )}%</div>`;
        },
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "70",
        containLabel: true,
      },
      xAxis: {
        axisLabel: {
          showMaxLabel: true,
          interval: 0,
          show: true,
        },
        type: "category",
        data: [],
      },
      yAxis: {
        type: "value",
      },
      series: [],
      dataZoom: {
        show: true,
        type: "slider",
        xAxisIndex: [0],
        start: 0,
        height: 15,
        bottom: 50,

        handleSize: 0,
      },
    },
  },
  fieldsConfig: {
    land_yxbh: {
      name: "优先保护",
      type: "bar",
      stack: "总量",
      barMaxWidth: 40,
      barGap: "40%",
      data: [],
      itemStyle: {
        color: "#7ECF52",
      },
      normal: {
        label: {
          show: true, //开启显示
          position: "top", //在上方显示
          textStyle: {
            //数值样式
            color: "#333",
            fontSize: 12,
          },
        },
      },
    },
    land_yxbh_ratio: {
      name: "优先保护",
      type: "bar",
      barMaxWidth: 40,
      barGap: "40%",
      data: [],
      itemStyle: {
        color: "#7ECF52",
      },
      normal: {
        label: {
          show: true, //开启显示
          position: "top", //在上方显示
          textStyle: {
            //数值样式
            color: "#333",
            fontSize: 12,
          },
        },
      },
    },
    land_aqly_ratio: {
      name: "安全利用",
      type: "bar",
      barMaxWidth: 40,
      barGap: "40%",
      data: [],
      itemStyle: {
        color: "#EDCC5F",
      },
      normal: {
        label: {
          show: true, //开启显示
          position: "top", //在上方显示
          textStyle: {
            //数值样式
            color: "#333",
            fontSize: 12,
          },
        },
      },
    },
    land_yggk_ratio: {
      name: "严格管控",
      type: "bar",
      barMaxWidth: 40,
      barGap: "40%",
      data: [],
      itemStyle: {
        color: "#EE6860",
      },
      normal: {
        label: {
          show: true, //开启显示
          position: "top", //在上方显示
          textStyle: {
            //数值样式
            color: "#333",
            fontSize: 12,
          },
        },
      },
    },
    land_yggk: {
      name: "严格管控",
      type: "bar",
      stack: "总量",
      barMaxWidth: 40,
      barGap: "40%",
      data: [],
      itemStyle: {
        color: "#EE6860",
      },
      normal: {
        label: {
          show: true, //开启显示
          position: "top", //在上方显示
          textStyle: {
            //数值样式
            color: "#333",
            fontSize: 12,
          },
        },
      },
    },
    land_aqly: {
      name: "安全利用",
      type: "bar",
      stack: "总量",
      barMaxWidth: 40,
      barGap: "40%",
      data: [],
      itemStyle: {
        color: "#EDCC5F",
      },
      normal: {
        label: {
          show: true, //开启显示
          position: "top", //在上方显示
          textStyle: {
            //数值样式
            color: "#333",
            fontSize: 12,
          },
        },
      },
      // large: true,
    },
    land_area: {
      name: "耕地",
    },
    cd_cbl: {
      name: "镉超标率",
      type: "bar",
      stack: "总量",
      barMaxWidth: 40,
      barGap: "40%",
      itemStyle: {
        color: "#4065E0",
      },
      data: [],
      normal: {
        label: {
          show: true, //开启显示
          position: "top", //在上方显示
          textStyle: {
            //数值样式
            color: "#333",
            fontSize: 12,
          },
        },
      },
    },
    hg_cbl: {
      name: "汞超标率",
      type: "bar",
      stack: "总量",
      barMaxWidth: 40,
      barGap: "40%",
      itemStyle: {
        color: "#4065E0",
      },
      normal: {
        label: {
          show: true, //开启显示
          position: "top", //在上方显示
          textStyle: {
            //数值样式
            color: "#333",
            fontSize: 12,
          },
        },
      },
      data: [],
    },
    as_cbl: {
      name: "砷超标率",
      type: "bar",
      stack: "总量",
      barMaxWidth: 40,
      barGap: "40%",
      itemStyle: {
        color: "#4065E0",
      },
      normal: {
        label: {
          show: true, //开启显示
          position: "top", //在上方显示
          textStyle: {
            //数值样式
            color: "#333",
            fontSize: 12,
          },
        },
      },
      data: [],
    },
    pb_cbl: {
      name: "铅超标率",
      type: "bar",
      stack: "总量",
      barMaxWidth: 40,
      barGap: "40%",
      itemStyle: {
        color: "#4065E0",
      },
      normal: {
        label: {
          show: true, //开启显示
          position: "top", //在上方显示
          textStyle: {
            //数值样式
            color: "#333",
            fontSize: 12,
          },
        },
      },
      data: [],
    },
    cr_cbl: {
      name: "铬超标率",
      type: "bar",
      stack: "总量",
      barMaxWidth: 40,
      barGap: "40%",
      itemStyle: {
        color: "#4065E0",
      },
      normal: {
        label: {
          show: true, //开启显示
          position: "top", //在上方显示
          textStyle: {
            //数值样式
            color: "#333",
            fontSize: 12,
          },
        },
      },
      data: [],
    },
  },
  apiListConfig: {
    area: {
      city: {
        fields: "CXZQMC",
        nextFields: "",
        url: "land_plough_city",
      },
      county: {
        fields: "FXZQMC",
        url: "land_plough_county",
      },
      town: {
        fields: "TXZQMC",
        url: "land_plough_town",
      },
      village: {
        fields: "XZQMC",
        url: "land_plough_village",
      },
    },
    area_ratio: {
      city: {
        fields: "CXZQMC",
        url: "land_plough_city",
      },
      county: {
        fields: "FXZQMC",
        url: "land_plough_county",
      },
      town: {
        fields: "TXZQMC",
        url: "land_plough_town",
      },
      village: {
        fields: "XZQMC",
        url: "land_plough_village",
      },
    },
    cbl: {
      city: {
        fields: "CXZQMC",
        url: "land_tr_dw_city",
      },
      county: {
        fields: "FXZQMC",
        url: "land_tr_dw_county",
      },
      town: {
        fields: "TXZQMC",
        url: "land_tr_dw_town",
      },
      village: {
        fields: "XZQMC",
        url: "land_tr_dw_village",
      },
    },
    ncp_cbl: {
      city: {
        fields: "CXZQMC",
        url: "land_ncp_dw_city",
      },
      county: {
        fields: "FXZQMC",
        url: "land_ncp_dw_county",
      },
      town: {
        fields: "TXZQMC",
        url: "land_ncp_dw_town",
      },
      village: {
        fields: "XZQMC",
        url: "land_ncp_dw_village",
      },
    },
  },
};
