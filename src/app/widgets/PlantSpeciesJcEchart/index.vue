<template>
  <div class="jimu-widget jimu-tool-widget box">
    <TitleBar :title="`${params.name}原生境保护点数量`"></TitleBar>
    <Echart :options="option"></Echart>
  </div>
</template>

<script>
import TitleBar from '../../Components/TitleBar'
import Echart from '@/components/Chart/'
import BaseWidget from '@/BaseWidget'
export default {
  name: 'PlantSpeciesJcEchart',

  mixins: [BaseWidget],

  components: {
    TitleBar,
    Echart
  },

  data() {
    return {
      params: {
        code: 45,
        level: 0,
        date: '2018',
        name: '广西',
        semi_annual: '上半年',
        crop: ''
      },
      type: ['PXZQMC', 'CXZQMC', 'FXZQMC', 'FXZQMC'],
      option: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.1]
        },
        yAxis: {
          type: 'category',
          data: [],
          axisLabel: {
            textStyle: {
              fontSize: 16,
              fontFamily: 'PingFangSC-Semibold, PingFang SC'
            }
          },
          axisTick: {
            show: false
          }
        },
        series: [
          {
            type: 'bar',
            barWidth: 30,
            data: []
          }
        ]
      }
    }
  },
  methods: {
    formatData(value) {
      console.log(value,']]]]]]')
      if (!value) {
        this.params = {
          code: 45,
          level: 0,
          date: '',
          name: '广西',
          crop: ''
        }
      } else {
        this.params.code = value.code
        this.params.level = value.level
        this.params.name = value.name
        this.params.semi_annual = value.semi_annual
      }
    },
    getFilters(value) {
      console.log(value)
      this.params.date = value.date
      this.params.crop = value.crop
      this.params.semi_annual = value.semi_annual
      this.getData()
    },
    async getData() {
      this.option.yAxis.data = []
      this.option.series[0].data = []
      let filter = [
        'all',
        ['=', this.type[this.params.level], this.params.name]
      ]

      if (this.params.date) filter.push(['=', 'date', this.params.date])
      if (this.params.semi_annual)
        filter.push(['=', 'semi_annual', this.params.semi_annual])
      if (this.params.crop) filter.push(['like', 'mbwz', this.params.crop])

      if (filter.length == 2) filter = filter[1]

      let param = {
        returnGeometry: false,
        format: 'json',
        filter,
        groupFields: [this.type[this.params.level + 1]],
        aggregates: [['ysc_bm', 'count', 'desc', 'distinct']]
      }
      let res = await this.$apis.feature.aggregate('plant_point_gx', param)
      console.log(param,res,'llll')
      res.forEach(item => {
        this.option.yAxis.data.unshift(item[this.type[this.params.level + 1]])
        this.option.series[0].data.unshift(item.ysc_bm_count)
      })
    }
  },
  mounted() {
    this.formatData(this.params)
    this.bus.$on('setPlantfilters', this.getFilters)
    this.bus.$on(this.$events.admin.CHANGE_ADMIN, this.formatData)
  }
}
</script>

<style lang="less" scoped>
.box {
  padding: 12px;
}
</style>
