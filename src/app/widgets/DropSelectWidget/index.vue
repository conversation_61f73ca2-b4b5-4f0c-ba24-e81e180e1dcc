<template>
  <div class="jimu-widget select-list" v-if="selectList.length > 0">
    <div class="select-wrap" v-for="(item, index) in selectList" :key="index">
      <a-select
        v-model="item.selected"
        :placeholder="'请选择' + item.name"
        @change="ChangeHandler($event, index)"
      >
        <a-select-option
          v-for="option in item.options"
          :key="option.value"
          :value="option.value"
        >
          {{ option.label }}
        </a-select-option>
      </a-select>
    </div>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget.js";

export default {
  name: "DropSelectWidget",

  mixins: [BaseWidget],

  data() {
    return {
      selectList: [],
      selectIndex: null,
    };
  },
  methods: {
    // 直接查询选项
    async optionsRequest(item, index) {
      if (!item.options || item.options.length == 0 || index == -1) {
        item.options = await this.getOptions(item);
        this.selectList.push(item);
      }
    },
    // 关联查询选项
    async optionsConnectRequest(item, index) {
      let parentIndex = this.selectList.findIndex(
        (items) => items.id == item.connect_id
      );
      let parent = this.selectList.find((items) => items.id == item.connect_id);
      let info = this.selectList.find((items) => items.id == item.id);
      if (info && this.selectIndex !== parentIndex) return;
      let filter = [];
      if (parent && parent.selected)
        filter.push(["=", item.request_field, parent.selected]);
      item.options = await this.getOptions(item, filter);
      if (index > -1 && this.selectIndex !== index) {
        if (item.queryAll) {
          item.selected = "";
        } else if (item.selected !== undefined) {
          item.selected = undefined;
        }
        this.$set(this.selectList, index, item);
      } else {
        this.selectList.push(item);
      }
    },
    // selectList初始化
    async init() {
      for (const item of this.config.options) {
        let index = this.selectList.findIndex((items) => items.id == item.id);
        if (item.type == "request" && !item.connect_id) {
          await this.optionsRequest(item, index);
        } else if (item.type == "request" && item.connect_id) {
          await this.optionsConnectRequest(item, index);
        } else {
          if (index > -1) continue;

          if (item.queryAll) {
            item.options.unshift({
              label: item.queryAllLabel || "全部",
              value: "",
            });
          }
          this.selectList.push(item);
        }
      }
    },
    async getOptions(item, filter) {
      let filters = [];
      if (item.filter) filters.push(...item.filter);
      if (filter && filter.length > 0) filters.push(...filter);
      if (filters.length > 1) filters.unshift("all");
      if (filters.length == 1) filters = filters[0];
      let params = {
        returnGeometry: false,
        format: "json",
      };
      if (filters && filters.length > 0) params.filter = filters;
      let data = await this.$apis.feature.query(item.formName, params);
      let ary = [];
      data.forEach((items) => {
        let idx = ary.findIndex(
          (info) => info[item.field] == items[item.field]
        );
        if (idx > -1) return;
        ary.push(items);
      });
      let options = ary.map((items) => {
        return {
          label: item.label ? items[item.label] : items[item.field],
          value: items[item.field],
        };
      });
      if (item.queryAll) {
        options.unshift({
          label: item.queryAllLabel || "全部",
          value: "",
        });
      }
      return options;
    },
    async ChangeHandler(val, index) {
      if (!this.config.eventName) {
        this.$notification.error({
          message: "配置提示",
          description: "请给组件DropSelectWidget配置相关的eventName",
        });
        return;
      }
      this.$set(this.selectList[index], "selected", val);
      this.selectList[index].selected = val;
      this.selectIndex = index;
      await this.init();
      let selected = [];
      this.selectList.forEach((item) => {
        if (item.selected) {
          let info = item.options.find(
            (option) => option.value == item.selected
          );
          selected.push({
            id: item.id,
            value: item.selected,
            data: info,
          });
        }
      });
      this.selected = selected;
      this.bus.$emit(this.config.eventName, selected);
      this.handleLayerFilter();
    },
    async subFormFilter(layer, value) {
      const filter = ["=", layer.filterField, value];
      let res = await this.$apis.feature.query(layer.table, {
        format: "json",
        filter,
      });

      const arr = [...new Set(res.map((item) => item[layer.tableField]))];
      return ["in", ["get", layer.mainTableField], ["literal", arr]];
    },
    async handleFilter(layer, value) {
      switch (layer.judge) {
        case "match":
          return [layer.judge, ["get", layer.field], value, true, false];
        case "indexOf":
          return [
            "!=",
            ["number", ["index-of", value, ["get", layer.field]]],
            -1,
          ];
        case "table":
          return await this.subFormFilter(layer, value);
        default:
          if (layer.fieldConnectId) {
            let info = this.selectList.find(
              (item) => item.id == layer.fieldConnectId
            );
            if (info) {
              let options = info.options.filter((item) => {
                if (info.selected) return item.value == info.selected;
                return item.value;
              });
              let fields = options.map((item) => {
                return [layer.judge, ["get", item.value], value];
              });
              let operator = layer.judgeAll ? layer.judgeAll : "any";
              return [operator, ...fields];
            }
          }
          return [layer.judge, ["get", layer.field], value];
      }
    },
    handleLayerFilter() {
      const layers = {};
      this.selectList.forEach(async (item) => {
        if (!item.layers || item.layers.length == 0) return;
        // 组合filter
        for (const layer of item.layers) {
          try {
            if (item.selected) {
              const filter = await this.handleFilter(layer, item.selected);
              if (
                layers[layer.id] &&
                layers[layer.id].length == 3 &&
                layers[layer.id][0] != "all"
              ) {
                layers[layer.id] = ["all", layers[layer.id], filter];
              } else if (layers[layer.id] && layers[layer.id][0] == "all") {
                layers[layer.id].push(filter);
              } else {
                layers[layer.id] = filter;
              }
              if (layers[layer.id]) {
                this.setLayerFilter(layer, layers[layer.id]);
              }
            } else {
              if (layer.field) this.setLayerFilter(layer, null);
            }
          } catch (error) {
            console.log(error, "error");
          }
        }
      });
    },
    setLayerFilter(layer, filter) {
      if (layer.includes) {
        const layers = this.map.getStyle().layers;
        layers.forEach((item) => {
          if (item.id.includes(layer.id)) {
            this.setModuleLayerFilter(item.id, layer.field, filter);
            this.bus.$emit(this.$events.map.SET_LAYER_FILTER, item.id);
          }
        });
      } else {
        this.setModuleLayerFilter(layer.id, layer.field, filter);
        this.bus.$emit(this.$events.map.SET_LAYER_FILTER, layer.id);
      }
    },
  },
  allLoaded() {
    // 定时为了等待其他组件先加载完毕
    setTimeout(async () => {
      await this.init();
      console.log("选项:", this.selectList);
      if (Array.isArray(this.selectList)) {
        let selected = [];
        this.selectList.forEach((item) => {
          if (item.selected) {
            let info = item.options.find(
              (option) => option.value == item.selected
            );
            selected.push({
              id: item.id,
              value: item.selected,
              data: info,
            });
          }
        });
        this.selected = selected;
        this.bus.$emit(this.config.eventName, selected);
        this.handleLayerFilter();
      }
    }, 500);
  },
};
</script>

<style lang="less" scoped>
.select-list {
  display: flex;
  align-items: center;
}
.select-wrap {
  background: #ffffff;
  // border: 1px solid #fff;
  border-radius: 4px;
  padding: 4px;
  box-shadow: 2px 2px 3px rgb(0 0 0 / 20%);
  margin-right: 10px;
}
/deep/.ant-select-selection--single {
  border: none;
}
/deep/.ant-select {
  min-width: 120px;
  font-size: 17px;
}
</style>
