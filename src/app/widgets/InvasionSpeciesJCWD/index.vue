<template>
  <div class="jimu-widget jimu-tool-widget box" ref="myElement">
    <div class="tabs-content">
      <div
        :class="['tabs', item == currentTab ? 'active' : '']"
        v-for="(item, index) in tabs"
        :key="index"
        @click="currentTab = item"
      >
        {{ item }}
      </div>
    </div>

    <div class="content scrollbar" v-show="currentTab == '项目简介'">
      <div v-for="(item, index) in profile" :key="index">
        <div class="content_title">{{ item.title }}</div>
        <div
          class="content_font"
          style="text-align: justify; text-indent: 2em"
          v-for="(i, index) in item.info"
          :key="index"
        >
          <p v-html="i"></p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";

export default {
  name: "PlantSpeciesJcEchart",

  mixins: [BaseWidget],

  computed: {},

  watch: {
    async adminData() {},
  },

  data() {
    return {
      currentTab: "项目简介",
      tabs: ["项目简介"],
      profile: [],
    };
  },

  allLoaded() {
    // 定时为了等待其他组件先加载完毕
    setTimeout(async () => {
      if (Array.isArray(this.config.profile)) {
        this.profile = this.config.profile;
      }
    }, 500);
  },
};
</script>

<style lang="less" scoped>
.box {
  .part {
    width: 100%;
    margin-bottom: 2vh;
    .table {
      margin-bottom: 20px;
    }
    .more {
      cursor: pointer;
    }
    .chart-box {
      position: relative;
      .back {
        position: absolute;
        right: 1vh;
        top: 0;
        cursor: pointer;
        color: #002159;
      }
    }
  }
}
.chart-wrap {
  height: 30vh;
}
.tabs-content {
  display: flex;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #e7ebf4;
  overflow: hidden;
  margin-bottom: 1vh;
  .tabs {
    width: 100%;
    height: 40px;
    text-align: center;
    // border: 1px solid #ccc;
    line-height: 40px;
    cursor: pointer;
  }

  .active {
    border-bottom: none;
    // color: rgb(62, 145, 247);
    color: #fff;
    background-color: #4065e0;
  }
}
.scrollbar {
  height: 100%;
  overflow-y: scroll;
  // 隐藏滚动条
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.content {
  height: calc(100% - 65px);
  display: flex;
  flex-direction: column;
  overflow-y: scroll;
  .content_title {
    font-size: 22px;
    font-weight: bold;
    color: #333;
    margin: 10px 0;
    line-height: 1.5;
  }
  .content_font {
    font-size: 18px;
    color: #555;
    margin: 10px 0;
    line-height: 1.6;
  }
}
</style>
