<template>
  <div class="jimu-widget jimu-tool-widget box">
    <div>
      <TitleBar
        class="right-title jimu-widget-title-ui"
        :title="'秸秆还田离田情况对比'"
      />
      <Contrast paramKey="return" :code="code" minYear="2018" :year="year" />
    </div>

    <chart-table />
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
import TitleBar from "@/app/Components/TitleBar/";
import Contrast from "../components/Contrast";

import ChartTable from "../all/components/chartTable/index.vue";

export default {
  name: "SpeciesNumAnalysisWidget",

  mixins: [BaseWidget],

  components: { TitleBar, Contrast, ChartTable },

  data() {
    return {
      year: "2018",
      code: "45",
    };
  },

  mounted() {
    this.bus.$on(
      this.$events.admin.CHANGE_ADMIN,
      this.adminFilterUpdateHandler
    );
    this.bus.$on("RegionCHART_UPDATE", this.setFilter);
  },

  destroyed() {
    this.bus.$off(
      this.$events.admin.CHANGE_ADMIN,
      this.adminFilterUpdateHandler
    );
    this.bus.$off("RegionCHART_UPDATE", this.setFilter);
  },

  methods: {
    setFilter(e) {
      let filters = e.filters;
      let date = "2018";
      let dateFilter = filters.find((item) => item[1] === "date");
      if (dateFilter) date = dateFilter[2];
      this.year = date;
    },
    adminFilterUpdateHandler(e) {
      if (e) {
        this.code = e.code;
      } else {
        this.code = "45";
      }
    },
  },
};
</script>

<style lang="less" scoped>
.box {
  overflow: scroll;
}
.main-chart {
  width: 100%;
  height: 400px;
}
.pie-chart {
  width: 100%;
  height: 400px;
}
.slot-title {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 10px 0;
}
.toggleTable {
  cursor: pointer;
  height: 20px;
  width: 20px;
  .icon {
    font-size: 20px;
    font-weight: 800;
  }
}
.chart-wrap {
  width: 100%;

  // overflow-y: scroll;
}

.table-title {
  text-align: center;
  font-size: 18px;
  font-weight: 700;
  color: rgba(0, 0, 0, 0.8);
  margin-bottom: 10px;
}
// /deep/.ant-table td {
//   white-space: nowrap;
// }
// /deep/.ant-table th {
//   white-space: nowrap;
// }
</style>
