<template>
  <div class="jimu-widget jimu-tool-widget box">
    <div>
      <TitleBar
        class="right-title jimu-widget-title-ui"
        :title="'产生情况对比'"
      />
      <Contrast :paramKey="'output'" :code="code" minYear="2018" :year="year" />
    </div>

    <chart-table />
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";

import TitleBar from "@/app/Components/TitleBar/";
import Contrast from "../components/Contrast";

import ChartTable from "../all/components/chartTable/index.vue";
export default {
  name: "SpeciesNumAnalysisWidget",

  mixins: [BaseWidget],

  components: { ChartTable, TitleBar, Contrast },

  data() {
    return {
      year: "2018",
      code: "45",
    };
  },
  mounted() {
    this.bus.$on(
      this.$events.admin.CHANGE_ADMIN,
      this.adminFilterUpdateHandler
    );
    this.bus.$on("RegionCHART_UPDATE", this.setFilter);
  },

  destroyed() {
    this.bus.$off(
      this.$events.admin.CHANGE_ADMIN,
      this.adminFilterUpdateHandler
    );
    this.bus.$off("RegionCHART_UPDATE", this.setFilter);
  },

  methods: {
    setFilter(e) {
      let filters = e.filters;
      let date = "2018";

      let dateFilter = filters.find((item) => item[1] === "date");

      if (dateFilter) date = dateFilter[2];

      this.year = date;
    },
    adminFilterUpdateHandler(e) {
      if (e) {
        this.code = e.code;
      } else {
        this.code = "45";
      }
    },
  },
};
</script>

<style lang="less" scoped>
.box {
  overflow: scroll;
}
</style>
