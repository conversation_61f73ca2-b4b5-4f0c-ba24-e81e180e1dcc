const xAxisBar = {
  title: {
    text: "",
    x: "center",
    textStyle: {
      fontSize: 18,
    },
  },
  tooltip: {
    trigger: "item",
    formatter: "合计{b}万吨 占比{c}%",
  },
  grid: {
    left: "3%",
    right: "20%",
    bottom: "3%",
    containLabel: true,
  },
  xAxis: {
    type: "value",
    position: "top",
  },
  yAxis: {
    type: "category",
    data: [],
    axisTick: {
      show: false,
    },
    axisLabel: {
      width: 50,
      overflow: "break",
    },
    axisLine: {
      show: false,
    },
    inverse: true,
  },
  series: [
    {
      type: "bar",
      data: [],
      label: {
        show: true,
        position: "right",
        formatter: "{b}   ({c}%)",
      },
    },
  ],
};

const xAxisindexBar = {
  title: {
    text: "",
    x: "center",
    textStyle: {
      fontSize: 18,
    },
  },
  tooltip: {
    trigger: "item",
    formatter: "{b} :{c}",
  },
  grid: {
    left: "3%",
    right: "20%",
    bottom: "3%",
    containLabel: true,
  },
  xAxis: {
    type: "value",
    position: "top",
  },
  yAxis: {
    inverse: true,
    type: "category",
    data: [],
    axisTick: {
      show: false,
    },
    axisLabel: {
      width: 50,
      overflow: "break",
    },
    axisLine: {
      show: false,
    },
  },
  series: [
    {
      type: "bar",
      data: [],
      label: {
        show: true,
        position: "right",
        formatter: "{c}",
      },
    },
  ],
};

const yAxisColorBar = {
  title: {
    text: "",
    x: "center",
    textStyle: {
      fontSize: 24,
    },
  },
  tooltip: {
    trigger: "axis",
    formatter: "{b} {c}",
  },
  xAxis: {
    type: "category",
    data: [],
    axisLabel: {
      interval: 0,
    },
    axisTick: {
      show: false,
    },
    axisLine: {
      show: false,
    },
  },
  yAxis: {
    type: "value",
  },
  series: [
    {
      data: [],
      type: "bar",
      barWidth: "30px",
      itemStyle: {
        normal: {
          color: function (params) {
            const color = [
              "#5470c6",
              "#91cc75",
              "#fac858",
              "#ee6666",
              "#73c0de",
              "#3ba272",
              "#fc8452",
              "#9a60b4",
              "#ea7ccc",
            ];
            return color[params.dataIndex % color.length];
          },
        },
      },
    },
  ],
};

const pieEcharts = {
  title: {
    text: "",
    x: "center",
  },
  tooltip: {
    trigger: "item",
    formatter: function (params) {
      return (
        params.data.name +
        ":" +
        params.data.value +
        "万吨\n占比:" +
        params.percent +
        "%"
      );
    },
  },
  series: [
    {
      type: "pie",
      // radius: '80%',
      radius: ["40%", "70%"],
      data: [],
      label: {
        formatter: function (params) {
          params.data.label.color = params.color;
          return (
            params.data.name +
            ":" +
            params.data.value +
            "万吨\n占比:" +
            params.percent +
            "%"
          );
        },
      },
    },
  ],
};

export default {
  columns: {
    秸秆产生量: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "秸秆产生量(吨)",
        dataIndex: "straw_output",
        key: "3",
        align: "center",
        customRender: (text) => `${parseFloat(text).toFixed(2)}`,
      },
      {
        title: "秸秆产生量占比(%)",
        dataIndex: "straw_output_rate",
        key: "4",
        align: "center",
        customRender: (text) => `${parseFloat(text).toFixed(2) + "%"}`,
      },
    ],
    秸秆可收集量: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "秸秆可收集量(吨)",
        dataIndex: "straw_collect",
        key: "3",
        align: "center",
        customRender: (text, record, index) => `${parseFloat(text).toFixed(2)}`,
      },
      {
        title: "秸秆可收集量占比(%)",
        dataIndex: "straw_collect_rate",
        key: "4",
        align: "center",
        customRender: (text, record, index) =>
          `${parseFloat(text).toFixed(2) + "%"}`,
      },
    ],
    播种面积: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "播种面积(亩)",
        dataIndex: "crop_sown_area",
        key: "3",
        align: "center",
        customRender: (text, record, index) => `${parseFloat(text).toFixed(2)}`,
      },
    ],
    粮食产量: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "粮食产量(吨)",
        dataIndex: "crop_output",
        key: "3",
        align: "center",
        customRender: (text, record, index) => `${parseFloat(text).toFixed(2)}`,
      },
    ],

    秸秆综合利用率: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "秸秆综合利用量(吨)",
        dataIndex: "straw_use_scale",
        key: "3",
        align: "center",
        customRender: (text, record, index) => `${parseFloat(text).toFixed(2)}`,
      },
      {
        title: "秸秆综合利用率(%)",
        dataIndex: "straw_use_rate",
        key: "4",
        align: "center",
        customRender: (text, record, index) =>
          `${parseFloat(text).toFixed(2) + "%"}`,
      },
    ],
    五化利用量: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "肥料化(吨)",
        dataIndex: "flh_use",
        key: "3",
        align: "center",
        customRender: (text, record, index) => `${parseFloat(text).toFixed(2)}`,
      },
      {
        title: "饲料化(吨)",
        dataIndex: "slh_use",
        key: "4",
        align: "center",
        customRender: (text, record, index) => `${parseFloat(text).toFixed(2)}`,
      },
      {
        title: "燃料化(吨)",
        dataIndex: "rlh_use",
        key: "5",
        align: "center",
        customRender: (text, record, index) => `${parseFloat(text).toFixed(2)}`,
      },
      {
        title: "基料化(吨)",
        dataIndex: "jlh_use",
        key: "6",
        align: "center",
        customRender: (text, record, index) => `${parseFloat(text).toFixed(2)}`,
      },
      {
        title: "原料化(吨)",
        dataIndex: "ylh_use",
        key: "7",
        align: "center",
        customRender: (text, record, index) => `${parseFloat(text).toFixed(2)}`,
      },
    ],
    肥料化利用量: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "肥料化(吨)",
        dataIndex: "flh_use",
        key: "3",
        align: "center",
        customRender: (text, record, index) => `${parseFloat(text).toFixed(2)}`,
      },
    ],
    饲料化利用量: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "饲料化(吨)",
        dataIndex: "slh_use",
        key: "4",
        align: "center",
        customRender: (text, record, index) => `${parseFloat(text).toFixed(2)}`,
      },
    ],
    基料化利用量: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "基料化(吨)",
        dataIndex: "jlh_use",
        key: "6",
        align: "center",
        customRender: (text, record, index) => `${parseFloat(text).toFixed(2)}`,
      },
    ],
    原料化利用量: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "原料化(吨)",
        dataIndex: "ylh_use",
        key: "7",
        align: "center",
        customRender: (text, record, index) => `${parseFloat(text).toFixed(2)}`,
      },
    ],
    燃料化利用量: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "燃料化(吨)",
        dataIndex: "rlh_use",
        key: "5",
        align: "center",
        customRender: (text, record, index) => `${parseFloat(text).toFixed(2)}`,
      },
    ],
    综合利用指数: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "综合利用指数",
        dataIndex: "zhly_rate",
        key: "3",
        align: "center",
        customRender: (text, record, index) => `${parseFloat(text).toFixed(2)}`,
      },
    ],
    产业化利用指数: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "产业化利用指数",
        dataIndex: "cyhly_rate",
        key: "3",
        align: "center",
        customRender: (text, record, index) => `${parseFloat(text).toFixed(2)}`,
      },
    ],
    离田后五化利用及占比: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "离田利用量(吨)",
        dataIndex: "ltly_scale",
        key: "3",
        align: "center",
        customRender: (text, record, index) => `${parseFloat(text).toFixed(2)}`,
      },
      {
        title: "肥料化(离田)利用量(吨)",
        dataIndex: "lt_flhlyl",
        key: "4",
        align: "center",
        customRender: (text, record, index) => `${parseFloat(text).toFixed(2)}`,
      },
      {
        title: "占离田利用量比例(%)",
        dataIndex: "lt_flhlyl_rate",
        key: "4",
        align: "center",
        customRender: (text, record, index) => `${parseFloat(text).toFixed(2)}`,
      },
    ],
    秸秆离田利用量: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "秸秆离田利用量(吨)",
        dataIndex: "ltly_scale",
        key: "3",
        align: "center",
        customRender: (text, record, index) => `${parseFloat(text).toFixed(2)}`,
      },
    ],
    秸秆离田利用量展开: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "秸秆离田利用量(吨)",
        dataIndex: "ltly_scale",
        key: "3",
        align: "center",
        customRender: (text, record, index) => `${parseFloat(text).toFixed(2)}`,
      },
      {
        title: "农户分散利用量(吨)",
        dataIndex: "nhfsly_scale",
        key: "4",
        align: "center",
        customRender: (text, record, index) => `${parseFloat(text).toFixed(2)}`,
      },
      {
        title: "",
        rate: ["nhfsly_scale", "scztly_scale"],
        dataIndex: "rate",
        key: "5",
        align: "center",
        scopedSlots: { customRender: "rate" },
      },
      // { title: 'rate', dataIndex: 'rate', key: '5', align: 'center',scopedSlots: { customRender: (text, record, index) =>`${(parseFloat(record.nhfsly_scale) / parseFloat(parseFloat(record.nhfsly_scale)+parseFloat(record.scztly_scale))).toFixed(2)}` },},
      {
        title: "市场规模化利用量(吨)",
        dataIndex: "scztly_scale",
        key: "6",
        align: "center",
        customRender: (text, record, index) => `${parseFloat(text).toFixed(2)}`,
      },
    ],
    市场规模化主体利用量: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "市场规模化主体利用量(吨)",
        dataIndex: "scztly_scale",
        key: "3",
        align: "center",
        customRender: (text, record, index) => `${parseFloat(text).toFixed(2)}`,
      },
      {
        title: "占秸秆离田利用量比例(%)",
        dataIndex: "straw_sc_rate",
        key: "4",
        align: "center",
        customRender: (text, record, index) =>
          `${parseFloat(text).toFixed(2) + "%"}`,
      },
      // { title: '占比', dataIndex: '', key: '4', align: 'center', customRender: ( text, record, index ) => `${ parseFloat( text ).toFixed( 2 ) + '%' }` }
    ],
    农户分散离田利用量: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "农户分散离田利用量(吨)",
        dataIndex: "nhfsly_scale",
        key: "3",
        align: "center",
        customRender: (text, record, index) => `${parseFloat(text).toFixed(2)}`,
      },
      // { title: '占比', dataIndex: '', key: '4', align: 'center', customRender: ( text, record, index ) => `${ parseFloat( text ).toFixed( 2 ) + '%' }` }
    ],
    秸秆直接还田量: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "秸秆直接还田量(吨)",
        dataIndex: "zjht_scale",
        key: "3",
        align: "center",
        customRender: (text, record, index) => `${parseFloat(text).toFixed(2)}`,
      },
    ],
    秸秆直接还田率: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "秸秆直接还田率(%)",
        dataIndex: "zjht_rate",
        key: "4",
        align: "center",
        customRender: (text, record, index) =>
          `${parseFloat(text).toFixed(2) + "%"}`,
      },
    ],
  },
  option: {
    秸秆产生量: xAxisBar,
    秸秆可收集量: xAxisBar,
    播种面积: xAxisindexBar,
    粮食产量: xAxisindexBar,

    秸秆综合利用率: xAxisBar,
    五化利用量: xAxisindexBar,
    肥料化利用量: xAxisindexBar,
    饲料化利用量: xAxisindexBar,
    基料化利用量: xAxisindexBar,
    原料化利用量: xAxisindexBar,
    燃料化利用量: xAxisindexBar,
    综合利用指数: xAxisindexBar,
    产业化利用指数: xAxisindexBar,
    离田后五化利用及占比: xAxisindexBar,

    秸秆离田利用量: xAxisindexBar,
    市场规模化主体利用量: xAxisindexBar,
    农户分散离田利用量: xAxisindexBar,
    秸秆直接还田量: xAxisindexBar,
    秸秆直接还田率: xAxisindexBar,
  },
};
