<template>
  <div>
    <div class="slot-title">
      <div class="jimu-widget-title-ui">{{ info?.title }}</div>
    </div>
    <Chart v-if="lhpieChartShow" class="pie-chart" :options="pieConfig1" />
    <Chart v-if="wlhpieChartShow" class="pie-chart" :options="pieConfig" />

    <div class="chart-wrap" v-if="echartConfig">
      <div class="toggleTable" @click="toggleTable = !toggleTable">
        <span v-show="toggleTable" class="gt-icon gt-icon-table icon"></span>
        <span v-show="!toggleTable" class="gt-icon gt-icon-echarts icon"></span>
      </div>
      <Chart v-if="toggleTable" :options="echartConfig" style="height: 700px" />
      <div v-if="!toggleTable" class="table-title">
        {{ year + "年各类作物" + info?.title }}
      </div>
      <a-table
        v-if="!toggleTable"
        :scroll="{ x: tableWidth }"
        :columns="info.table"
        :data-source="tableData"
        :pagination="false"
        class="responsive-table"
      />
    </div>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
import Chart from "@/components/Chart/index.vue";
import config from "./config";
import _ from "lodash";

export default {
  name: "ChartTable",
  mixins: [BaseWidget],
  components: { Chart },
  data() {
    return {
      info: config[this.field],
      crop: "合计",
      year: "2018",
      code: "45",
      cityName: "广西",
      lastYear: null,
      data: null,
      echartConfig: null,
      toggleTable: true,
      chartHeight: null,
      tableConfig: [],
      tableData: [],

      pieConfig1: {},
      pieConfig: {},

      wlhpieChartShow: false,
      lhpieChartShow: false,
    };
  },

  computed: {
    viewportHeight() {
      return window.innerHeight / 100; // 获取1vh对应的像素数
    },
    tableWidth() {
      if (this.info && this.info.table) {
        return this.info.table.reduce((total, item) => {
          if (item.width) {
            return total + item.width;
          }
        }, 0);
      }
      return 0;
    },
  },
  mounted() {
    this.bus.$on(
      this.$events.admin.CHANGE_ADMIN,
      this.adminFilterUpdateHandler
    );
    this.bus.$on("RegionCHART_UPDATE", this.setFilter);
  },

  configLoaded() {
    this.isLoad = true;
    this.info = _.cloneDeep(config[this.field]);
    this.getData();
  },

  methods: {
    setFilter(e) {
      this.field = e.field;
      let filters = e.filters;
      let date = "2018";
      let crop = "合计";
      let dateFilter = filters.find((item) => item[1] === "date");
      let cropFilter = filters.find((item) => item[1] === "crop");
      if (dateFilter) date = dateFilter[2];
      if (cropFilter) crop = cropFilter[2];
      this.year = date;
      this.crop = crop;
      this.info = _.cloneDeep(config[this.field]);
      this.getData();
    },
    adminFilterUpdateHandler(e) {
      if (e) {
        this.adminData = e;
        this.adminCode = e.code;
        this.code = e.code;
        this.level = e.level;
        this.cityName = e.name;
      } else {
        this.adminData = null;
        this.adminCode = "45";
        this.code = "45";
        this.level = 0;
        this.cityName = "广西";
      }
      this.getData();
    },
    async getData() {
      let param = {
        format: "json",
        returnGeometry: false,
        orderField: this.field,
        orderType: "DESC",
        filter: ["all", ["=", "qydm", this.code], ["=", "date", this.year]],
      };
      let data = await this.$apis.feature.query("straw_data_gx", param);
      data = data
        .filter((item) => item.crop !== "合计")
        .concat(data.filter((item) => item.crop === "合计"));
      this.data = data;
      this.getOption();
    },

    getOption() {
      let { chart, chartConfig } = this.info || {};
      let { data } = this;
      let dataList = [];
      let nameList = [];
      this.tableData = [];
      const newtableData = [];

      if (
        this.field === "flh_use" ||
        this.field === "slh_use" ||
        this.field === "jlh_use" ||
        this.field === "ylh_use" ||
        this.field === "rlh_use"
      ) {
        this.countRate(data);
      }

      data.forEach((item) => {
        if (item.crop !== "合计") {
          let value = item[this.field];
          if (this.info?.division) {
            value = this.$utils.number.divisor(
              value,
              this.info.division,
              this.info.decimals
            );
          }
          if (chartConfig?.name) {
            dataList.push({
              value: parseFloat(value),
              name: item[chartConfig.name],
            });
          } else {
            dataList.push(parseFloat(value));
          }
          newtableData.push(item);
          nameList.push(item.crop);
        }
      });

      // 处理 万吨、万亩
      newtableData.forEach((item) => {
        Object.entries(
          new Map(
            this.info.table
              .filter((item) => item.division)
              .map((item) => [item.dataIndex, item.division])
          )
        ).forEach(([dataIndex, division]) => {
          if (item[dataIndex] != null) {
            item[dataIndex] = (item[dataIndex] / division).toFixed(2);
          }
        });
      });

      // 按照指定的标签排序
      this.tableData = this.sortTable(newtableData);

      // 展示饼状图
      this.showPie();

      chart.tooltip.formatter = chartConfig.tooltip;
      chart.series[0].label.formatter = chartConfig.series_label;
      chart.yAxis.data = nameList;
      chart.series[0].data = dataList;

      this.echartConfig = chart;
      this.echartConfig.title.text = `${this.year}年各类作物${this.info.title}`;
    },
    countRate(data) {
      let totals = {
        flh_use_total: 0,
        slh_use_total: 0,
        jlh_use_total: 0,
        ylh_use_total: 0,
        rlh_use_total: 0,
      };

      // 计算总和
      data.forEach((item) => {
        totals.flh_use_total += item.flh_use || 0;
        totals.slh_use_total += item.slh_use || 0;
        totals.jlh_use_total += item.jlh_use || 0;
        totals.ylh_use_total += item.ylh_use || 0;
        totals.rlh_use_total += item.rlh_use || 0;
      });

      // 计算百分比
      data.forEach((item) => {
        item.flh_use_rate = totals.flh_use_total
          ? parseFloat(((item.flh_use / totals.flh_use_total) * 100).toFixed(2))
          : 0;
        item.slh_use_rate = totals.slh_use_total
          ? parseFloat(((item.slh_use / totals.slh_use_total) * 100).toFixed(2))
          : 0;
        item.jlh_use_rate = totals.jlh_use_total
          ? parseFloat(((item.jlh_use / totals.jlh_use_total) * 100).toFixed(2))
          : 0;
        item.ylh_use_rate = totals.ylh_use_total
          ? parseFloat(((item.ylh_use / totals.ylh_use_total) * 100).toFixed(2))
          : 0;
        item.rlh_use_rate = totals.rlh_use_total
          ? parseFloat(((item.rlh_use / totals.rlh_use_total) * 100).toFixed(2))
          : 0;
      });

      return data;
    },
    // 表格标签排序
    sortTable(data) {
      const order = [
        "早稻",
        "中稻和一季晚稻",
        "双季晚稻",
        "小麦",
        "玉米",
        "马铃薯",
        "甘薯",
        "木薯",
        "花生",
        "油菜",
        "大豆",
        "棉花",
        "甘蔗",
        "其他",
      ];
      const orderMap = new Map(order.map((item, index) => [item, index]));
      return data.sort((a, b) => {
        const indexA = orderMap.get(a.crop) ?? order.length;
        const indexB = orderMap.get(b.crop) ?? order.length;
        return indexA - indexB;
      });
    },
    // 展示饼状图
    showPie() {
      const wlhAry = [
        "wlh_use_total",
        "flh_use",
        "slh_use",
        "jlh_use",
        "ylh_use",
        "rlh_use",
        "zhly_rate",
      ];
      const lhAry = ["flh_use", "slh_use", "jlh_use", "ylh_use", "rlh_use"];

      this.wlhpieChartShow = wlhAry.includes(this.field);
      this.lhpieChartShow = lhAry.includes(this.field);

      if (this.wlhpieChartShow) this.getwlhlyldata();
      if (this.lhpieChartShow) this.getlhlyldata();
    },
    getwlhlyldata() {
      let data = this.data.find((item) => item.crop == this.crop);
      let rlh_use = this.$utils.number.divisor(data.rlh_use, 10000, 2);
      let flh_use = this.$utils.number.divisor(data.flh_use, 10000, 2);
      let ylh_use = this.$utils.number.divisor(data.ylh_use, 10000, 2);
      let jlh_use = this.$utils.number.divisor(data.jlh_use, 10000, 2);
      let slh_use = this.$utils.number.divisor(data.slh_use, 10000, 2);
      let datalist = [
        {
          value: flh_use,
          name: "肥料化",
          label: { color: "" },
        },
        {
          value: slh_use,
          name: "饲料化",
          label: { color: "" },
        },
        {
          value: rlh_use,
          name: "燃料化",
          label: { color: "" },
        },
        {
          value: ylh_use,
          name: "原料化",
          label: { color: "" },
        },
        {
          value: jlh_use,
          name: "基料化",
          label: { color: "" },
        },
      ];
      let title = "";
      if (this.crop === "合计") {
        title =
          this.year +
          this.cityName +
          this.info.title +
          "\n" +
          "（在五化利用总量中占比情况）";
      } else {
        title =
          this.year +
          this.cityName +
          data.crop +
          this.info.title +
          "\n" +
          "（在五化利用总量中占比情况）";
      }
      this.updatePieConfig(title, datalist);
    },
    getlhlyldata() {
      let data = this.data.find((item) => item.crop == this.crop);
      let lt_flhlyl_rate = data.lt_flhlyl_rate;
      let lt_slhlyl_rate = data.lt_slhlyl_rate;
      let lt_ylhlyl_rate = data.lt_ylhlyl_rate;
      let lt_jlhlyl_rate = data.lt_jlhlyl_rate;
      let lt_rlhlyl_rate = data.lt_rlhlyl_rate;
      let datalist = [
        {
          value: lt_flhlyl_rate,
          name: "肥料化",
          label: { color: "" },
        },
        {
          value: lt_slhlyl_rate,
          name: "饲料化",
          label: { color: "" },
        },
        {
          value: lt_rlhlyl_rate,
          name: "燃料化",
          label: { color: "" },
        },
        {
          value: lt_ylhlyl_rate,
          name: "原料化",
          label: { color: "" },
        },
        {
          value: lt_jlhlyl_rate,
          name: "基料化",
          label: { color: "" },
        },
      ];

      let title = "";
      if (this.crop === "合计") {
        title =
          this.year +
          this.cityName +
          this.info.title +
          "\n" +
          "（在秸秆离田利用量中占比情况）";
      } else {
        title =
          this.year +
          this.cityName +
          data.crop +
          this.info.title +
          "\n" +
          "（在秸秆离田利用量中占比情况）";
      }

      this.updateLtlPieConfig(title, datalist);
    },
    updateLtlPieConfig(title, data) {
      this.pieConfig1 = {
        title: {
          text: title,
          x: "center",
        },
        tooltip: {
          trigger: "item",
          formatter: function (params) {
            return params.data.name + ":" + params.percent + "%";
          },
        },
        series: [
          {
            type: "pie",
            color: ["#ffd900", "#91CC75", "#EE6666", "#d0826c", "#5470C6"],
            radius: ["40%", "70%"],
            data,
            label: {
              formatter: function (params) {
                params.data.label.color = params.color;
                return params.data.name + ":" + params.percent + "%";
              },
            },
          },
        ],
      };
    },
    updatePieConfig(title, data) {
      this.pieConfig = {
        title: {
          text: title,
          x: "center",
          // 设置下边距
          padding: [0, 0, 50, 0],
          top: 0,
          bottom: 30,
        },

        tooltip: {
          trigger: "item",
          formatter: function (params) {
            return (
              params.data.name +
              ":" +
              params.data.value +
              "万吨\n占比:" +
              params.percent +
              "%"
            );
          },
        },
        series: [
          {
            type: "pie",
            // radius: '80%',
            radius: ["40%", "70%"],
            color: ["#ffd900", "#91CC75", "#EE6666", "#d0826c", "#5470C6"],
            data,
            label: {
              formatter: function (params) {
                params.data.label.color = params.color;
                return (
                  params.data.name +
                  ":" +
                  params.data.value +
                  "万吨\n占比:" +
                  params.percent +
                  "%"
                );
              },
            },
          },
        ],
      };
    },
  },
};
</script>

<style lang="less" scoped>
.responsive-table {
  overflow-x: auto;
  width: 100%;
}
.a-table {
  min-width: 1000px; /* 设置最小宽度以适应所有列 */
}
.main-chart {
  width: 100%;
  // height: 400px;
}
.pie-chart {
  width: 100%;
  height: 400px;
}
.slot-title {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 10px 0;
}

.chart-wrap {
  display: flex;
  flex-direction: column;
  width: 100%;
  position: relative;
  .toggleTable {
    cursor: pointer;
    position: absolute;
    top: 0px;
    right: 10px;
    z-index: 9999;

    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
    .icon {
      font-size: 20px;
    }
  }
}

.table-title {
  text-align: center;
  font-size: 18px;
  font-weight: 700;
  color: rgba(0, 0, 0, 0.8);
  margin-bottom: 10px;
}
</style>
