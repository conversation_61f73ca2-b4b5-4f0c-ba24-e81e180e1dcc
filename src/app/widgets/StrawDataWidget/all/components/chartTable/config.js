// 横向柱状图
const HorizonBar = {
  title: {
    text: "",
    x: "center",
    textStyle: {
      fontSize: 18,
    },
  },
  tooltip: {
    trigger: "item",
    formatter: "",
  },
  grid: {
    left: "3%",
    right: "20%",
    bottom: "3%",
    containLabel: true,
  },
  xAxis: {
    type: "value",
    position: "top",
  },
  yAxis: {
    type: "category",
    data: [],
    axisTick: {
      show: false,
    },
    axisLabel: {
      width: 50,
      overflow: "break",
    },
    axisLine: {
      show: false,
    },
    inverse: true,
  },
  series: [
    {
      type: "bar",
      data: [],
      label: {
        show: true,
        position: "right",
        formatter: "{b}   ({c}%)",
      },
    },
  ],
};

export default {
  // 秸秆产生情况
  straw_output: {
    title: "秸秆产生量",
    division: 10000,
    decimals: 2,
    table: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "秸秆产生量(吨)",
        dataIndex: "straw_output",
        key: "3",
        align: "center",
        customRender: (text) => `${parseFloat(text).toFixed(2)}`,
      },
      {
        title: "秸秆产生量占比(%)",
        dataIndex: "straw_output_rate",
        key: "4",
        align: "center",
        customRender: (text) => `${parseFloat(text).toFixed(2) + "%"}`,
      },
    ],
    chart: HorizonBar,
    chartConfig: {
      name: "straw_output_rate",
      tooltip: "合计{c}万吨 占比{b}%",
      series_label: "{c} ({b}%)",
    },
  },
  straw_collect: {
    title: "秸秆可收集量",
    division: 10000,
    decimals: 2,
    table: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "秸秆可收集量(吨)",
        dataIndex: "straw_collect",
        key: "3",
        align: "center",
        customRender: (value) => `${parseFloat(value).toFixed(2)}`,
      },
      {
        title: "秸秆可收集量占比(%)",
        dataIndex: "straw_collect_rate",
        key: "4",
        align: "center",
        customRender: (value) => `${parseFloat(value).toFixed(2) + "%"}`,
      },
    ],
    chart: HorizonBar,
    chartConfig: {
      name: "straw_collect_rate",
      tooltip: "合计{c}万吨 占比{b}%",
      series_label: "{c} ({b}%)",
    },
  },
  crop_output: {
    title: "粮食产量",
    division: 10000,
    decimals: 2,
    table: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "粮食产量(吨)",
        dataIndex: "crop_output",
        key: "3",
        align: "center",
        customRender: (value) => `${parseFloat(value).toFixed(2)}`,
      },
    ],
    chart: HorizonBar,
    chartConfig: {
      name: "crop_output",
      tooltip: "{c}万吨",
      series_label: "{c}",
    },
  },
  crop_sown_area: {
    title: "播种面积",
    division: 10000,
    decimals: 2,
    table: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "播种面积(亩)",
        dataIndex: "crop_sown_area",
        key: "3",
        align: "center",
        customRender: (value) => `${parseFloat(value).toFixed(2)}`,
      },
    ],
    chart: HorizonBar,
    chartConfig: {
      name: "crop_sown_area",
      tooltip: "{c}万亩",
      series_label: "{c}",
    },
  },

  // 秸秆利用情况
  straw_use_scale: {
    title: "秸秆利用量",
    division: 10000,
    decimals: 2,
    table: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "秸秆利用量(万吨)",
        dataIndex: "straw_use_scale",
        key: "3",
        align: "center",
        customRender: (value) => `${parseFloat(value).toFixed(2)}`,
      },
      {
        title: "秸秆利用率(%)",
        dataIndex: "straw_use_rate",
        key: "4",
        align: "center",
        customRender: (text) => `${parseFloat(text).toFixed(2) + "%"}`,
      },
    ],
    chart: HorizonBar,
    chartConfig: {
      name: "straw_use_rate",
      tooltip: "合计{c}万吨 占比{b}%",
      series_label: "{c} ({b}%)",
    },
  },
  straw_use_rate: {
    title: "秸秆利用率",
    decimals: 2,
    table: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "秸秆综合利用量(吨)",
        // division: 10000,
        dataIndex: "straw_use_scale",
        key: "3",
        align: "center",
        customRender: (value) => `${parseFloat(value).toFixed(2)}`,
      },
      {
        title: "秸秆综合利用率(%)",
        dataIndex: "straw_use_rate",
        key: "4",
        align: "center",
        customRender: (value) => `${parseFloat(value).toFixed(2) + "%"}`,
      },
    ],
    chart: HorizonBar,
    chartConfig: {
      name: "straw_use_scale",
      tooltip: function (params) {
        let a = (params.data.name / 10000).toFixed(2);
        return `合计${a}万吨 占比${params.data.value}%`;
      },
      series_label: function (params) {
        let a = (params.data.name / 10000).toFixed(2);
        return `${a} (${params.data.value}%)`;
      },
    },
  },
  wlh_use_total: {
    title: "五化利用量",
    division: 10000,
    decimals: 2,
    table: [
      {
        title: "作物种类",
        width: 120,
        dataIndex: "crop",
        key: "2",
        align: "center",
      },
      {
        title: "肥料化(吨)",
        dataIndex: "flh_use",
        key: "3",
        align: "center",
        width: 120,
        customRender: (value) => `${parseFloat(value).toFixed(2)}`,
      },
      {
        title: "饲料化(吨)",
        dataIndex: "slh_use",
        key: "4",
        align: "center",
        width: 120,
        customRender: (value) => `${parseFloat(value).toFixed(2)}`,
      },
      {
        title: "燃料化(吨)",
        dataIndex: "rlh_use",
        key: "5",
        align: "center",
        width: 120,
        customRender: (value) => `${parseFloat(value).toFixed(2)}`,
      },
      {
        title: "基料化(吨)",
        dataIndex: "jlh_use",
        key: "6",
        align: "center",
        width: 120,
        customRender: (value) => `${parseFloat(value).toFixed(2)}`,
      },
      {
        title: "原料化(吨)",
        dataIndex: "ylh_use",
        key: "7",
        align: "center",
        width: 120,
        customRender: (value) => `${parseFloat(value).toFixed(2)}`,
      },
    ],
    chart: HorizonBar,
    chartConfig: {
      tooltip: "{c}万吨",
      series_label: "{c}",
    },
  },
  flh_use: {
    title: "肥料化利用量",
    division: 10000,
    decimals: 2,
    table: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "肥料化(吨)",
        dataIndex: "flh_use",
        key: "3",
        align: "center",
        customRender: (value) => `${parseFloat(value).toFixed(2)}`,
      },
      {
        title: "肥料化占比(%)",
        dataIndex: "flh_use_rate",
        key: "4",
        align: "center",
        customRender: (value) => `${parseFloat(value).toFixed(2) + "%"}`,
      },
    ],
    chart: HorizonBar,
    chartConfig: {
      name: "flh_use_rate",
      tooltip: "{c}万吨 占比{b}%",
      series_label: "{c} ({b}%)",
    },
  },
  slh_use: {
    title: "饲料化利用量",
    division: 10000,
    decimals: 2,
    table: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "饲料化(吨)",
        dataIndex: "slh_use",
        key: "3",
        align: "center",
        customRender: (value) => `${parseFloat(value).toFixed(2)}`,
      },
      {
        title: "饲料化占比(%)",
        dataIndex: "slh_use_rate",
        key: "4",
        align: "center",
        customRender: (value) => `${parseFloat(value).toFixed(2) + "%"}`,
      },
    ],
    chart: HorizonBar,
    chartConfig: {
      name: "slh_use_rate",
      tooltip: "{c}万吨 占比{b}%",
      series_label: "{c} ({b}%)",
    },
  },
  jlh_use: {
    title: "基料化利用量",
    division: 10000,
    decimals: 2,
    table: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "基料化(吨)",
        dataIndex: "jlh_use",
        key: "3",
        align: "center",
        customRender: (value) => `${parseFloat(value).toFixed(2)}`,
      },
      {
        title: "基料化占比(%)",
        dataIndex: "jlh_use_rate",
        key: "4",
        align: "center",
        customRender: (value) => `${parseFloat(value).toFixed(2) + "%"}`,
      },
    ],
    chart: HorizonBar,
    chartConfig: {
      name: "jlh_use_rate",
      tooltip: "{c}万吨 占比{b}%",
      series_label: "{c} ({b}%)",
    },
  },
  ylh_use: {
    title: "原料化利用量",
    division: 10000,
    decimals: 2,
    table: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "原料化(吨)",
        dataIndex: "ylh_use",
        key: "3",
        align: "center",
        customRender: (value) => `${parseFloat(value).toFixed(2)}`,
      },
      {
        title: "原料化占比(%)",
        dataIndex: "ylh_use_rate",
        key: "4",
        align: "center",
        customRender: (value) => `${parseFloat(value).toFixed(2) + "%"}`,
      },
    ],
    chart: HorizonBar,
    chartConfig: {
      name: "ylh_use_rate",
      tooltip: "{c}万吨 占比{b}%",
      series_label: "{c} ({b}%)",
    },
  },
  rlh_use: {
    title: "燃料化利用量",
    division: 10000,
    decimals: 2,
    table: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "燃料化(吨)",
        dataIndex: "rlh_use",
        key: "3",
        align: "center",
        customRender: (value) => `${parseFloat(value).toFixed(2)}`,
      },
      {
        title: "燃料化占比(%)",
        dataIndex: "rlh_use_rate",
        key: "4",
        align: "center",
        customRender: (value) => `${parseFloat(value).toFixed(2) + "%"}`,
      },
    ],
    chart: HorizonBar,
    chartConfig: {
      name: "rlh_use_rate",
      tooltip: "{c}万吨 占比{b}%",
      series_label: "{c} ({b}%)",
    },
  },
  zhly_rate: {
    title: "离田后五化利用及占比",
    decimals: 2,
    table: [
      {
        title: "作物种类",
        width: 120,
        dataIndex: "crop",
        key: "2",
        align: "center",
      },
      {
        title: "离田利用量(吨)",
        width: 120,
        dataIndex: "ltly_scale",
        key: "3",
        align: "center",
        customRender: (value) => `${parseFloat(value).toFixed(2)}`,
      },
      {
        title: "肥料化(离田)利用量(吨)",
        width: 120,
        dataIndex: "lt_flhlyl",
        key: "4",
        align: "center",
        customRender: (value) => `${parseFloat(value).toFixed(2)}`,
      },
      {
        title: "占离田利用量比例(%)",
        width: 120,
        dataIndex: "lt_flhlyl_rate",
        key: "5",
        align: "center",
        customRender: (value) => `${parseFloat(value).toFixed(2)}`,
      },
    ],
    chart: HorizonBar,
    chartConfig: {
      name: "zhly_rate",
      tooltip: "{c}",
      series_label: "{c}",
    },
  },
  cyhly_rate: {
    title: "产业化利用指数",
    decimals: 2,
    table: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "产业化利用指数",
        dataIndex: "cyhly_rate",
        key: "3",
        align: "center",
        customRender: (value) => `${parseFloat(value).toFixed(2)}`,
      },
    ],
    chart: HorizonBar,
    chartConfig: {
      name: "cyhly_rate",
      tooltip: "{c}",
      series_label: "{c}",
    },
  },

  // 还田离田情况
  zjht_scale: {
    title: "秸秆直接还田量",
    division: 10000,
    decimals: 2,
    table: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "秸秆直接还田量(吨)",
        dataIndex: "zjht_scale",
        key: "3",
        align: "center",
        customRender: (value) => `${parseFloat(value).toFixed(2)}`,
      },
    ],
    chart: HorizonBar,
    chartConfig: {
      name: "cyhly_rate",
      tooltip: "{c}万吨",
      series_label: "{c}",
    },
  },
  zjht_rate: {
    title: "秸秆直接还田率",
    decimals: 2,
    table: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "秸秆直接还田率(%)",
        dataIndex: "zjht_rate",
        key: "3",
        align: "center",
        customRender: (value) => `${parseFloat(value).toFixed(2) + "%"}`,
      },
    ],
    chart: HorizonBar,
    chartConfig: {
      name: "cyhly_rate",
      tooltip: function (params) {
        return params.data.value.toFixed(2) + "%";
      },
      series_label: function (params) {
        return params.data.value.toFixed(2) + "%";
      },
    },
  },
  ltly_scale: {
    title: "秸秆离田利用量",
    division: 10000,
    decimals: 2,
    table: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "秸秆离田利用量(吨)",
        dataIndex: "ltly_scale",
        key: "3",
        align: "center",
        customRender: (value) => `${parseFloat(value).toFixed(2)}`,
      },
    ],
    chart: HorizonBar,
    chartConfig: {
      name: "cyhly_rate",
      tooltip: "{c}万吨",
      series_label: "{c}",
    },
  },
  ltly_rate: {
    title: "秸秆离田利用率",
    decimals: 2,
    table: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "秸秆离田利用率(%)",
        dataIndex: "ltly_rate",
        key: "3",
        align: "center",
        customRender: (value) => `${parseFloat(value).toFixed(2) + "%"}`,
      },
    ],
    chart: HorizonBar,
    chartConfig: {
      name: "ltly_rate",
      tooltip: function (params) {
        return parseFloat(params.data.value).toFixed(2) + "%";
      },
      series_label: function (params) {
        return parseFloat(params.data.value).toFixed(2) + "%";
      },
    },
  },
  scztly_scale: {
    title: "市场主体规模化利用量",
    division: 10000,
    decimals: 2,
    table: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "市场规模化主体利用量(吨)",
        dataIndex: "scztly_scale",
        key: "3",
        align: "center",
        customRender: (value) => `${parseFloat(value).toFixed(2)}`,
      },
      {
        title: "占秸秆离田利用量比例(%)",
        dataIndex: "straw_sc_rate",
        key: "4",
        align: "center",
        customRender: (value) => `${parseFloat(value).toFixed(2) + "%"}`,
      },
    ],
    chart: HorizonBar,
    chartConfig: {
      name: "straw_sc_rate",
      tooltip: "合计{c}万吨 占比{b}%",
      series_label: "{c} ({b}%)",
    },
  },
  nhfsly_scale: {
    title: "农户分散离田利用量",
    division: 10000,
    decimals: 2,
    table: [
      { title: "作物种类", dataIndex: "crop", key: "2", align: "center" },
      {
        title: "农户分散离田利用量(吨)",
        dataIndex: "nhfsly_scale",
        key: "3",
        align: "center",
        customRender: (value) => `${parseFloat(value).toFixed(2)}`,
      },
    ],
    chart: HorizonBar,
    chartConfig: {
      name: "nhfsly_scale",
      tooltip: "{c}万吨",
      series_label: "{c}",
    },
  },
};
