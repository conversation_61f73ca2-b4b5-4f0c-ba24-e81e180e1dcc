<template>
  <div class="jimu-widget jimu-tool-widget box">
    <div>
      <TitleBar class="right-title jimu-widget-title-ui" title="总体概况" />
      <Contrast :paramKey="'all'" :code="code" minYear="2018" :year="year" />
    </div>

    <chart-table style="margin-top: 15px"></chart-table>

    <div style="margin-top: 15px">
      <TitleBar class="right-title jimu-widget-title-ui" title="秸秆五化利用量" />
      <div class="pie-box"><Chart :options="Pieoption"></Chart></div>
    </div>

    <div class="right-bottom">
      <TitleBar title="市场主体数量（个）" />
      <Chart class="chart-box" :options="companyoption"></Chart>
    </div>

    <div class="right-top" style="margin-top: 15px">
      <TitleBar title="秸秆还田离田量构成（万吨）" />
      <Chart class="chart-box" :options="marketoption"></Chart>
    </div>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";
import Chart from "@/components/Chart/index.vue";
import TitleBar from "@/app/Components/TitleBar/";
import Contrast from "../components/Contrast";

import chartTable from "./components/chartTable/index.vue";

export default {
  name: "SpeciesNumAnalysisWidget",

  mixins: [BaseWidget],

  components: { Chart, TitleBar, Contrast, chartTable },

  data() {
    return {
      year: "2018",
      code: "45",
      lastYear: null,
      data: null,
      level: 0,
      adminField: ["PXZQDM", "CXZQDM", "FXZQDM", "TXZQDM"],
      pieData: [],
      Pieoption: {
        tooltip: {
          trigger: "item",
          formatter: function (params) {
            let value = params.data.value > 10000 ? (params.data.value / 10000).toFixed(2) + "万" : params.data.value;
            return (
              '<span style="color:' +
              params.data.color +
              ';">' +
              params.data.name +
              "利用量:<br>" +
              value +
              "吨<br>占比:" +
              params.percent +
              "%" +
              "</span>"
            );
          },
        },
        grid: {
          bottom: "20%",
          left: "2%",
          containLabel: true,
        },
        series: [
          {
            type: "pie",
            // radius: '90%',
            radius: ["40%", "70%"],
            color: ["#5470C6", "#91CC75", "#EE6666", "#d0826c", "#ffd900"],
            label: {
              fontSize: 12,
              formatter: function (params) {
                console.log("数据", params.data);
                let value = params.data.value > 10000 ? (params.data.value / 10000).toFixed(2) + "万" : params.data.value;
                return params.data.name + ":" + value + "吨\n占比:" + params.percent + "%";
              },
            },
            data: [],
          },
        ],
      },
      marketoption: {
        xAxis: {
          type: "category",
          data: [],
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
            onZero: false,
          },
          axisLabel: {
            interval: 0,
            textStyle: {
              color: "#002159",
              fontSize: 12,
              padding: 8,
              fontFamily: "PingFangSC-Semibold, PingFang SC",
            },
          },
        },
        yAxis: {
          type: "value",
        },
        grid: {
          top: "20%",
          left: "5%",
          right: "5%",
          bottom: "5%",
          containLabel: true,
        },
        legend: {
          data: ["市场化主体利用量", "农户分散利用量", "直接还田量"],
          top: "5%",
          textStyle: {
            color: "#002159",
            fontSize: 12,
            fontFamily: "PingFangSC-Semibold, PingFang SC",
          },
        },
        series: [
          {
            name: "市场化主体利用量",
            data: [],
            type: "bar",
            barWidth: 40,
            stack: "秸秆离田利用量",
            itemStyle: {
              color: "#5470c6",
            },

            label: {
              show: true,
              position: "inside",
              formatter: "{c}",
            },
          },
          {
            name: "农户分散利用量",
            data: [],
            type: "bar",
            barWidth: 40,
            stack: "秸秆离田利用量",
            itemStyle: {
              color: "#91cc75",
            },
            label: {
              show: true,
              position: "inside",
              formatter: "{c}",
            },
          },
          {
            name: "直接还田量",
            data: [],
            type: "bar",
            barWidth: 40,
            stack: "秸秆离田利用量",
            itemStyle: {
              color: "#ee6666",
            },
            label: {
              show: true,
              position: "top",
              formatter: "{c}",
            },
          },
        ],
      },
      companyoption: {
        legend: {
          data: ["利用量小于1000吨", "利用量大于1000吨(含1000吨)"],
        },
        tooltip: {},
        xAxis: {
          type: "category",
          data: [],
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            interval: 0,
            textStyle: {
              color: "#002159",
              fontSize: 12,
              padding: 8,
              fontFamily: "PingFangSC-Semibold, PingFang SC",
            },
          },
        },
        yAxis: {
          type: "value",
        },
        grid: {
          top: "10%",
          left: "5%",
          right: "5%",
          bottom: "5%",
          containLabel: true,
        },
        series: [],
      },

      type: "straw_output",
    };
  },
  mounted() {
    this.bus.$on(this.$events.admin.CHANGE_ADMIN, this.adminFilterUpdateHandler);
    this.bus.$on("RegionMapFilter", this.setFilter);
    this.bus.$on("RegionCHART_UPDATE", this.showType);
  },

  destroyed() {
    this.bus.$off(this.$events.admin.CHANGE_ADMIN, this.adminFilterUpdateHandler);
    this.bus.$off("RegionCHART_UPDATE", this.setFilter);
  },

  configLoaded() {
    this.isLoad = true;
    this.getData();
  },

  methods: {
    showType(e) {
      this.type = e.field;
    },
    setFilter(e) {
      let date = "2018";
      let dateFilter = e.find((item) => item.id === "date");
      if (dateFilter) date = dateFilter.value;
      this.year = date;
      this.getData();
    },
    adminFilterUpdateHandler(e) {
      if (e) {
        this.adminData = e;
        this.adminCode = e.code;
        this.code = e.code;
        this.level = e.level;
      } else {
        this.adminData = null;
        this.adminCode = "45";
        this.code = "45";
        this.level = 0;
      }
      this.getData();
    },

    async getData() {
      let param = {
        format: "json",
        returnGeometry: false,
        filter: ["all", ["=", "qydm", this.code], ["=", "date", this.year], ["=", "crop", "合计"]],
      };
      let data = await this.$apis.feature.query("straw_data_gx", param);

      let param1 = {
        format: "json",
        returnGeometry: false,
        orderField: this.chartField,
        orderType: "DESC",
        filter: ["all", ["=", this.adminField[this.level], this.code], ["=", "date", this.year]],
      };
      let data1 = await this.$apis.feature.query("straw_company_point_gx", param1);

      this.getPie(data);
      this.getMarket(data);

      this.setCompanyData(data1);
    },

    getPie(data) {
      //饼图五化数据处理
      let total = 0;
      const color = ["#ffd900", "#91CC75", "#EE6666", "#d0826c", "#5470C6", "#3ba272", "#fc8452", "#9a60b4", "#ea7ccc"];

      let list = [
        { value: "", key: "flh_use", name: "肥料化", color: "", percent: 0 },
        { value: "", key: "slh_use", name: "饲料化", color: "", percent: 0 },
        { value: "", key: "rlh_use", name: "燃料化", color: "", percent: 0 },
        { value: "", key: "ylh_use", name: "原料化", color: "", percent: 0 },
        { value: "", key: "jlh_use", name: "基料化", color: "", percent: 0 },
      ];
      list.forEach((item, i) => {
        item.value = data[0][item.key];
        item.color = color[i % color.length];
        item.label = {};
        item.label.color = item.color;
        total += parseFloat(data[0][item.key]);
      });
      list.forEach((item) => {
        item.percent = parseFloat((item.value / total).toFixed(4));
        item.percent = item.percent ? item.percent : 0;
      });
      console.log("list", list);
      this.$set(this.Pieoption.series[0], "data", list);
      this.pieData = list;
    },

    getMarket(data) {
      console.log("data", data);

      const marketData = {
        市场化主体利用量: parseFloat(data[0]["scztly_scale"] / 10000).toFixed(2),
        农户分散利用量: parseFloat(data[0]["nhfsly_scale"] / 10000).toFixed(2),
        直接还田量: parseFloat(data[0]["zjht_scale"] / 10000).toFixed(2),
      };

      this.marketoption.series[0].data = [marketData["市场化主体利用量"], null];
      this.marketoption.series[1].data = [marketData["农户分散利用量"], null];
      this.marketoption.series[2].data = [null, marketData["直接还田量"]];

      this.marketoption.xAxis.data = ["秸秆离田利用量", "直接还田量"];
    },

    setCompanyData(data) {
      console.log("市场主体数据", data);

      let list = [
        { key: "rlh", name: "燃料化" },
        { key: "ylh", name: "原料化" },
        { key: "slh", name: "饲料化" },
        { key: "jlh", name: "基料化" },
        { key: "flh", name: "肥料化" },
      ];
      let series = [
        {
          name: "利用量小于1000吨",
          type: "bar",
          stack: "total",
          barWidth: 40,
          emphasis: {
            focus: "series",
          },
          data: [0, 0, 0, 0, 0],
        },
        {
          name: "利用量大于1000吨(含1000吨)",
          type: "bar",
          stack: "total",
          barWidth: 40,
          emphasis: {
            focus: "series",
          },
          data: [0, 0, 0, 0, 0],
        },
        {
          name: "总量",
          type: "bar",
          barGap: "-100%",
          barWidth: 40,
          emphasis: {
            focus: "series",
          },
          data: [0, 0, 0, 0, 0],
          itemStyle: {
            color: "rgba(0,0,0,0)",
          },
          z: -1,
          label: {
            show: true,
            position: "top",
            formatter: "{c}",
          },
        },
      ];
      let namelist = [];
      list.forEach((item, index) => {
        namelist.push(item.name);
        let key = item.key;
        data.forEach((items) => {
          if (items[key] > 0) series[2].data[index] += 1;
          if (items[key] >= 1000) {
            series[1].data[index] += 1;
          } else if (items[key] > 0) {
            series[0].data[index] += 1;
          }
        });
      });

      this.companyoption.series = series;
      this.companyoption.xAxis.data = namelist;
    },
  },
};
</script>

<style lang="less" scoped>
.box {
  overflow: scroll;
}
.chart-box {
  // margin-top: 15px;
  width: 100%;
  height: 400px;
}
.pie-box {
  width: 100%;
  height: 400px;
}
.slot-title {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 10px 0;
}
.toggleTable {
  cursor: pointer;
  height: 20px;
  width: 20px;
  .icon {
    font-size: 20px;
    font-weight: 800;
  }
}
</style>
