import Vue from 'vue'

import App from '@/App.vue'
import router from '@/router'
import store from '@/store'
import '@/import'
import "@/assets/fonts/style.css";
import '@/assets/styles/common.less'

// import gt from 'gt-web-components'
// import 'gt-web-components/lib/index.css' // 引入css文件
// Vue.use(gt)

import 'gt-mapbox-gl-compare/dist/mapbox-gl-compare.css'


import events from '@/events'
import utils from '@/utils'
import constants from '@/constants'
import apis from '@/apis'

Vue.config.productionTip = false

Vue.use(events)
Vue.use(utils)
Vue.use(apis)
Vue.prototype.$constants = constants


new Vue({
  store,
  router,
  render: h => h(App),
}).$mount('#app')
