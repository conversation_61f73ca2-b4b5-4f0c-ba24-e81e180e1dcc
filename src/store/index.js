import Vue from 'vue'
import Vuex from 'vuex'
import constants from '../constants'

Vue.use( Vuex )

const state = {

  config: null,
  routes: null,
  // 全局范围共享数据
  data: {},
  // 模块范围数据共享
  moduleData: {},

  thisModule: '产地环境'

}

const mutations = {

  setConfig ( state, config ) {
    state.config = config
  },

  setModuleName ( state, config ) {
    state.thisModule = config
  },

  setRoutes ( state, routes ) {
    state.routes = routes
  },

  setMenu( state, menu ) {
    state.menu = menu
  },

  setGlobalData ( state, obj ) {
    if ( !obj || !obj.key ) return
    Vue.set( state.data, obj.key, obj.value )
  },

  setModuleData ( state, obj ) {
    if ( !obj || !obj.key || !obj.module ) return
    const moduleData = state.moduleData[obj.module] ? state.moduleData[obj.module] : {}
    Object.assign( moduleData, {
      [obj.key]: obj.value
    } )
    Vue.set( state.moduleData, obj.module, moduleData )
  },

  setModuleLayerFilter ( state, obj ) {
    if ( !obj || !obj.module ) return
    if ( !state.moduleData[obj.module] ) {
      Vue.set( state.moduleData, obj.module, {} )
    }
    if ( !state.moduleData[obj.module][constants.shareDataKey.LAYER_FILTER] ) {
      Vue.set( state.moduleData[obj.module], constants.shareDataKey.LAYER_FILTER, {} )
    }
    if ( !state.moduleData[obj.module][constants.shareDataKey.LAYER_FILTER][obj.layerId] ) {
      Vue.set( state.moduleData[obj.module][constants.shareDataKey.LAYER_FILTER], [obj.layerId], {} )
    }
    const moduleLayerData = state.moduleData[obj.module][constants.shareDataKey.LAYER_FILTER][obj.layerId]
    Object.assign( moduleLayerData, {
      [obj.key]: obj.value
    } )
    Vue.set( state.moduleData[obj.module][constants.shareDataKey.LAYER_FILTER], [obj.layerId], moduleLayerData )
  },

  setModuleLayerStyle ( state, obj ) {
    if ( !obj || !obj.module ) return
    if ( !state.moduleData[obj.module] ) {
      Vue.set( state.moduleData, obj.module, {} )
    }
    if ( !state.moduleData[obj.module][constants.shareDataKey.LAYER_STYLE] ) {
      Vue.set( state.moduleData[obj.module], constants.shareDataKey.LAYER_STYLE, {} )
    }
    if ( !state.moduleData[obj.module][constants.shareDataKey.LAYER_STYLE][obj.layerId] ) {
      Vue.set( state.moduleData[obj.module][constants.shareDataKey.LAYER_STYLE], [obj.layerId], {} )
    }
    const moduleLayerData = state.moduleData[obj.module][constants.shareDataKey.LAYER_STYLE][obj.layerId]
    Object.assign( moduleLayerData, {
      [obj.key]: obj.value
    } )
    Vue.set( state.moduleData[obj.module][constants.shareDataKey.LAYER_STYLE], [obj.layerId], moduleLayerData )
  }



}

const actions = {
  SET_DATA ( { commit }, param ) {
    commit( 'setGlobalData', param )
  }
}

export default new Vuex.Store( {
  state,
  mutations,
  actions
} )