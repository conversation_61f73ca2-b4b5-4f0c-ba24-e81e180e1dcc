<template>
  <div class="chart" ref="chart"></div>
</template>

<script>
import elementResizeDetectorMaker from 'element-resize-detector'

export default {
  name: 'Echarts2D',
  props: {
    options: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      chart: null,
      functionStack: [],
      canDo: false
    }
  },
  methods: {
    initChart() {
      this.chart = this.$echarts.init(this.$refs.chart)
      let erd = elementResizeDetectorMaker()
      erd.listenTo(this.$refs.chart, () => {
        this.chart.resize()
      })
      this.functionStack.forEach(e => {
        e.func(e.param)
      })
      this.canDo = true
    },
    reloadChart() {
      this.$nextTick(() => {
        if (this.canDo) {
          this.chart.setOption(this.options, true)
        } else {
          this.chart.setOption(this.options, true)
        }
      })
    }
  },
  watch: {
    options: {
      handler() {
        this.reloadChart()
      },
      deep: true
    }
  },
  async mounted() {
    await this.initChart()
    let that = this

    // 扩大echarts点击面的大小，即点击灰色面也可以触发
    that.chart.getZr().on('click', function (params) {
      const pointInPixel = [params.offsetX, params.offsetY]
      if (that.chart.containPixel('grid', pointInPixel)) {
        /*此处添加具体执行代码*/
        const pointInGrid = that.chart.convertFromPixel(
          { seriesIndex: 0 },
          pointInPixel
        )
        //X轴序号
        const xIndex = pointInGrid[0]

        //获取当前图表的option
        const op = that.chart.getOption()
        let name = ''
        let data
        //获得图表中我们想要的数据 判断数据在x轴还是y轴
        if (op.xAxis[0].type === 'category') {
          name = op.xAxis[0].data[xIndex]
          data=op.series.map(item=>{
            return {value:item.data[xIndex]}
          } )
        } else {
          name = op.yAxis[0].data[xIndex]
        }

        that.$emit('barClick', { name, data })
      }
    })

    this.reloadChart()
  }
}
</script>

<style scoped>
.chart {
  width: 100%;
  height: 100%;
}
</style>
