<template>
  <div id="jimu-popup-container">
    <div class="title" v-show="title">{{ title }}</div>
    <a-icon class="btn-popup-close" type="close" @click="closeHandler" />
    <keep-alive :max="5">
      <component
        :is="content.component"
        :data="content.data"
        :fields="content.fields"
        :popupConfig="popupConfig"
      />
    </keep-alive>
  </div>
</template>

<script>
import mapboxgl from "mapbox-gl";

import DefaultPopupContent from "./components/DefaultPopupContent";

export default {
  name: "Popup",

  components: {
    DefaultPopupContent,
  },

  props: {
    map: {
      type: Object,
    },
  },

  data() {
    return {
      title: null,
      content: {
        component: null,
        data: null,
      },
      popupConfig: null,
    };
  },

  mounted() {
    var div = window.document.getElementById("jimu-popup-container");
    this.popup = new mapboxgl.Popup({
      closeOnClick: false,
      closeButton: false,
      // maxWidth: "360",
    }).setDOMContent(div);
  },

  methods: {
    async show(lngLat, data, popup, component) {
      this.title = this._getPopupTitle(popup, data);
      const module = !component
        ? DefaultPopupContent
        : await this._loadModule(component);

      this.popupConfig = popup;
      this.content = {
        component: module,
        data,
        fields: popup.fields,
      };
      this.popup.setLngLat(lngLat).addTo(this.map);
      if (popup.maxWidth) {
        this.popup.setMaxWidth(popup.maxWidth);
      } else {
        this.popup.setMaxWidth("360px");
      }
      console.log(this.popup.getMaxWidth(), popup, "popup");
    },

    close() {
      if (this.popup.isOpen()) {
        this.popup.remove();
      }
    },
    _getPopupTitle(popup, props) {
      if (!popup.title || !props) return;
      // 正则匹配
      const match = popup.title.match(/\{(.+?)\}/g);
      // 如果没有匹配{xxx}则返回原始内容作为标题
      if (!match) return popup.title;
      // 对匹配的内容用props中的值替换
      let title = popup.title;
      match.forEach((m) => {
        const field = `${m}`.substr(1, m.length - 2);
        let value = props[field];
        value = value === undefined ? "" : value;
        title = title.replace(`${m}`, value);
      });
      return title;
    },

    closeHandler() {
      this.popup.remove();
      this.$emit("close");
    },

    // 加载模块
    async _loadModule(uri) {
      const module = await import(`@/${uri}`);
      return this.$utils.object.deepCloneObj(module.default);
    },
  },
};
</script>

<style lang="less" scoped>
#jimu-popup-container {
  min-width: 100px;
  // width: 350;
  width: 100%;

  .title {
    font-size: 21px;
    font-weight: bold;
    padding: 0 8px 4px 0;
  }

  .item {
    font-size: 17px;
    padding: 2px 0 2px 0;
  }

  .btn-popup-close {
    position: absolute;
    right: 6px;
    top: 6px;
  }
}
</style>
