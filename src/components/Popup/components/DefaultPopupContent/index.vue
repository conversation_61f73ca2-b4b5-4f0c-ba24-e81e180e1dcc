<template>
  <div id="jimu-popup-content">
    <!-- {{ data }}{{ fields }} -->
    <div class="item" v-for="(item, index) in fields" :key="index">
      <span class="label">{{ item.label }}</span> :
      <span class="value">{{ init(item, data) || '' }} {{ item.unit }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DefaultPopupContent',

  props: {
    data: {
      type: Object
    },
    fields: {
      type: Array
    }
  },

  data() {
    return {}
  },

  watch: {
    data() {
      // this.init()
    }
  },

  mounted() {
    // this.init()
  },

  methods: {
    init(item, data) {

      if (this.$utils.number.isPositiveInteger(item.decimals)) {
        const value = parseFloat(data[item.field])
        if (!isNaN(value)) {
          return value.toFixed(item.decimals)
        }
      }else{
        return data[item.field]
      }
    }
  }
}
</script>

<style lang="less" scoped>
#jimu-popup-content {
  .item {
    font-size: 17px;
    padding: 2px 0 2px 0;
  }
}
</style>
