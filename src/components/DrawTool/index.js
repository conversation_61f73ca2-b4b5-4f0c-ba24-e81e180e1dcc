import Vue from 'vue'
import { distance, rectangle, circle } from './helper'

const SHAPE_POINT = 'point'
const SHAPE_LINE = 'line'
const SHAPE_POLYGON = 'polygon'
const SHAPE_CIRCLE = 'circle'
const SHAPE_RECT = 'rectangle'
const SHAPE_LIST = [SHAPE_POINT, SHAPE_LINE, SHAPE_POLYGON, SHAPE_CIRCLE, SHAPE_RECT]
const POLYGON_SHAPE_LIST = [SHAPE_POLYGON, SHAPE_CIRCLE, SHAPE_RECT]

const CIRCLE_RADIUS_LINE = '__shape_circle_radius_line__'
const CIRCLE_RADIUS_LABEL = '__shape_circle_radius_label__'

const GEOM_TYPE = {
  [SHAPE_POINT]: 'Point',
  [SHAPE_LINE]: 'LineString',
  [SHAPE_POLYGON]: 'Polygon',
  [SHAPE_RECT]: 'Polygon',
  [SHAPE_CIRCLE]: 'Polygon',
  [CIRCLE_RADIUS_LINE]: 'LineString',
  [CIRCLE_RADIUS_LABEL]: 'LineString'
}

const LAYER_TYPE = {
  [SHAPE_POINT]: 'circle',
  [SHAPE_LINE]: 'line',
  [SHAPE_POLYGON]: 'fill',
  [SHAPE_RECT]: 'fill',
  [SHAPE_CIRCLE]: 'fill',
  [CIRCLE_RADIUS_LINE]: 'line',
  [CIRCLE_RADIUS_LABEL]: 'line'
}

const LAYER_STYLE = {
  [SHAPE_POINT]: {
    'circle-color': '#ff0000',
    'circle-radius': 6
  },
  [SHAPE_LINE]: {
    'line-width': 2,
    'line-color': '#ff0000'
  },
  [SHAPE_POLYGON]: {
    'fill-color': '#ff0000',
    'fill-opacity': 0.1
  },
  [SHAPE_RECT]: {
    'fill-color': '#ff0000',
    'fill-opacity': 0.1
  },
  [SHAPE_CIRCLE]: {
    'fill-color': '#ff0000',
    'fill-opacity': 0.1
  },
  [CIRCLE_RADIUS_LINE]: {
    'line-width': 2,
    'line-color': '#ff0000',
    'line-dasharray': [4, 2]
  },
  [CIRCLE_RADIUS_LABEL]: {
    'symbol-placement': 'point',
    'text-field': ['get', 'radius'],
    'text-size': 24,
    'text-font': ['open-sans'],
    'text-allow-overlap': true,
    'text-offset': [0, -1],
    'text-anchor': 'left'
  }
}

const EVENT = {
  DRAW_ACTIVE: 'drawActive',
  DRAW_END: 'drawEnd',
  DRAW_CLEAR: 'drawClear'
}

const DRAW_LAYER_ID = '__draw_layer_id__'
const DRAW_LAYER_OUTLINE_ID = '__draw_layer_outline_id__'
const DRAW_LAYER_CIRCLE_RADIUS_LINE_ID = '__draw_layer_circle_radius_line_id__'
const DRAW_LAYER_CIRCLE_RADIUS_LABEL_ID = '__draw_layer_circle_radius_label_id__'

/**
 *
 */
export default class DrawTool {
  // 地图实例
  map = null
  drawType = null
  clickHandlers = null
  mouseCursor = null
  coordinates = null
  clickHandler = null
  dblClickHandler = null
  mouseMoveHandler = null
  bus = null

  geomTypes = GEOM_TYPE

  constructor (map) {
    this.bus = new Vue()
    this.setMap(map)
  }

  on (event, handler) {
    this.bus.$on(event, handler)
  }

  off (event, handler) {
    this.bus.$off(event, handler)
  }

  setMap (map) {
    this.map = map
  }

  activate (type) {
    if (SHAPE_LIST.indexOf(type) == -1) {
      throw new Error('Unsupported geometry type')
    }
    if (!this.map) {
      throw new Error('No map instance binded')
    }
    // 记录鼠标形状
    this.mouseCursor = this.map.getCanvas().style.cursor
    // 激活前前清理
    this.deactivate()
    this.drawType = type
    // 暂存并清理地图点击事件响应
    this.clickHandlers = this.map._listeners.click
    this.map._listeners.click = []
    // 向地图添加临时图层以显示绘制过程中的图形
    this._removeDrawLayers()
    this._initDrawLayers(type)
    // 添加地图点击事件响应
    this.clickHandler = (e) => this._clickHandler(e)
    this.map.on('click', this.clickHandler)
    // 画线和面需要响应双击结束绘制
    if (type == SHAPE_LINE || type == SHAPE_POLYGON) {
      this.dblClickHandler = (e) => this._dblClickHandler(e)
      this.map.on('dblclick', this.dblClickHandler)
    }
    // 初始化坐标列表
    this.coordinates = []
    // 设置鼠标形状
    this.map.getCanvas().style.cursor = 'crosshair'

    // 触发激活事件
    this.bus.$emit(EVENT.DRAW_ACTIVE, type)
  }

  deactivate () {
    this.map.off('click', this.clickHandler)
    this.map.off('mousemove', this.mouseMoveHandler)
    this.map.off('dblclick', this.dblClickHandler)
    // 恢复地图点击响应
    if (this.clickHandler) {
      this.map._listeners.click = this.clickHandlers
    }
    // 鼠标形状
    this.map.getCanvas().style.cursor = this.mouseCursor
    // 删除所有事件监听
    // this.bus._events = {}
    // 删除图层
    this._removeDrawLayers()
    //
    this.clickHandler = null
    this.dblClickHandler = null
    this.mouseMoveHandler = null
    this.mouseCursor = null
  }

  clear () {
    this.bus.$emit(EVENT.DRAW_CLEAR)
  }

  _handleDrawEnd () {
    // 清空图形
    this.coordinates = []
    this._updateDrawFeature()
    // 取消mousemove事件响应
    this.map.off('mousemove', this.mouseMoveHandler)
    this.mouseMoveHandler = null
  }

  _clickHandler (e) {
    e.preventDefault()
    const point = this._getLngLatFromClickEvent(e)

    // 如果绘制点，第一次点击则结束绘制
    if (this.drawType == SHAPE_POINT) {
      this.coordinates = point
      const feature = this._updateDrawFeature()
      this.bus.$emit(EVENT.DRAW_END, feature)
      this._handleDrawEnd()
      return
    }

    // 如果绘制矩形或圆，绘制第二个点结束
    if ((this.drawType == SHAPE_RECT || this.drawType == SHAPE_CIRCLE) && this.coordinates.length == 2) {
      const feature = this._createFeature(this.drawType, this.coordinates)
      this.bus.$emit(EVENT.DRAW_END, feature)
      this._handleDrawEnd()
      return
    }

    // 第一次点击时添加mousemove事件响应
    if (!this.mouseMoveHandler && this.coordinates.length == 0) {
      this.mouseMoveHandler = (e) => this._mouseMoveHandler(e)
      this.map.on('mousemove', this.mouseMoveHandler)
    }

    // 记录该点坐标
    this.coordinates.push(point)
    this._updateDrawFeature()
  }

  _mouseMoveHandler (e) {
    const lastPointIndex = this.coordinates.length - 1
    const coord = this._getLngLatFromClickEvent(e)
    // 如果当前只有一个点，则继续追加，如果有多个点，则替换最后一个点
    if (lastPointIndex == 0) {
      this.coordinates.push(coord)
    } else {
      this.coordinates[lastPointIndex] = coord
    }
    this._updateDrawFeature()
  }

  _dblClickHandler (e) {
    e.preventDefault()
    if (this.coordinates.length < 2) {
      return
    }
    // 去掉末尾两个重复点
    this.coordinates.splice(this.coordinates.length - 2)
    // 如果是多边形首位坐标闭合
    if (this.drawType == SHAPE_POLYGON) {
      this.coordinates.push(this.coordinates[0])
    }
    const feature = this._createFeature(this.drawType, this.coordinates)
    this.bus.$emit(EVENT.DRAW_END, feature)
    this._handleDrawEnd()
  }

  _getLngLatFromClickEvent (e) {
    if (!e || !e.lngLat) return
    return [e.lngLat.lng, e.lngLat.lat]
  }

  _initDrawLayers (type) {
    if (!type) return
    this.map.addLayer(this._createDrawLayer(type, DRAW_LAYER_ID))
    // 为面数据增加轮廓线
    if (POLYGON_SHAPE_LIST.indexOf(type) >= 0) {
      this.map.addLayer(this._createDrawLayer(SHAPE_LINE, DRAW_LAYER_OUTLINE_ID))
    }
    // 为圆增加半径线
    if (type == SHAPE_CIRCLE) {
      this.map.addLayer(this._createDrawLayer(CIRCLE_RADIUS_LINE, DRAW_LAYER_CIRCLE_RADIUS_LINE_ID))
      this.map.addLayer(this._createSymbolDrawLayer(CIRCLE_RADIUS_LABEL, DRAW_LAYER_CIRCLE_RADIUS_LABEL_ID))
    }
  }

  _removeDrawLayers () {
    if (this.map.getLayer(DRAW_LAYER_ID)) {
      this.map.removeLayer(DRAW_LAYER_ID)
      this.map.removeSource(DRAW_LAYER_ID)
    }
    if (this.map.getLayer(DRAW_LAYER_OUTLINE_ID)) {
      this.map.removeLayer(DRAW_LAYER_OUTLINE_ID)
      this.map.removeSource(DRAW_LAYER_OUTLINE_ID)
    }
    if (this.map.getLayer(DRAW_LAYER_CIRCLE_RADIUS_LINE_ID)) {
      this.map.removeLayer(DRAW_LAYER_CIRCLE_RADIUS_LINE_ID)
      this.map.removeSource(DRAW_LAYER_CIRCLE_RADIUS_LINE_ID)
    }
    if (this.map.getLayer(DRAW_LAYER_CIRCLE_RADIUS_LABEL_ID)) {
      this.map.removeLayer(DRAW_LAYER_CIRCLE_RADIUS_LABEL_ID)
      this.map.removeSource(DRAW_LAYER_CIRCLE_RADIUS_LABEL_ID)
    }
  }

  _createDrawLayer (type, id) {
    return {
      id,
      type: LAYER_TYPE[type],
      source: {
        type: 'geojson',
        data: this._createFeature(type)
      },
      paint: LAYER_STYLE[type]
    }
  }

  _createSymbolDrawLayer (type, id) {
    return {
      id,
      type: 'symbol',
      source: {
        type: 'geojson',
        data: this._createFeature(type)
      },
      layout: LAYER_STYLE[type]
    }
  }

  _createFeature (type, coordinates = []) {
    let points = JSON.parse(JSON.stringify(coordinates))
    if (points.length > 1) {
      if (type == SHAPE_CIRCLE) {
        points = [circle(points[0], points[1])]
      } else if (type == SHAPE_RECT) {
        points = [rectangle(points[0], points[1])]
      } else if (type == SHAPE_POLYGON) {
        if (points.length > 2) {
          points.push(points[0])
          points = [points]
        }
      }
    }
    return {
      type: 'Feature',
      geometry: {
        type: GEOM_TYPE[type],
        coordinates: points
      }
    }
  }

  _createCircleRadiusFeature (coordinates) {
    if (!coordinates) return
    let radius = 0; let unit = '米'
    if (coordinates.length >= 2) {
      radius = distance(coordinates[0], coordinates[1])
      if (radius > 10000) {
        radius /= 1000
        radius = radius.toFixed(2)
        unit = '千米'
      } else {
        radius = Math.round(radius)
      }
    }
    return {
      type: 'Feature',
      properties: {
        radius: `${radius}${unit}`
      },
      geometry: {
        type: GEOM_TYPE[SHAPE_LINE],
        coordinates
      }
    }
  }

  _createOutlineFeature (type, coordinates) {
    if (!type || !coordinates) return
    if (POLYGON_SHAPE_LIST.indexOf(type) == -1) return
    let points = JSON.parse(JSON.stringify(coordinates))
    if (points.length >= 2) {
      if (type == SHAPE_CIRCLE) {
        points = circle(points[0], points[1])
      } else if (type == SHAPE_RECT) {
        points = rectangle(points[0], points[1])
      } else if (type == SHAPE_POLYGON) {
        points.push(points[0])
      }
    }
    return {
      type: 'Feature',
      geometry: {
        type: GEOM_TYPE[SHAPE_LINE],
        coordinates: points
      }
    }
  }

  _updateDrawFeature () {
    if (!this.map.getLayer(DRAW_LAYER_ID)) return
    // 绘制图形
    const feature = this._createFeature(this.drawType, this.coordinates)
    this.map.getSource(DRAW_LAYER_ID).setData(feature)
    // 为面数据增加轮廓线
    if (POLYGON_SHAPE_LIST.indexOf(this.drawType) >= 0) {
      const outline = this._createOutlineFeature(this.drawType, this.coordinates)
      this.map.getSource(DRAW_LAYER_OUTLINE_ID).setData(outline)
    }
    // 为圆增加半径线
    if (this.drawType == SHAPE_CIRCLE) {
      const radius = this._createCircleRadiusFeature(this.coordinates)
      this.map.getSource(DRAW_LAYER_CIRCLE_RADIUS_LINE_ID).setData(radius)
      this.map.getSource(DRAW_LAYER_CIRCLE_RADIUS_LABEL_ID).setData(radius)
    }
    return feature
  }
}
