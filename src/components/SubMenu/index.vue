<template functional>
  <a-sub-menu :key="props.menuInfo.key" v-on="$listeners">
      <span slot="title">
        <span class="gt-icon" :class="props.menuInfo.icon"/><span>{{ props.menuInfo.title }}</span>
      </span>
      <template v-for="item in props.menuInfo.children">
        <a-menu-item v-if="!item.children" :key="item.key" @click="clickHandler(item)">
          <router-link :to="item.path">
            <span class="gt-icon" :class="item.icon"/>
            <span>{{ item.title }}</span>
          </router-link>
        </a-menu-item>
        <sub-menu v-else :key="item.key" :menuInfo="item" />
      </template>
    </a-sub-menu>
</template>

<script>

export default {

  name: 'SubMenu',

  methods: {

    clickHandler(item){
      this.$emit('click', item)
    }

  }

}

</script>

<style lang="less" scopped>

  .gt-icon{
    padding-right: 4px;
  }

</style>
