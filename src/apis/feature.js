import axios from "axios";
import store from "@/store";

const DEFAULT_LIMIT = 50000;

const getUrl = () => {
  return store.state.config?.api.feature || "/api/feature";
};

// 查询
const query = async (dataName, params) => {
  if (!dataName) return;
  const url = getUrl();
  params = !params ? {} : params;
  params.limit = !params.limit ? DEFAULT_LIMIT : params.limit;
  const resp = await axios.post(`${url}/${dataName}/query`, params);
  if (resp.status == 200 && resp.data?.code == 0) {
    return resp.data.data;
  }
};

// 获取单个要素
const getFeatureById = async (dataName, featureId, params) => {
  if (!dataName || !featureId) return;
  const url = getUrl();
  const resp = await axios.post(`${url}/${dataName}/${featureId}`, params);
  if (resp.status == 200 && resp.data?.code == 0) {
    return resp.data.data;
  }
};

// 获取单个要素
const getFeatureByKey = async (dataName, keyField, keyValue, params) => {
  if (!dataName || !keyField || !keyValue) return;
  const url = getUrl();
  params = !params ? {} : params;
  Object.assign(params, {
    limit: 1,
    filter: ["=", keyField, keyValue],
  });
  const resp = await axios.post(`${url}/${dataName}/query`, params);
  if (resp.status == 200 && resp.data?.code == 0) {
    const features = resp.data.data?.features;
    return features && features[0] ? features[0] : null;
  }
};

// 获取记录数
const count = async (dataName, keyField, params, distinct) => {
  if (!dataName) return;
  const url = getUrl();
  params = !params ? {} : params;
  if (distinct) {
    params["aggregates"] = [[keyField, "count", null, "distinct"]];
  } else {
    params["aggregates"] = [[keyField, "count"]];
  }
  const resp = await axios.post(`${url}/${dataName}/aggregation`, params);
  if (resp.status == 200 && resp.data?.code == 0) {
    const count = resp.data.data[0];
    return count[`${keyField}_count`] ? count[`${keyField}_count`] : 0;
  }
};

// 统计
const aggregate = async (dataName, params) => {
  if (!dataName) return;
  const url = getUrl();
  params = !params ? {} : params;
  const resp = await axios.post(`${url}/${dataName}/aggregation`, params);
  if (resp.status == 200 && resp.data?.code == 0) {
    return resp.data.data;
  }
};

export default {
  query,
  getFeatureById,
  getFeatureByKey,
  count,
  aggregate,
};
