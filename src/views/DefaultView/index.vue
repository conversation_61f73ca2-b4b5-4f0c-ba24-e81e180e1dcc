<template>
  <RootLayout ref="rootLayout" />
</template>

<script>
import { uid } from "uid";
import axios from "axios";
import RootLayout from "@/layouts/RootLayout";

/**
 * 作为路由模块的容器，自行读取配置文件，加载子组件
 */
export default {
  name: "DefaultView",

  components: {
    RootLayout,
  },

  data() {
    return {
      config: null,
    };
  },
  methods: {},
  async mounted() {
    this.layoutUid = `layout-${uid()}`;
    const path = this.$route.meta.configUrl;
    if (path) {
      try {
        const resp = await axios.get(path);
        this.config = resp.data;
        const parentPath = path.substring(0, path.lastIndexOf("/") + 1);
        this.$refs.rootLayout.setChildren(this.config, parentPath);
      } catch (e) {
        console.error(`${this.$options.name} load config file error, url: ${this.configUrl}`, e);
      }
    }
  },
};
</script>
