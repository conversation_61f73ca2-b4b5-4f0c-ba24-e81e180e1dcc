import store from '@/store'
import Constants from '@/constants'

/**
 * 负责根据配置设置样式
 */
export default class StyleManager{

  constructor() {
    store.subscribe((mutation, state) => {
      if (mutation.type == Constants.mutations.SET_CONFIG && state.config.page) {
        this._initPageStyle(state.config.page)
      }
    })
  }

  _initPageStyle(page) {
    if (page.favicon) {
      const link = document.querySelector("link[rel*='icon']") || document.createElement('link')
      link.type = 'image/x-icon'
      link.rel = 'shortcut icon'
      link.href = this._stripHTML(page.favicon)
      document.getElementsByTagName('head')[0].appendChild(link)
    }
    if (page.title) {
      document.title = this._stripHTML(page.title)
    }
  }

  _stripHTML (str) {
    if (!str) {
      return str
    }
    if (str.indexOf('<') > -1 && str.indexOf('>') > -1) {
      var matchTag = /<(?:.|\s)*?>/g
      return str.replace(matchTag, '')
    } else {
      return str
    }
  }
}