import Constants from "@/constants";
import FeatureApi from "@/apis/feature";
import { divisor } from "@/utils/number";

const LAYER_VISIBILITY = {
  VISIBILITY: "visibility",
  VISIBLE: "visible",
  NONE: "none",
};

const EMPTY_SOURCE = {
  type: "geojson",
  data: {
    type: "Feature",
    geometry: {
      type: "Point",
      coordinates: [],
    },
  },
};

const QUERY_LIMIT_DEFAULT = 50000;

export default class LayerManager {
  constructor(map) {
    this.map = map;
    // 图层列表
    this.layers = [];

    this.legendMap = {};
  }

  /**
   * 显示图层
   * @param  layer 图层信息，结构如下：
   * {
   *   "key": "layer-xxxxxx",             // 图层KEY，唯一标识，可作为layerId使用
   *   "type": "vector|raster|feature",   // 图层类型，vector、raster、feature
   *   "url": "http://..."                // 服务地址
   *   "style": {...}                     // 图层样式，type为vector或者feature时可用
   *   "order": 2                         // 图层顺序
   *   "orderWeight": 3                   // 图层顺序权重，顺序相等时参考权重
   * }
   */
  async showLayer(layer, geometryFilter, regionFilter, filterData, layerStyleData) {
    if (!this.map || !layer || !layer.key) return;
    const layerId = layer.key;
    const theLayer = this.map.getLayer(layerId);
    if (theLayer) {
      this._showLayer(layerId);
    } else {
      await this._addLayer(layer, geometryFilter, regionFilter, filterData, layerStyleData);
      // 记录该图层的信息
      this._addToLayersArray(layer);
      // 如果是新增图层返回true
      return true;
    }
  }

  // 记录图层信息，并按照order排序
  _addToLayersArray(layer) {
    if (!layer || !layer.key) return;
    // 相同的key视为同一个图层
    const keys = this.layers.map((layer) => layer.key);
    if (keys.indexOf(layer.key) >= 0) return;
    // 记录图层并重新排序
    this.layers.push(layer);
    this.layers.sort((a, b) => {
      const orderDiff = a.order - b.order;
      // 如果用户配置的order相等，则参考配置文件中的顺序，排在上面的图层优先显示
      if (orderDiff == 0) {
        return b.orderWeight - a.orderWeight;
      }
      return orderDiff;
    });
  }

  // 显示已存在的图层
  _showLayer(layerId) {
    const visibility = this.map.getLayoutProperty(layerId, LAYER_VISIBILITY.VISIBILITY);
    if (visibility !== LAYER_VISIBILITY.VISIBLE) {
      this.map.setLayoutProperty(layerId, LAYER_VISIBILITY.VISIBILITY, LAYER_VISIBILITY.VISIBLE);
    }
  }

  // 添加新图层
  async _addLayer(layer, geometryFilter, regionFilter, filterData, layerStyleData) {
    if (!layer) return;
    // 获取图层顺序
    const beforeId = this._getBeforeLayerId(layer);
    switch (layer.type) {
      case Constants.layerTypes.VECTOR:
        await this._addVectorLayer(layer, beforeId, geometryFilter, regionFilter, filterData, layerStyleData);
        break;
      case Constants.layerTypes.RASTER:
        this._addRasterLayer(layer, beforeId);
        break;
      case Constants.layerTypes.FEATURE:
        this._addFeatureLayer(layer, beforeId, geometryFilter, regionFilter, filterData, layerStyleData);
        break;
    }
  }

  _getBeforeLayerId(layer) {
    // 比较原则：
    // 1. 先比较order，order代表用户指定的顺序，越大优先级越高
    // 2. order相同时比较orderWeight
    // 3. orderWeight代表配置文件中图层顺序，越小优先级越高
    for (let i = 0; i < this.layers.length; i++) {
      if (this.layers[i].order >= layer.order && this.layers[i].orderWeight < layer.orderWeight) {
        return this.layers[i].key;
      }
    }
  }

  // 添加矢量瓦片图层
  async _addVectorLayer(layer, beforeId, geometryFilter, regionFilter, filterData, layerStyleData) {
    if (!layer) return;

    const dataSource = this.map.getSource(layer.key);
    if (!dataSource) {
      let source = {
        type: "vector",
        tiles: [layer.url],
        // add minzoom and maxzoom
      };
      if (layer.maxzoom) {
        source.maxzoom = layer.maxzoom;
      }
      this.map.addSource(layer.key, source);
    }
    const options = {
      id: layer.key,
      source: layer.key,
      "source-layer": layer["source-layer"] ? layer["source-layer"] : "layer", // default value: layer
      layout: {
        visibility: "visible",
      },
    };

    Object.assign(options, layer.style);
    if (layer.style?.type === "symbol" && layer.style.layout?.["icon-image"]) {
      const imageUrl = layer.style.layout["icon-image"];
      if (this.map.hasImage(imageUrl)) {
        this.map.addLayer(options, beforeId);
      } else {
        this.map.loadImage(imageUrl, (err, image) => {
          if (err) {
            console.log("load image error:", err);
          }
          this.map.addImage(imageUrl, image, { sdf: true });

          this.map.addLayer(options, beforeId);
        });
      }
    } else {
      this.map.addLayer(options, beforeId);
    }
    // 空间过滤
    this.refreshLayer(layer, geometryFilter, regionFilter, filterData);
    if (layerStyleData) {
      this.refreshLayerStyle(layer, layerStyleData);
    }
    if (layer.apply) {
      await this.applyLayer(layer);
    }
  }

  // 添加栅格瓦片图层
  _addRasterLayer(layer, beforeId) {
    if (!layer) return;
    const dataSource = this.map.getSource(layer.key);
    if (!dataSource) {
      this.map.addSource(layer.key, {
        type: "raster",
        tiles: [layer.url],
        tileSize: 256,
      });
    }
    this.map.addLayer(
      {
        id: layer.key,
        type: "raster",
        source: layer.key,
        layout: {
          visibility: "visible",
        },
      },
      beforeId
    );
  }

  // 添加Feature图层（通过Feature API获取数据）
  _addFeatureLayer(layer, beforeId, geometryFilter, regionFilter, filterData, layerStyleData) {
    if (!layer) return;
    const dataSource = this.map.getSource(layer.key);
    if (!dataSource) {
      this.map.addSource(layer.key, EMPTY_SOURCE);
    }
    const options = {
      id: layer.key,
      source: layer.key,
      layout: {
        visibility: "visible",
      },
    };
    if (layer.filter) {
      options.filter = layer.filter;
    }

    Object.assign(options, layer.style);
    if (layer.images) {
      this._addFeatureSymbolLayer(layer, options, beforeId, geometryFilter, regionFilter, filterData);
    } else {
      this.map.addLayer(options, beforeId);
      FeatureApi.query(layer.name, layer.params).then((features) => {
        this.map.getSource(layer.key).setData(features);
      });
      // 空间过滤

      this.refreshLayer(layer, geometryFilter, regionFilter, filterData);
      if (layerStyleData) {
        this.refreshLayerStyle(layer, layerStyleData);
      }
    }
  }

  // 使用图标显示图层
  _addFeatureSymbolLayer(layer, options, beforeId, geometryFilter, regionFilter) {
    options.layout.visibility = "visible";
    const imageUrls = layer.images;
    imageUrls.forEach(async (imageUrl) => {
      if (this.map.hasImage(imageUrl)) {
        return;
      } else {
        await this.map.loadImage(imageUrl, (err, image) => {
          if (err) {
            console.log("load image error:", err);
          }
          this.map.addImage(imageUrl, image);
        });
      }
    });

    this.map.addLayer(options, beforeId);
    FeatureApi.query(layer.name, layer.params).then((features) => {
      this.map.getSource(layer.key).setData(features);
    });
    this.refreshLayer(layer, geometryFilter, regionFilter);
  }

  // 刷新所有图层
  refreshAllLayers(geometryFilter, regionFilter, layerFilter) {
    this.layers.forEach((layer) => {
      if (layer.enableRegionFilter || layer.enableGeometryFilter) {
        if (layer.type === Constants.layerTypes.VECTOR || layer.type === Constants.layerTypes.FEATURE) {
          let filterData = null;

          if (layerFilter && layerFilter[layer.key]) {
            if (!layerFilter[layer.key]) return;
            let data = layerFilter[layer.key];
            // 清除对象的无效值
            Object.keys(data).forEach((key) => {
              if (!data[key]) delete data[key];
            });
            filterData = ["all", ...Object.values(data)];
            if (filterData.length === 2) {
              filterData = filterData[1];
            }
          }

          this.refreshLayer(layer, layer.enableGeometryFilter ? geometryFilter : null, layer.enableRegionFilter ? regionFilter : null, filterData);
        }
      }
    });
  }

  // 刷新单个图层
  async refreshLayer(layer, geometryFilter, regionFilter, filterData) {
    if (!geometryFilter && !regionFilter && !filterData && !layer.filter) {
      // 清空过滤
      const filter = this.map.getFilter(layer.key);
      if (filter) {
        this.map.setFilter(layer.key, null);
      }
      return;
    }

    let filter = ["all"];
    if (layer.filter) {
      filter.push(layer.filter);
    }
    if (regionFilter) {
      filter.push(regionFilter);
    }

    if (geometryFilter) {
      const limit = layer.params?.limit ? layer.params.limit : QUERY_LIMIT_DEFAULT;
      const params = {
        outFields: [layer.primaryKey],
        format: "json",
        returnGeometry: false,
        geometry: geometryFilter,
        limit,
      };
      let features = await FeatureApi.query(layer.name, params);
      if (features || features.length !== 0) {
        const ids = features.map((f) => f[layer.primaryKey]);
        filter.push(["match", ["get", layer.primaryKey], [...ids], true, false]);
      }
    }
    if (filterData) {
      filter.push(filterData);
    }
    if (filter.length == 2) {
      filter = filter[1];
    }
    console.log("setFilter========>", layer.key, filter);

    this.map.setFilter(layer.key, filter);
  }

  refreshLayerStyle(layer, styleObj) {
    Object.keys(styleObj).forEach((key) => {
      this.map.setPaintProperty(layer.key, key, styleObj[key]);
    });
  }

  // 隐藏图层
  hideLayer(layer) {
    if (!this.map || !layer || !layer.key) return;
    const layerId = layer.key;
    const theLayer = this.map.getLayer(layerId);
    if (theLayer) {
      this._hideLayer(layerId);
    }
  }

  // 隐藏已存在的图层
  _hideLayer(layerId) {
    const visibility = this.map.getLayoutProperty(layerId, LAYER_VISIBILITY.VISIBILITY);
    if (visibility === LAYER_VISIBILITY.VISIBLE) {
      this.map.setLayoutProperty(layerId, LAYER_VISIBILITY.VISIBILITY, LAYER_VISIBILITY.NONE);
    }
  }

  async applyLayer(layer) {
    const uri = layer.applyConfig.uri;
    const { applyField, dataField, layerField } = layer.applyConfig.fields;
    const filter = layer.applyConfig.filter ? layer.applyConfig.filter : null;
    const colors = layer.applyConfig.colors ? layer.applyConfig.colors : ["#fffdd0", "#ddeeab", "#b7db95", "#60a965", "#265936"];
    let param = {
      outFields: [applyField, dataField],
      format: "json",
      returnGeometry: false,
      filter,
    };

    let data = await FeatureApi.query(uri, param);
    let splitList = this.splitDataByAvg(
      data.map((item) => {
        return item[dataField];
      })
    );
    let legendData = colors.map((item, index) => {
      return {
        min: Number(splitList[index]),
        max: Number(splitList[index + 1]),
      };
    });
    this.legendMap[layer.key] = {
      key: layer.key,
      legend: {
        title: layer.title,
        decimal: 2,
        type: "polygon",
        items: legendData.map((item, index) => {
          if (item.min >= 10000) item.min = divisor(item.min, 10000, 0) + "万";
          if (item.max >= 10000) item.max = divisor(item.max, 10000, 0) + "万";
          return {
            label: index === legendData.length - 1 ? `${item.min}以上` : item,
            symbol: {
              color: colors[index],
              icon: "",
            },
          };
        }),
      },
    };
    let paintColor = ["match", ["get", layerField]];
    data.forEach((item) => {
      for (let i = 0; i < splitList.length; i++) {
        let lastIndex = splitList.length - 1;
        if (item[dataField] > splitList[i] && (i == lastIndex || item[dataField] <= splitList[i + 1])) {
          let color = i === lastIndex ? colors[lastIndex] : colors[i];
          paintColor.push(item[applyField], color);
          break;
        }
      }
    });
    paintColor.push("rgba(0,0,0,.05)");
    if (data.length === 0) {
      paintColor = "rgba(0,0,0,.05)";
    }
    this.map.setPaintProperty(layer.key, "fill-color", paintColor);
  }

  splitDataByAvg(values, splitLen = 5) {
    function getRoundOff(value, base) {
      let floor = Math.floor(value / base);
      if (floor > 10) {
        if (base > 10000) {
          let result = floor * base;
          return {
            value: result,
            floor,
            base,
          };
        }
        base = base * 10;
        return getRoundOff(value, base);
      } else {
        let result = floor * base;
        return {
          value: result,
          floor,
          base,
        };
      }
    }

    function getStepValue(value, base) {
      let length = splitLen - 2;
      if (value == 0) return 0;
      let floor = Math.floor(value / base);
      if (floor > 10) {
        base = base * 10;
        return getStepValue(value, base);
      } else {
        if (base == 10 && value < 10) return Number((value / length).toFixed(0));
        let average = Number(floor * base) / length;
        let result = Math.max(0.5, Math.round(average / base)) * base;
        return result;
      }
    }

    if (values.length == 0) return [];
    values.sort((a, b) => {
      return (a ? parseFloat(a) : 0) - (b ? parseFloat(b) : 0);
    });
    const _values = [];
    values.forEach((item) => {
      if (!item) return false;
      if (_values.indexOf(item) < 0) {
        _values.push(Number(item));
      }
    });
    values = _values;
    let stepValue = 0;
    let stepStart = 0;
    let lastIndex = values.length - 1;
    // 获取相应的数值
    const endIndex = values.length > 2 ? Math.floor(lastIndex * 0.8) : lastIndex;
    const startIndex = values.length > 3 ? Math.floor(lastIndex * 0.2) : 0;

    // 获取最大值与最小值的差距
    let valueGap = values[endIndex] - values[0];
    // 获取步骤差距
    stepValue = getStepValue(valueGap, 0.01);
    // 判断大小值差距与步骤差距
    let minResult = getRoundOff(values[startIndex], 0.01);
    stepStart = minResult.base > valueGap ? Math.floor(values[startIndex]) : values[0];
    if (stepStart == 0) stepStart = values[startIndex];
    stepValue = getStepValue(valueGap, 0.01);

    const stepList = [];
    for (let i = 0; i < splitLen; i++) {
      let value = i > 0 ? stepValue * i + stepStart : 0;
      value = Number(value.toFixed(2));
      if (value > 10) value = Math.floor(value);
      if (value > 10000) value = getRoundOff(value, 10000).value;
      console.log(value, "step-item");
      stepList.push(value);
    }
    return stepList;
  }
}
