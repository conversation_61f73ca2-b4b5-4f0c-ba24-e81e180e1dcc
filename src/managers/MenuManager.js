import store from '@/store'
import { uid } from 'uid'
import Constants from '@/constants'

const ROUTER_LAYOUT_URI = 'layouts/RouterLayout'

class Menu {

  constructor(obj) {
    if (!obj) return
    const { name, path, icon, children } = obj
    this.title = name
    this.path = path
    this.icon = icon
    this.key = path
    if (children && Array.isArray(children) && children.length > 0) {
      this.items = children.map(child => new <PERSON>u(child))
    }
  }

  toMenuItem() {
    //
    if (!this.items) {
      return this._toMenuItem()
    }
    //
    const hasChildrenItems = this.items.filter(item => item.items)
    if (hasChildrenItems.length == 0) {
      return {
        key: this.items[0].path,
        icon: this.icon,
        title: this.title,
        path: this.items[0].path,
        modules: this.items
      }
    }
    //
    const children = this.items.map(item => item.toMenuItem())
    return {
      key: `jimu-menu-${uid()}`,
      icon: this.icon,
      title: this.title,
      children
    }
  }

  _toMenuItem() {
    return {
      key: this.path,
      icon: this.icon,
      title: this.title,
      path: this.path,
      modules: [
        {
          icon: this.icon,
          title: this.title,
          path: this.path
        }
      ]
    }
  }

}

/**
 * 负责根据配置文件生成菜单
 */
export default class MenuManager{

  // 从配置文件中找到RouterLayout并配置路由
  constructor() {
    store.subscribe((mutation, state) => {
      if (mutation.type == Constants.mutations.SET_CONFIG && state.config.children) {
        this._initMenu(state.config.children)
      }
    })
  }

  _initMenu(children) {
    if (!children && !Array.isArray(children)) return
    const menuItems = []
    children.forEach((child) => {
      if (child.uri == ROUTER_LAYOUT_URI && child.children && Array.isArray(child.children)) {
        child.children.forEach((child) => {
          const menuInfo = new Menu(child).toMenuItem()
          if (menuInfo) {
            menuItems.push(menuInfo)
          }
        })
      }
    })
    store.commit(Constants.mutations.SET_MENU, menuItems)
  }

}