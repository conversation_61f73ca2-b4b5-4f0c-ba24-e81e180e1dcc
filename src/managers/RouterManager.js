import store from '@/store'
import router from '@/router'
import Constants from '@/constants'

const ROUTER_LAYOUT_URI = 'layouts/RouterLayout'

/**
 * 负责根据配置文件生成路由
 */
export default class RouterManager{

  // 从配置文件中找到RouterLayout并配置路由
  constructor() {
    store.subscribe((mutation, state) => {
      if (mutation.type == Constants.mutations.SET_CONFIG && state.config.children) {
        this._initAllRoutes(state.config.children)
      }
    })
  }

  _initAllRoutes(children) {
    if (!children && !Array.isArray(children)) return
    const routes = []
    children.forEach((child) => {
      if (child.uri == ROUTER_LAYOUT_URI && child.children && Array.isArray(child.children)) {
        child.children.forEach((child) => {
          const childRoutes = this._initRouters(child)
          routes.push(...childRoutes)
        })
      }
    })
    routes.forEach( route => router.addRoute(route))
    // 设置默认路由
    router.addRoute({
      path: '*',
      redirect: routes[0].path
    })
    store.commit(Constants.mutations.SET_ROUTES, routes)

    //
  }

  _initRouters(child) {
    const routes = []
    if (child.children && Array.isArray(child.children)){
      child.children.forEach((c) => {
        const childRoutes = this._initRouters(c)
        routes.push(...childRoutes)
      })
    } else {
      let route = this._initRouter(child)
      if (route) {
        routes.push(route)
      }
    }
    return routes
  }

  // 构建路由
  _initRouter(route) {
    return {
      path: route.path,
      name: route.name,
      component: () => import(`@/${route.uri}`),
      meta: {
        configUrl: route.config
      }
    }
  }

}