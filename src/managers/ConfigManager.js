import axios from 'axios'
import store from '@/store'
import Constants from '@/constants'

const DEFAULT_CONFIG_URL = 'config_test.json'

/**
 * 负责全局配置文件的解析和共享
 */
export default class ConfigManager{

  constructor() {
  }

  async load() {
    const configUrl = this._getQueryString('config') || DEFAULT_CONFIG_URL
    const resp = await axios.get(configUrl)
    if (resp.status != 200) {
      console.error('config manager load config file error')
      return
    }
    let config = resp.data
    config = this._parse(config)
    this._store(config)
    return config
  }

  _parse(config) {
    return config
  }



  _store(config) {
    store.commit(Constants.mutations.SET_CONFIG, config)
  }

  _getQueryString(name) {
    let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
    let r = window.location.search.substr(1).match(reg);
    if (r != null) {
        return decodeURIComponent(r[2]);
    }
    return null
 }

}