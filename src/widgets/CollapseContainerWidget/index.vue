<template>
  <div class="chart-wrap-widget">
    <a-collapse
      expandIconPosition="right"
      v-model="activeKey"
      v-if="children.length"
    >
      <a-collapse-panel
        :key="0"
        class="header ant-collapse-item-active"
        :header="layerConfig.title"
      >
        <template v-for="(child, index) in children">
          <div v-show="index == showKey" class="content" :key="index">
            <div class="icon-wrap">
              <template v-for="(c, i) in children">
                <div
                  :key="i"
                  :class="[
                    'change-child-icon',
                    i == showKey ? 'icon-active' : ''
                  ]"
                  v-if="children.length !== 1"
                >
                  <a-icon :type="c.icon" @click="changeChildShow(i)" />
                </div>
              </template>
            </div>
            <div class="jimu-collapse-cont">
              <div class="child-box" style="width: 100%; height: 100%">
                <component :is="child.module"></component>
              </div>
            </div>
          </div>
        </template>
      </a-collapse-panel>
    </a-collapse>
  </div>
</template>

<script>
/**
 * 布局组件，用于通过配置文件对子组件布局
 */
import BaseWidget from '@/BaseWidget'

export default {
  
  name: 'CollapseContainerWidget',

  mixins: [BaseWidget],

  data() {
    return {
      // 子组件配置
      activeKey: [0],
      children: [],
      param: {},
      showKey: 0
    }
  },

  created() {},

  configLoaded() {},
  mounted() {
    this.setChildren(this.layerConfig.charts)
  },
  methods: {
    changeChildShow(i) {
      this.showKey = i
      // if (this.showKey < this.children.length - 1) {
      //   this.showKey++
      // } else {
      //   this.showKey = 0
      // }
    },

    setChartData(param) {
      // this.param = param
      this.$set(this.param, param.name, param.param)
    },
    setTableData(param) {
      // this.param = param
      this.$set(this.param, param.name, param)
    },
    async setChildren(children) {
      const modules = []
      if (!children || !Array.isArray(children)) return
      for (let i = 0; i < children.length; i++) {
        let module = await this._loadModule(children[i])
        modules.push({
          module,
          title: children[i].name,
          icon: children[i].icon,
          name: children[i].name
        })
      }
      this.children = modules
    },
    // 加载模块
    async _loadModule(config) {
      const module = await import(`@/${config.uri}`)
      const theModule = this.$utils.object.deepCloneObj(module.default)
      let dataObj = {}
      if (theModule.data) {
        dataObj = theModule.data()
      }
      dataObj.dataConfig = config.config
      dataObj.layerName = this.layerConfig.name
      theModule.data = () => this.$utils.object.deepCloneObj(dataObj)
      return theModule
    }
  }
}
</script>

<style lang="less" scoped>
@import '@/assets/styles/common.less';
.chart-wrap-widget {
  width: 100%;
}
.jimu-root-layout {
  width: 100%;
  // height: 100%;
  position: absolute;
  background: #fff;
}
.table {
  overflow-y: scroll;
  .content {
    width: 100%;
    height: 100%;
    .icon-wrap {
      display: flex;
      // float: right;
      width: 100%;
      justify-content: flex-end;
    }
    .change-child-icon {
      padding: 0px 5px;
    }
    .icon-active {
      color: @primaryColor;
    }
    .jimu-collapse-cont {
      width: 100%;
      height: 200px;
      min-height: 200px;
      position: relative;
    }
  }
}
</style>
