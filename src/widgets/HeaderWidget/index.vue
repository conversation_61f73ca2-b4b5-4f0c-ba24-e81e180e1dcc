<template>
  <div class="jimu-widget header-widget">
    <div class="logo" :style="{width:logoSize+'px',height:logoSize+'px'}">
      <img v-i="logo" :src="`${logo}`" alt="">
    </div>
    <span :style="{color:titleColor,fontSize:titleSize+'px',fontWeight:titleWeight}">{{title}}</span>
  </div>
</template>

<script>

import {mapState} from 'vuex'

import BaseWidget from '@/BaseWidget'

export default {

  name: 'HeaderWidget',

  mixins: [BaseWidget],

  computed: {
    ...mapState({
      title: state => state.config.header?.title,
      logo: state => state.config.header?.logo,
      logoSize: state =>state.config.header?.logoSize,
      titleSize: state =>state.config.header?.titleSize,
      titleColor: state =>state.config.header?.titleColor,
      background: state =>state.config.header?.background,
      titleWeight: state =>state.config.header?.titleWeight
    })

  },
  methods:{

  },
  mounted(){

  }
}
</script>

<style lang="less" scoped>

@import '@/assets/styles/common.less';
.header-widget{
  width: 100%;
  height: 90px;
  font-size: 50px;
  background: #4065E0;
  color: #ffffff;
  line-height: 90px;
  padding-left: 12px;
  background-repeat: no-repeat;
  background-size: 100% 100%;

  .logo{
    float: left;
    margin-right: 10px;
    text-align: center;
    // line-height: 90px;
  }
  img{
    width: 100%;
    background-image: cover;
  }
}

</style>
