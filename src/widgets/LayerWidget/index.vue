<template>
  <div class="jimu-widget open-style" :class="{ 'jimu-tool-widget': !stateIndex }">
    <div v-show="!stateIndex" class="scroll">
      <div class="gt-icon gt-icon-close close" v-show="!exist" @click="clickHandler"></div>
      <a-tree
        :checkable="true"
        :selectable="false"
        :show-icon="true"
        :tree-data="layerTreeData"
        :checkedKeys="checkedKeys"
        :expanded-keys="expandedKeys"
        @check="checkHandler"
        @expand="expandHandler"
      >
        <template v-slot:title="nodeData">
          <div class="tree-title-wrapper" v-if="!nodeData.type">
            {{ nodeData.title }}
            <a-icon
              type="table"
              class="icon-table"
              :class="{ active: nodeData.tableOpen }"
              v-if="!nodeData.children && nodeData.table && nodeData.checked"
              @click.stop="tableIconClickHandler(nodeData)"
            />
          </div>
          <div class="tree-title-wrapper" v-else>
            <div v-if="nodeData.type === 'linkTitle'">
              {{ nodeData.title }}
            </div>
            <div v-if="nodeData.type === 'link'">
              <p>
                <a-icon type="link" :style="{ fontSize: '16px' }" />
                <a v-lese target="_blank" :href="nodeData.url">{{ nodeData.title }}</a>
              </p>
            </div>
          </div>
        </template>
      </a-tree>
    </div>
    <div v-show="stateIndex" class="gt-icon gt-icon-openTool jimu-tool-widget open" @click="clickHandler"></div>
  </div>
</template>

<script>
import { uid } from "uid";
import BaseWidget from "@/BaseWidget";
import { mapState } from "vuex";
import axios from "axios";
export default {
  name: "LayerWidget",

  mixins: [BaseWidget],

  data() {
    return {
      layerTreeData: null,
      // 上一次选中的节点
      checkedKeys: [],
      // 所有叶子节点
      leafNodesMap: {},
      // 展开的图层组
      expandedKeys: [],

      layerConfigMap: {},

      stateIndex: 0,
      exist: null,
    };
  },

  allLoaded() {
    const tree = this.$utils.object.deepCloneJson(this.config);

    let layerTree = this.parseTreeConfig(tree, this.leafNodesMap);

    this.layerTreeData = layerTree.filter((item) => {
      return item.display;
    });
    console.log(this.layerTreeData, "layerTreeData");

    this.checkedKeys = Object.values(this.leafNodesMap)
      .filter((n) => {
        return n.open && n.display;
      })
      .map((n) => n.key);

    // 打开默认open的图层
    this.showDefaultOpenLayers();

    // 添加事件响应
    this.addListeners();
  },

  mounted() {},
  computed: {
    ...mapState({
      initExtent: (state) => ({
        zoom: state.config.map?.initZoom,
        center: state.config.map?.initCenter,
      }),
      basemaps: (state) => state.config?.basemaps,
      apiUrl: (state) => state.config.api,

      adminData: (state) => state.data?.admin,
    }),
  },

  methods: {
    downloadIconClickHandler(node) {
      console.log("下载", node);
    },
    addListeners() {
      this.bus.$on(this.$events.table.TABLE_CLOSED, this.tableClosedHandler);
      this.bus.$on(this.$events.layer.CHECKED_LAYER, this.checkKeyEvent);
    },

    tableClosedHandler(key) {
      if (!key) return;
      const node = this.leafNodesMap[key];
      if (!node) return;
      node.tableOpen = false;
    },

    parseTreeConfig(tree, leafNodesMap, parentNodeDisplay = true) {
      if (!tree || !Array.isArray(tree)) return;
      // 图层顺序作为显示顺序权重，order相等时参考顺序
      tree.forEach((node) => {
        // 设置图层唯一KEY
        node.key = node.key ? node.key : `jimu-layer-${uid()}`;

        //判断当前节点是否显示在图层树
        let display = node.display !== false;
        node.display = !parentNodeDisplay ? false : display;

        if (node.type) {
          node.disabled = true;
        }

        // 识别叶子节点并保存
        if (!node.children) {
          // 解析图层显示顺序和权重，默认为0
          node.order = node.order || 0;
          node.orderWeight = Object.keys(leafNodesMap).length;
          // 是否默认打开
          node.open = !node.open ? false : true;
          // 是否显示表格图标
          node.tableOpen = false;
          leafNodesMap[node.key] = node;
        } else {
          if (!node.unExpanded) {
            this.expandedKeys.push(node.key);
          }
        }
        // 递归处理
        if (node.children) {
          node.children = this.parseTreeConfig(node.children, leafNodesMap, node.display);
        }
      });
      return tree;
    },

    showDefaultOpenLayers() {
      let layerKeys = Object.values(this.leafNodesMap)
        .filter((n) => n.open)
        .map((n) => n.key);

      this.toggleLayers(layerKeys, true);
    },

    checkHandler(checkedKeys, e) {
      let keys,
        checked = e.checked;
      // 获取本次选中的节点
      if (checked) {
        keys = checkedKeys.filter((k) => !this.checkedKeys.includes(k));
      }
      // 获取本次取消选中的节点
      else {
        keys = this.checkedKeys.filter((k) => !checkedKeys.includes(k));
      }
      // 记录本次操作后选中的节点
      this.checkedKeys = checkedKeys;
      this.toggleLayers(keys, e.checked);
    },

    toggleLayers(keys, show) {
      if (!keys || !Array.isArray(keys)) return;
      // 获取叶子节点
      const nodes = this.getLeafNodes(keys);
      // 派发图层事件
      const event = !show ? this.$events.map.LAYER_HIDE : this.$events.map.LAYER_SHOW;
      this.bus.$emit(event, nodes);
      if (show) {
        this.layerShowHandler(nodes);
      } else {
        this.layerHiddenHandler(keys);
      }
    },

    layerHiddenHandler(layers) {
      layers.forEach(async (layer) => {
        // 关闭图例
        const layerConfig = this.layerConfigMap[layer];
        if (layerConfig) {
          this.bus.$emit(this.$events.widget.CLOSE_WIDGET, layerConfig);
        }
      });
    },

    genLayerUrl(layer) {
      if (!layer || !layer.type) return;
      switch (layer.type) {
        case this.$constants.layerTypes.VECTOR:
          return `${this.apiUrl?.vector}/${layer.name}/{z}/{x}/{y}.vector.pbf`;
        case this.$constants.layerTypes.RASTER:
          return `${this.apiUrl?.raster}/${layer.name}/{z}/{x}/{y}.png`;
        case this.$constants.layerTypes.FEATURE:
          return `${this.apiUrl?.feature}/${layer.name}/query`;
      }
    },

    async getLayerConfig(layer) {
      if (!layer || !layer.key || !layer.config) return;
      layer = this.$utils.object.deepCloneJson(layer);
      const resp = await axios.get(layer.config);
      if (layer.extend) {
        let extend = await axios.get(layer.extend);
        resp.data = Object.assign(resp.data, extend.data);
      }
      let config = resp.data;
      config.url = this.genLayerUrl(config);
      Object.assign(layer, config);
      return layer;
    },

    async layerShowHandler(layers) {
      this.openLayers = layers;
      if (!layers || !Array.isArray(layers)) return;
      layers.forEach(async (layer) => {
        let layerConfig = this.layerConfigMap[layer.key];
        // 获取图层配置信息
        if (!layerConfig) {
          layerConfig = this.layerConfigMap[layer.key] = await this.getLayerConfig(layer);
        }
        if (layerConfig.charts) {
          this.bus.$emit(this.$events.widget.OPEN_WIDGET, layerConfig);
        }
      });
    },

    getLeafNodes(keys) {
      if (!keys || !Array.isArray(keys)) return;
      const nodes = keys.map((k) => this.leafNodesMap[k]);
      return nodes.filter((n) => n);
    },

    expandHandler(expandedKeys) {
      this.expandedKeys = expandedKeys;
    },

    tableIconClickHandler(nodeData) {
      nodeData.dataRef.tableOpen = !nodeData.tableOpen;
      const event = nodeData.dataRef.tableOpen ? this.$events.table.OPEN_TABLE : this.$events.table.CLOSE_TABLE;
      const payload = this.genOpenTablePayload(nodeData);
      this.bus.$emit(event, payload);
    },

    genOpenTablePayload(node) {
      console.log("node----->", node);

      if (!node) return;
      const { key, title, tableComponent, config, extend } = node;
      return {
        key,
        title,
        tableComponent,
        config,
        extend,
      };
    },

    clickHandler() {
      if (!this.states) {
        this.stateIndex = this.stateIndex ? 0 : 1;
        return;
      }
      this.stateIndex++;
      if (this.stateIndex >= this.states.length) {
        this.stateIndex = 0;
      }
      const state = this.states[this.stateIndex];
      this.bus.$emit(this.$events.state.WIDGETSTATE_UPDATE, state);
    },
    checkKeyEvent(e) {
      this.toggleLayers(this.checkedKeys, false);
      let keyList = Array.isArray(e) ? e : [e];
      this.checkedKeys = keyList.filter((item) => !this.checkedKeys.includes(item));
      if (this.checkedKeys.length > 0) {
        this.toggleLayers(this.checkedKeys, true);
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";
.tree-title-wrapper {
  display: block;
  font-size: 18px;
  font-weight: 450;
  color: @primaryTextColor;
}

.open-style {
  background: #eef0f9;
}

.scroll {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}

.close {
  float: right;
  cursor: pointer;
  position: absolute;
  right: 3px;
  top: 3px;
}
.open {
  padding: 0px;
  text-align: center;
  height: 40px !important;
  width: 40px !important;
  cursor: pointer;
  font-size: 30px;
  text-align: center;
  line-height: 40px;
}
.icon-table {
  position: absolute;
  right: 12px;
  margin-top: 4px;
  color: @lightColor;

  &:hover,
  &.active {
    color: @primaryColor;
  }
}
/deep/.ant-tree li {
  margin: 2px;
  position: relative;
}
</style>
