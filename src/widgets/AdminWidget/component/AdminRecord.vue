<template>
  <div class="admin-record">
    <template v-for="(recordItem, index) in recordList">
      <div class="record-item" :key="index">
        <div class="record-value" @click="handleRecordClick(recordItem)">
          <span class="gt-icon gt-icon-time" />
          <template v-for="(item, index) in recordItem">
            <span :key="index"> {{ getAdminName(item, index) }}/ </span>
          </template>
        </div>
        <span class="close" @click="delRecord(recordItem)">
          <span class="gt-icon gt-icon-close record-close"
        /></span>
      </div>
    </template>
  </div>
</template>

<script>
export default {
  name: 'AdminRecord',
  computed: {},

  props: {
    recordList: { type: Array },
    adminBase: { type: Array },
    baseLevel: { type: Number }
  },

  data() {
    return {
      showAdminCont: false,
      recordIsShow: false,
      tabActiveKey: 0,
      adminList: [],
      adminValue: '',
      leaveAdmin: false,
      inputFocused: false
    }
  },

  mounted() {
    // 监听点击事件判断是否点击非输入框范围
    document.addEventListener('click', () => {
      // if (
      //   e.target.className !== 'record-item' &&
      //   !this.inputFocused &&
      //   e.target.className.indexOf('record-close') == -1
      // ) {
      this.recordIsShow = false
      // }
      // if (this.leaveAdmin) {
      this.showAdminCont = false
      // }
    })
  },

  methods: {
    getAdminName(item, level) {
      let nameKey = this.adminBase[level].nameKey
      return item[nameKey]
    },
    handleRecordClick(record) {
      this.$emit('handleRecordClick', record)
    },
    delRecord(delItem) {
      this.$emit('delRecord', delItem)
    }
  }
}
</script>

<style lang="less" scoped>
@import '@/assets/styles/common.less';
.admin-record {
  top: 0;
  left: 0;
  width: 100%;
  position: absolute;
  z-index: 999;
  font-size: 17px;
  border-radius: 4px;
  box-shadow: @boxShadow;
  .record-item {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    // padding: 7px 14px;
    background-color: #fff;
    .record-value {
      width: calc(100% - 30px);
      padding: 7px 14px;
      box-sizing: border-box;
      cursor: default;
    }
    .record-close {
      width: 30px;
      display: none;
      &:hover {
        cursor: pointer;
      }
    }
    &:hover {
      background-color: rgb(229, 229, 230);
    }
    &:hover .record-close {
      display: inline-block;
    }
  }
}
</style>
