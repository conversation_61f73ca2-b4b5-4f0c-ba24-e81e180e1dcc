<template>
  <!-- {{ recordList }} -->
  <div class="admin-search">
    <input
      type="text"
      name=""
      id=""
      v-model="adminValue"
      placeholder="请选择城市"
      @focus="showRecord()"
    />
    <a-icon
      @click="closeRecord()"
      :type="'down'"
      slot="suffix"
      :style="{ color: '#eaeaea' }"
    />
  </div>
</template>

<script>
import BaseWidget from '@/BaseWidget'
export default {
  name: 'AdminSearch',

  mixins: [BaseWidget],

  computed: {},

  props: {
    adminValue: { type: String }
  },

  data() {
    return {}
  },

  mounted() {},

  configLoaded() {},

  watch: {},

  methods: {
    closeRecord() {
      this.$emit('closeRecord')
    },
    showRecord() {
      this.$emit('showRecord')
    }
  }
}
</script>

<style lang="less" scoped>
@import '@/assets/styles/common.less';
.admin-widget {
  // width: 400px;

  font-size: 17px;
  .admin-search {
    display: flex;
    position: relative;
    align-items: center;
    padding: 7px 14px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #e7ebf4;
    height: 40px;
    input {
      flex: 1;
      outline: none;
      border: none;
    }
    icon {
      color: rgb(234, 234, 234);
    }
    .admin-record {
      top: 100%;
      left: 0;
      width: 100%;
      position: absolute;
      z-index: 999;

      border-radius: 4px;
      box-shadow: @boxShadow;
      .record-item {
        display: flex;
        align-items: center;
        box-sizing: border-box;
        // padding: 7px 14px;
        background-color: #fff;
        .record-value {
          width: calc(100% - 30px);
          padding: 7px 14px;
          box-sizing: border-box;
          cursor: default;
        }
        .record-close {
          width: 30px;
          display: none;
          &:hover {
            cursor: pointer;
          }
        }
        &:hover {
          background-color: rgb(229, 229, 230);
        }
        &:hover .record-close {
          display: inline-block;
        }
      }
    }
  }
}
</style>
