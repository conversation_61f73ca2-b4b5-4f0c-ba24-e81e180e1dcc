<template>
  <div class="admin-cont">
    <a-tabs
      default-active-key="0"
      :activeKey="tabActiveKey"
      @tabClick="tabChange"
    >
      <template v-for="(item, index) in adminList">
        <a-tab-pane
          :key="index"
          :tab="item.title"
          :disabled="!item.list.length"
        >
          <div class="tab-content">
            <template v-for="(adminItem, Itemindex) in item.list">
              <div
                :class="[
                  'admin-item',
                  getAdminName(adminItem, index) ==
                  (item.thisAdmin ? getAdminName(item.thisAdmin, index) : null)
                    ? 'active-admin-item'
                    : ''
                ]"
                :key="Itemindex"
                @click="handleAdminClick(adminItem, index)"
              >
                {{ getAdminName(adminItem, index) }}
              </div>
            </template>
          </div>
        </a-tab-pane>
      </template>
    </a-tabs>
    <div class="admin-control">
      <a-button class="admin-control-item btn" type="primary" @click="confirm">
        确定
      </a-button>
      <a-button class="admin-control-item" @click="reset"> 重置 </a-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AdminTabs',

  computed: {},

  props: {
    adminList: { type: Array },
    adminBase: { type: Array },
    baseLevel: { type: Number }
  },

  data() {
    return {
      showAdminCont: false,
      recordIsShow: false,
      tabActiveKey: 0,
      adminValue: '',
      hasDefaultProvince: null,
      recordList: [],
      leaveAdmin: false,
      inputFocused: false
    }
  },

  mounted() {
    // 监听点击事件判断是否点击非输入框范围
    document.addEventListener('click', () => {
      this.recordIsShow = false
      this.showAdminCont = false
    })
  },

  watch: {
  },
  methods: {
    handleContClick() {
      this.recordIsShow = false
    },
    handleClick(e) {
      e.stopPropagation()
    },
    handleSearchClick() {
      this.showAdminCont = true
      this.recordIsShow = true
    },
    tabChange(e) {
      this.tabActiveKey = e
    },
    getAdminName(item, level) {
      let nameKey = this.adminBase[level].nameKey
      return item[nameKey]
    },
    handleAdminClick(item, index) {
      if (this.tabActiveKey < this.baseLevel - 1) {
        this.tabActiveKey++
      }
      this.$emit('handleAdminClick', item, index)
    },
    reset() {
      this.tabActiveKey=0
      this.$emit('reset')
    },
    confirm() {
      this.$emit('confirm')
    }
  }
}
</script>

<style lang="less" scoped>
@import '@/assets/styles/common.less';
.admin-cont {
  margin-top: 10px;
  position: relative;
  // width: 400px;
  min-height: 250px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 100%;
  background-color: #fff;

  font-size: 17px;
  // padding: 10px;
  .tab-content {
    // display: flex;
    // justify-content: left;
    // flex-wrap: wrap;
    .admin-item {
      display: inline-block;
      margin: 5px 10px;
      color: @primaryColor;
      font-weight: 500;
      &:hover {
        cursor: pointer;
        text-decoration: underline;
        font-weight: 600;
      }
    }
    .active-admin-item {
      color: @primaryColor;
      text-decoration: underline;
      font-weight: 500;
    }
  }
  .admin-control {
    display: flex;
    margin-top: 10px;
    justify-content: center;

    .btn {
      background-color: @primaryColor;
      border-color: @primaryColor;
    }
    .admin-control-item {
      margin: 10px;
    }
  }
}
/deep/.ant-tabs-nav-scroll{
  text-align: center;
}
/deep/.ant-tabs-nav .ant-tabs-tab-active {
  color: #fff;
  background-color: @primaryColor;
  margin: 0 0 0 0;
  border-radius: 3px;
  padding: 5px 30px;
}
/deep/.ant-tabs-nav .ant-tabs-tab {
  padding: 5px 30px;
  border: 1px solid #e7ebf4;
  margin: 0;
}
/deep/.ant-tabs-ink-bar {
  background-color: @primaryColor;
  display: none;
  height: 0px;
}
</style>
