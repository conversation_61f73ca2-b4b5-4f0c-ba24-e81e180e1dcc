<template>
  <div
    @click="handleClick"
    :class="[
      'jimu-widget',
      showAdminCont ? 'jimu-tool-widget' : '',
      'admin-widget',
    ]"
  >
    <div class="search-wrap">
      <AdminSearch
        :adminValue="adminValue"
        @showRecord="handleSearchClick"
        @closeRecord="closeRecord"
      />
    </div>
    <div class="record-wrap">
      <AdminRecord
        v-show="showRecord"
        :recordList="recordList"
        @delRecord="delRecord"
        @handleRecordClick="handleRecordClick"
        :adminBase="adminBase"
        :baseLevel="baseLevel"
      />
    </div>
    <div class="tabs-wrap" @click="handleContClick">
      <AdminTabs
        ref="adminTabs"
        v-show="showAdminCont"
        :adminList="adminList"
        :adminBase="adminBase"
        :baseLevel="baseLevel"
        @handleAdminClick="handleAdminClick"
        @confirm="confirm"
        @reset="reset"
      />
    </div>
  </div>
</template>

<script>
import AdminSearch from "./component/AdminSearch.vue";
import AdminRecord from "./component/AdminRecord.vue";
import AdminTabs from "./component/AdminTabs.vue";
import { mapState, mapActions } from "vuex";
import BaseWidget from "@/BaseWidget";
import request from "@/apis/feature.js";

export default {
  components: { AdminTabs, AdminRecord, AdminSearch },
  name: "AdminWidget",

  mixins: [BaseWidget],

  computed: {
    // 从全局配置中获取api地址
    ...mapState({
      apiUrl: (state) => state.config.api,
    }),
    ...mapState(["data"]),
    getAdminValue() {
      let list = this.adminList
        .filter((item) => item.thisAdmin)
        .map((item, index) => {
          return Object.assign(item.thisAdmin, {
            name: item.thisAdmin[this.adminBase[index].nameKey],
            code: item.thisAdmin[this.adminBase[index].codeKey],
            bounds: [
              [item.thisAdmin.x_min, item.thisAdmin.y_min],
              [item.thisAdmin.x_max, item.thisAdmin.y_max],
            ],
          });
        });
      let obj = list[list.length - 1];
      obj.level = this.hasDefaultProvince ? list.length : list.length - 1;
      obj.region = this.adminBase[obj.level - 1].region
        ? this.adminBase[this.hasDefaultProvince ? obj.level - 1 : obj.level]
            .region
        : null;
      obj.nextRegion = this.adminBase[obj.level].region
        ? this.adminBase[this.hasDefaultProvince ? obj.level : obj.level - 1]
            .region
        : null;
      return obj;
    },
  },

  data() {
    return {
      showAdminCont: false,
      showRecord: false,
      baseLevel: 5,
      adminList: [],
      adminValue: "",
      hasDefaultProvince: null,
      adminBase: null,
      recordList: [],
      leaveAdmin: false,
      inputFocused: false,
      adminMap: {},
    };
  },

  mounted() {
    // 监听点击事件判断是否点击非输入框范围
    document.addEventListener("click", () => {
      this.showRecord = false;
      this.showAdminCont = false;
    });
    this.bus.$on("emit_admin_change", async (e) => {
      for (let i = 0; i < e.length; i++) {
        const data = this.adminList[e[i].index].list.find((item) => {
          return item[e[i].codeKey] == e[i].code;
        });
        await this.handleAdminClick(data, e[i].index);
      }
      // const data = this.adminList[e.index].list.find(item => {
      //   return item[e.codeKey] == e.code
      // })
      // await this.handleAdminClick(data, e.index)
      this.confirm();
    });
  },

  configLoaded() {
    this.configLoaded = true;

    if (this.config.levelConfig) {
      this.adminBase = this.config.levelConfig;
    }
    if (this.config.baseLevel) {
      this.baseLevel = this.config.baseLevel;
    }
    if (this.config.province) {
      this.hasDefaultProvince = this.config.province;
      this.adminBase.splice(0, 1);
    }
    this.initAdmin();
    this.getAdminList(
      0,
      this.hasDefaultProvince ? ["=", "PXZQDM", this.hasDefaultProvince] : null
    );
  },

  methods: {
    closeRecord() {
      this.showRecord = !this.showRecord;
    },
    handleContClick() {
      this.showRecord = false;
    },
    handleClick(e) {
      e.stopPropagation();
    },
    handleSearchClick() {
      this.showAdminCont = true;
      this.showRecord = true;
    },
    ...mapActions(["SET_DATA"]),
    getAdminName(item, level) {
      let nameKey = this.adminBase[level].nameKey;
      return item[nameKey];
    },
    async handleAdminClick(item, index) {
      let filter = this.getFilter(item, index);
      this.$set(this.adminList[index], "thisAdmin", item);
      if (index < this.baseLevel) {
        await this.getAdminList(index + 1, filter);
      }
      this.adminList.forEach((item, idx) => {
        if (idx - 1 > index) {
          item.list = [];
        }
        if (idx > index) {
          item.thisAdmin = null;
        }
      });
      let value = "";
      this.adminList.forEach((item, index) => {
        if (item.thisAdmin) {
          let name = item.thisAdmin[this.adminBase[index].nameKey];
          if (name) {
            value += name + "/";
          }
        }
      });
      this.adminValue = value;
    },
    reset() {
      this.adminList.forEach((item, index) => {
        item.thisAdmin = null;
        if (index > 0) {
          item.list = [];
        }
      });
      this.adminValue = "";
      this.SET_DATA({ key: "admin", value: null });
      this.setModuleData(this.$constants.shareDataKey.REGION_FILTER);
      this.bus.$emit(this.$events.admin.CHANGE_ADMIN);
      this.map.flyTo(this.initExtent);
    },
    initAdmin() {
      this.adminBase.forEach((item, index) => {
        if (index < this.baseLevel) {
          this.adminList.push({ title: item.title, list: [], thisAdmin: null });
        }
      });
    },
    confirm() {
      this.saveRecord();
      this.emitAdminData();
    },
    emitAdminData() {
      let admin = this.getAdminValue;
      this.SET_DATA({ key: "admin", value: admin });

      this.setModuleData(this.$constants.shareDataKey.REGION_FILTER, admin);
      this.bus.$emit(this.$events.admin.CHANGE_ADMIN, admin);
    },
    handleRecordClick(record) {
      this.adminValue = "";
      let value = "";
      record.forEach((item, index) => {
        let filter;
        filter = [
          "=",
          this.adminBase[index].codeKey,
          item[this.adminBase[index].codeKey],
        ];
        this.getAdminList(index + 1, filter);
        // this.handleAdminClick(item, index)

        value += item[this.adminBase[index].nameKey] + "/";
        this.$set(this.adminList[index], "thisAdmin", item);
        this.adminList;
      });
      let index = record.length - 1;
      this.adminList.forEach((item, idx) => {
        if (idx - 1 > index) {
          item.list = [];
        }
        if (idx > index) {
          item.thisAdmin = null;
        }
      });
      this.$refs.adminTabs.tabChange(index);
      this.adminValue = value;
      this.emitAdminData();
    },
    saveRecord() {
      let recordItem = this.adminList
        .filter((item) => {
          return item.thisAdmin;
        })
        .map((item) => {
          return item.thisAdmin;
        });
      if (recordItem.length) {
        if (
          JSON.stringify(this.recordList).indexOf(
            JSON.stringify(recordItem)
          ) === -1
        ) {
          this.recordList.unshift(recordItem);
          // arr.push(option) // 进行动态的操作
        }
      }
    },
    delRecord(delItem) {
      this.recordList = this.recordList.filter((item) => {
        return item !== delItem;
      });
    },
    async getAdminList(level, filter) {
      if (level == this.baseLevel) {
        return;
      }
      if (filter && this.adminMap[filter[2]]) {
        this.$set(this.adminList[level], "list", this.adminMap[filter[2]]);
      } else {
        let res = await request.query(this.adminBase[level].url, {
          filter: filter,
        });
        const list = res.features.map((item) => {
          return item.properties;
        });
        this.$set(this.adminList[level], "list", list);

        if (filter) {
          this.adminMap[filter[2]] = list;
        }
      }
    },
    getFilter(data, level) {
      let codeKey = this.adminBase[level].codeKey;
      let code = data[codeKey];
      return ["=", codeKey, code];
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";
.admin-widget {
  box-shadow: @boxShadow;
  font-size: 17px;
  z-index: 9999;
  .search-wrap {
    width: 100%;
  }

  .record-wrap {
    width: 100%;
    position: relative;
  }
  .cont-wrap {
    width: 100%;
  }
}
</style>
