<template>
  <div class="chart-widget">
    <Echart :options="options" />
  </div>
</template>

<script>
import FeatureApi from '@/apis/feature'
import BaseWidget from '@/BaseWidget'
import Echart from '@/components/Chart'
import { mapState } from 'vuex'
export default {
  name: 'PieChartWidget',

  mixins: [BaseWidget],

  components: { Echart },

  data() {
    return {
      param: null,
      geometry: null
    }
  },

  props: {},

  computed: {
    ...mapState({
      adminData: state => state.data?.admin,
      apiUrl: state => state.config.api
    }),
    options() {
      let data
      if (this.param) {
        data = {
          legend: {},
          tooltip: {
            confine: true,
            trigger: 'item',
            borderColor: '#fff'
          },
          title: this.param.map((item, index) => {
            return {
              subtext: item.name,
              top: '25%',
              left:
                parseInt(100 / (this.param.length * 2)) * (2 * index + 1) + '%',
              textAlign: 'center'
            }
          }),
          series: this.param.map((item, index) => {
            return {
              name: item.name,
              type: 'pie',
              radius: this.param.length > 2 ? '33%' : '50%',
              center: [
                parseInt(100 / (this.param.length * 2)) * (2 * index + 1) + '%',
                '60%'
              ],
              data: item.children.map(c => {
                return {
                  value: c.value,
                  name: c.name,
                  itemStyle: {
                    color: c.color
                  }
                }
              }),
              label: {
                show: false,
                position: 'center'
              },
              labelLine: {
                show: false
              }
            }
          })
        }
      } else {
        data = {
          tooltip: {
            trigger: 'item'
          },
          series: []
        }
      }
      if (this.dataConfig.unit || this.dataConfig.decimals) {
        let _this = this
        data.tooltip.formatter = function (params) {
          const { name, value, marker, seriesName } = params
          let relVal = seriesName + '<br/>' + marker + name + ' : '
          if (_this.dataConfig.decimals) {
            relVal += Number(value).toFixed(_this.dataConfig.decimals)
          } else {
            relVal += value
          }
          if (_this.dataConfig.unit) {
            relVal += _this.dataConfig.unit
          }
          return relVal
        }
      }
      return data
    }
  },

  async mounted() {
    this.geometry = await this.getModuleData(
      this.$constants.shareDataKey.GEOMETRY_FILTER
    )
    this.bus.$on(
      this.$events.map.GEOMETRY_FILTER_UPDATE,
      this.geometryFilterUpdateHandler
    )
    this.getdata(this.dataConfig)
  },

  watch: {
    adminData() {
      this.getdata(this.dataConfig)
    },
  },

  methods: {
    geometryFilterUpdateHandler(data) {
      this.geometry = data
      this.getdata(this.dataConfig)
    },
    async getdata(config) {
      let list = ['PXZQDM', 'CXZQDM', 'FXZQDM', 'TXZQDM', 'XZQDM']
      let filters = ['all']
      let geometry
      if (this.adminData) {
        filters.push(['=', list[this.adminData.level], this.adminData.code])
      }
      if (this.geometry) {
        geometry = this.geometry.geometry
      }

      let resContent = []
      config.children.forEach(item => {
        let aggregates = [[item.aggField, item.aggMethod]]
        let groupFields
        let filter = this.$utils.object.deepCloneJson(filters)
        if (item.groupField) {
          groupFields = [item.groupField]
        }
        if (item.filter) {
          filter.push(item.filter)
        }
        if (filter.length == 2) {
          filter = filter[1]
        }
        if (filter.length == 1) {
          filter = null
        }
        let res = FeatureApi.aggregate(this.apiUrl.feature, this.layerName, {
          filter,
          geometry,
          aggregates,
          groupFields
        })
        resContent.push(res)
      })
      let result = await Promise.all(resContent)
      let data = this.$utils.object.deepCloneJson(config.children)
      data = data.map(item => {
        return {
          name: item.label,
          children: config.category.map(i => {
            return { name: i.name, value: null, color: i.color, type: i.value }
          })
        }
      })
      result.forEach((item, index) => {
        data[index].children.forEach(d => {
          let a = item.find(i => {
            return d.type == i[config.children[index].groupField]
          })
          if (a) {
            d.value =
              a[
                config.children[index].aggField +
                  '_' +
                  config.children[index].aggMethod
              ]
          } else {
            d.value = 0
          }
        })
      })
      this.param = data
    }
  }
}
</script>

<style lang="less" scope>
.chart-widget {
  width: 100%;
  height: 100%;
}
.chart-widget-box {
  width: 100%;
  height: 100%;
  display: flex;
}
</style>
