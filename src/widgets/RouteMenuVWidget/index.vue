<template>
  <div class="jimu-widget route-menu-widget menu-row-style" ref="menu">
    <router-link v-for="(item, index) in menuItems" :to="item.path" :key="index">
      <div class="aside"
          @click="checkedStyle(index)"
          :class="{'checked':index == checked}"
          @mouseenter="setBackgroundColor"
          @mouseleave="removeBackgroundColor"
          >
          <span class="gt-icon icon"
          :style="`color:${config.iconcolor};
                   fontSize:${config.iconsize}`"
          :class="menu.children[index].icon">
          </span>
        <span>{{ item.name }}</span>
      </div>
    </router-link>
  </div>
</template>

<script>

import { mapState } from 'vuex'
import BaseWidget from '@/BaseWidget'

export default {

  name: 'RouteMenuVWidget',

  data(){
    return {
      checked:null,
      menu:null
    }
  },
  mixins: [BaseWidget],

  computed: {
    ...mapState({
      menuItems: state => state.routes,
      icon: state=>state.config
    })
  },
  methods:{
    setBackgroundColor(e){
      if(!this.config.hovercolor) return
      e.target.style.backgroundColor = this.config.hovercolor
    },
    removeBackgroundColor(e){
      if(!this.config.hovercolor) return
      e.target.style.backgroundColor = ''
    },
    checkedStyle(index){
      this.checked = index
    },
    getMenuConfig(){
      this.icon.children.forEach(item=>{
        if(item.uri == "layouts/RouterLayout"){
          this.menu = item
        }
      })
    },
    menuPosition(){
      const width = this.$refs.menu.offsetWidth
      this.$refs.menu.style.transform = `translateX(${-(width/2)}px)`
    }

  },
  async configLoaded(){
    await this.getMenuConfig()
    this.menuPosition()

  },
  mounted(){

  },
  mapLoaded(){
    this.checked += 0
  },
  beforeDestroy() {
    this.checked = null
  },

}
</script>

<style lang="less" scoped>
@asidewidth:74px;
@import '@/assets/styles/common.less';

.menu-row-style{
    height: @asidewidth;
    margin-left: 50%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
  }
.route-menu-widget{
  color: #ffffff;
  padding: 0px;

  a{
    color: #ffffff;
    text-decoration: none;
    padding: 4px;
  }

  .aside{
    width: @asidewidth;
    height: @asidewidth;
    background: rgba(38, 63, 161,0);
    margin-left: ((84-@asidewidth)/2);
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    .logo{
      height: 40px;
      width: 40px;
      border-radius: 100px;
      text-align: center;
      line-height: 40px;
      .icon{
        font-size: 3.7vh;
      }
    }
  }
  .checked{
      background-color: rgb(18, 192, 223);
      color: #263fa1 ;
      .icon{
        color: #263fa1 !important
      }
    }
}

</style>
