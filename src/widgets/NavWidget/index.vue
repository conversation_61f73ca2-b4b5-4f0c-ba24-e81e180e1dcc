<template>
  <div class="menu-row-style" ref="menu">
    <template v-for="(item, index) in menu">
      <template v-if="item.children">
        <div
          class="aside"
          :key="index"
          @click="checkedStyle(index)"
          :class="{ checked: setCheckout(item) }"
          :to="item.path"
        >
          <div class="contain">
            <div>
              <span class="tool-item gt-icon icon" :class="item.icon" />
            </div>
            <span>{{ item.name }}</span>
          </div>
          <div class="menu-wrap">
            <router-link
              class="menu"
              :to="menu.path"
              v-for="(menu, index) in item.children"
              :key="index"
            >
              {{ menu.name }}
            </router-link>
          </div>
        </div>
      </template>
      <template v-else>
        <router-link
          class="aside"
          :key="index"
          @click="checkedStyle(index)"
          :class="{ checked: setCheckout(item) }"
          :to="item.path"
        >
          <div class="contain">
            <div>
              <span class="tool-item gt-icon icon" :class="item.icon" />
            </div>
            <span>{{ item.name }}</span>
          </div>
        </router-link>
      </template>
    </template>
    <!-- <router-link
      class="aside"
      v-for="(item, index) in menu"
      :key="index"
      @click="checkedStyle(index)"
      :class="{ checked: setCheckout(item) }"
      :to="item.path"
    >
      <div class="contain">
        <div>
          <svg-icon class="svg" :icon-class="item.svg"></svg-icon>
        </div>
        <span>{{ item.name }}</span>
      </div>
      <router-link
        class="menu"
        :to="menu.path"
        v-for="(menu, index) in item.children"
        :key="index"
      >
        {{ menu.name }}
      </router-link>
    </router-link> -->
  </div>
</template>

<script>
import { mapState } from 'vuex'
import BaseWidget from '@/BaseWidget'

export default {
  name: 'NavWidget',

  data() {
    return {
      checked: null,
      menu: null
    }
  },
  mixins: [BaseWidget],

  computed: {
    ...mapState({
      menuItems: state => state.config.children,
      thisModule: state => state.thisModule
    })
  },
  methods: {
    setCheckout(item) {
      let res = false
      if (item.children) {
        const data = item.children.find(item => {
          return item.name == this.thisModule
        })
        if (data) {
          res = true
        }
      } else {
        if (item.name == this.thisModule) {
          res = true
        }
      }
      return res
    },
    mouseenter: function (index) {
      this.menu[index].seen = !this.menu[index].seen
    },
    mouseleave: function (index) {
      this.menu[index].seen = !this.menu[index].seen
    },
    checkedStyle(index) {
      this.checked = index
    },
    getMenuConfig() {
      this.menuItems.forEach(item => {
        if (item.uri == 'widgets/NavWidget') {
          this.menu = item.children
          this.menu.forEach(item => {
            this.$set(item, 'seen', false)
          })
        }
      })
    }
  },
  async configLoaded() {
    await this.getMenuConfig()
  },
  mounted() {},
  mapLoaded() {
    this.checked += 0
  },
  beforeDestroy() {
    this.checked = null
  }
}
</script>

<style lang="less" scoped>
// @asidewidth:74px;
@import '@/assets/styles/common.less';
.menu-row-style {
  position: absolute;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.aside {
  height: 100%;
  padding: 0px 15px;
  .contain {
    height: 100%;
    display: flex;
    align-items: center;
    cursor: pointer;
    z-index: 9999;
    .icon {
      font-size: 40px;
      color: white;
    }
    span {
      margin-left: 5px;
      font-size: 21px;
      font-weight: 500;
      color: white;
      white-space: nowrap;
    }
  }
  .menu-wrap {
    position: absolute;
    z-index: 99999;
    width: 165px;
  }
  .menu {
    display: none;
    height: 50px;
    background: @primaryColor;
    width: 100%;
    text-align: center;
    line-height: 50px;
    margin-left: -15px;
    font-size: 21px;
    font-weight: 550;
    color: #fff;
    &:hover {
      color: #fff;
      background-color: #639cf1;
    }
  }
  &:hover .menu {
    display: block;
    z-index: 99999;
  }
}
.checked {
  background-color: rgba(0, 0, 0, 0.1);
  border-bottom: 4px solid #00ffcf;
  .contain {
    div {
      .icon {
        color: #00ffcf;
      }
    }
    span {
      color: #00ffcf;
    }
  }
}
</style>
