<template>
  <div class="jimu-widget route-menu-widget" v-if="menuList.length > 1">
    <router-link v-for="(item, index) in menuList" :to="item.path" :key="index">
      <div class="aside" :class="{ checked: item.path == $route.path }">
        <span
          v-if="item.icon"
          class="gt-icon icon"
          :title="item.name"
          :class="item.icon"
          :style="`color:${config.iconcolor};
                   fontSize:${config.iconsize}`"
        >
        </span>
        <span>{{ item.name }}</span>
      </div>
    </router-link>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import BaseWidget from '@/BaseWidget'
import { mapMutations } from 'vuex'

export default {
  name: 'RouteMenuHWidget',
  data() {
    return {
      checked: null,
      menu: null,
      hovercolor: null
    }
  },
  mixins: [BaseWidget],
  watch: {
    $route: {
      handler(to) {
        const obj = this.menu.children.find(item => {
          return item.path == to.path
        })
        this._setModuleName(obj.moduleName)
        // )
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    ...mapState({
      menuItems: state => state.routes,
      icon: state => state.config,
      thisModule: state => state.thisModule
    }),
    menuList() {
      if (this.menu && this.thisModule) {
        return this.menu.children.filter(item => {
          return item.moduleName == this.thisModule
        })
      } else {
        return []
      }
    }
  },
  methods: {
    ...mapMutations({
      _setModuleName: 'setModuleName'
    }),
    getMenuConfig() {
      this.icon.children.forEach(item => {
        if (item.uri == 'layouts/RouterLayout') {
          this.menu = item
        }
      })
    }
  },

  configLoaded() {
    this.getMenuConfig()
    this.hovercolor = this.config.hovercolor
    this.hovericoncolor = this.config.hovericoncolor
    this.hoverspancolor = this.config.hoverspancolor
    const obj = this.menu.children.find(item => {
      return item.path == this.$route.path
    })
    this._setModuleName(obj.moduleName)
  },
  mapLoaded() {
    this.checked += 0
  },
  beforeDestroy() {
    this.checked = null
  }
}
</script>

<style lang="less" scoped>
@import '@/assets/styles/common.less';
.route-menu-widget {
  width: 84px;
  background: #ffffff;
  padding-top: 30px;
  font-size: 17px;
  font-weight: 450;
  a {
    color: #ffffff;
    text-decoration: none;
  }

  .aside {
    width: 100px;
    height: 100px;
    color: @primaryTextColor;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    padding: 10px 10px;
    div {
      text-align: center;
    }

    span {
      text-align: center;
    }
  }
}
.checked {
  background: #eef0f9;
  border-left: 4px solid @primaryColor;
  .icon {
    color: @primaryColor !important;
  }
  span {
    color: @primaryColor;
  }
}
</style>
