<template>
  <div class="jimu-widget jimu-tool-widget draw-widget" v-show="isLoaded">
    <div
      :class="['tool-item', activeTool === item ? 'is-active' : '']"
      v-for="(item, index) in tools"
      :key="index"
      @click="onToolChange(item)"
      :title="item.label"
    >
      <span :class="['gt-icon', 'gt-icon-' + item]" />
    </div>
  </div>
</template>

<script>

import { uid } from 'uid'
import DrawTool from '@/components/DrawTool'
import BaseWidget from '@/BaseWidget'

// 默认工具
const DEFAULT_TOOLS = [
  'point',
  'line',
  'polygon',
  'rectangle',
  'circle',
  'clear'
]
const DEFAULT_STYLE = {
  circle: {
    'circle-radius': 3,
    'circle-color': '#ff0000'
  },
  line: {
    'line-color': '#ff0000'
  },
  fill: {
    'fill-color': '#ff0000',
    'fill-opacity': 0.3
  }
}
const DEFAULT_SOURCE = {
  type: 'geojson',
  data: {
    type: 'Feature',
    geometry: {
      type: 'Polygon',
      coordinates: []
    },
    properties: {}
  }
}
const DRAW_LAYER_ID = 'draw_layer' // 绘制图层id

export default {
  name: 'DrawWidget',

  data() {
    return {
      isLoaded: null,
      tools: DEFAULT_TOOLS,
      style: DEFAULT_STYLE,
      activeTool: null,
      layerId: null,
      sourceId: DRAW_LAYER_ID + uid(),
      drawType: 'fill'
    }
  },

  mixins: [BaseWidget],

  mounted() {},

  allLoaded() {
    // window.map = this.map
    this.tools = this.config.tools
    this.isLoaded = true
    this.style = Object.assign(this.style, this.config.style)
    this.initDraw()
    this.map.addSource(this.sourceId, DEFAULT_SOURCE)
  },

  methods: {

    // 初始化绘制工具
    async initDraw() {
      this.draw = new DrawTool(this.map)
      this.draw.on('drawEnd', feature => {
        this.draw.deactivate()
        this.setGeometry(feature)
        // 模块内共享数据
        this.setModuleData(this.$constants.shareDataKey.GEOMETRY_FILTER, feature)
        this.bus.$emit(this.$events.map.GEOMETRY_FILTER_UPDATE, feature)
      })
    },
    // 绘制geometry图层
    setGeometry(feature) {
      this.map.getSource(this.sourceId).setData(feature)
    },
    clearDrawLayer() {
      if (this.layerId) {
        this.map.getSource(this.sourceId).setData(DEFAULT_SOURCE.data)
        this.map.removeLayer(this.layerId)
        this.layerId = null
      }
    },
    onToolChange(item) {
      this.clearDrawLayer()
      if (item == 'clear') {
        this.activeTool = ''
        this.setModuleData(this.$constants.shareDataKey.GEOMETRY_FILTER)
        this.bus.$emit(this.$events.map.GEOMETRY_FILTER_UPDATE)
      } else {
        this.activeTool = item
        this.addDrawLayer()
        this.draw.activate(item)
      }
    },
    addDrawLayer(item) {
      if (item == 'point') {
        this.drawType = 'circle'
      } else if (['polygon', 'rectangle', 'circle'].includes(item)) {
        this.drawType = 'fill'
      } else if (item == 'line') {
        this.drawType = 'line'
      }
      let id = DRAW_LAYER_ID + '_' + uid()
      this.map.addLayer({
        id: id,
        source: this.sourceId,
        type: this.drawType,
        paint: this.style[this.drawType]
      })
      this.layerId = id
    }
  }
}
</script>

<style lang="less" scoped>
@import '@/assets/styles/common.less';
.draw-widget {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4px;
  .tool-item {
    font-size: 24px;
    padding: 0 10px;
    color: #000;
    cursor: pointer;
    border: 1px solid transparent;
    border-right: 1px solid #eaeaea;
    color: #8a8a8a;
    .gt-icon {
      font-size: 24px;
    }
    &:hover {
      color: @primaryColor;
    }
  }

  .is-active {
    color: @primaryColor;
  }

  .tool-item:last-child {
    border-right: none;
  }
}
</style>
