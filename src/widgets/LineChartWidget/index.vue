<template>
  <div class="chart-widget">
    <Echart :options="options" />
  </div>
</template>

<script>
import FeatureApi from '@/apis/feature'
import BaseWidget from '@/BaseWidget'
import Echart from '@/components/Chart'
import { mapState } from 'vuex'
export default {
  name: 'LineChartWidget',

  mixins: [BaseWidget],

  components: { Echart },

  data() {
    return {
      param: null,
      geometry: null
    }
  },

  props: {},

  computed: {
    ...mapState({
      adminData: state => state.data?.admin,
      apiUrl: state => state.config.api
    }),
    options() {
      let data
      if (this.param) {
        data = {
          legend: {},
          tooltip: {
            confine: true,
            trigger: 'axis',
            axisPointer: {
              // Use axis to trigger tooltip
              type: 'shadow' // 'shadow' as default; can also be 'line' or 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            // top:'3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: this.dataConfig.children.map(item => item.label)
          },
          yAxis: {
            type: 'value'
          },
          series: this.param.map(item => {
            return {
              name: item.name,
              data: item.data,
              type: 'line',
              showBackground: true,
              stack: 'total',
              itemStyle: {
                color: item.color
              }
            }
          })
        }
      } else {
        data = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              // Use axis to trigger tooltip
              type: 'shadow' // 'shadow' as default; can also be 'line' or 'shadow'
            }
          },
          xAxis: {
            type: 'category',
            data: []
          },
          yAxis: {
            type: 'value'
          },
          series: []
        }
      }
      if (this.dataConfig.unit || this.dataConfig.decimals) {
        let _this = this
        data.tooltip.formatter = function (params) {
          var relVal = params[0].name
          for (var i = 0, l = params.length; i < l; i++) {
            relVal += '<br/>' + params[i].marker + params[i].seriesName + ' : '
            if (_this.dataConfig.decimals) {
              relVal += Number(params[i].value).toFixed(
                _this.dataConfig.decimals
              )
            } else {
              relVal += params[i].value
            }
            if (_this.dataConfig.unit) {
              relVal += _this.dataConfig.unit
            }
          }
          return relVal
        }
      }
      return data
    }
  },

  async mounted() {
    this.geometry = await this.getModuleData(
      this.$constants.shareDataKey.GEOMETRY_FILTER
    )
    this.bus.$on(
      this.$events.map.GEOMETRY_FILTER_UPDATE,
      this.geometryFilterUpdateHandler
    )
    this.getdata(this.dataConfig)
  },

  watch: {
    adminData() {
      this.getdata(this.dataConfig)
    }
  },

  methods: {
    geometryFilterUpdateHandler(data) {
      this.geometry = data
      this.getdata(this.dataConfig)
    },
    async getdata(config) {
      let list = ['PXZQDM', 'CXZQDM', 'FXZQDM', 'TXZQDM', 'XZQDM']
      let filter
      let geometry
      if (this.adminData) {
        filter = ['=', list[this.adminData.level], this.adminData.code]
      }
      if (this.geometry) {
        geometry = this.geometry.geometry
      }

      let resContent = []
      config.children.forEach(item => {
        let aggregates = [[item.aggField, config.aggMethod]]
        let groupFields
        if (item.groupField) {
          groupFields = [item.groupField]
        }
        let res = FeatureApi.aggregate(this.apiUrl.feature, this.layerName, {
          filter,
          geometry,
          aggregates,
          groupFields
        })
        resContent.push(res)
      })
      let result = await Promise.all(resContent)

      let data = this.$utils.object.deepCloneJson(config.category)
      data.map(item => {
        return Object.assign(item, { data: [] })
      })
      result.forEach((item, index) => {
        if (config.children[index].groupField) {
          data.forEach(d => {
            let a = item.find(i => {
              return d.value == i[config.children[index].groupField]
            })
            if (a) {
              d.data.push(
                a[config.children[index].aggField + '_' + config.aggMethod]
              )
            } else {
              d.data.push(0)
            }
          })
        } else {
          data.forEach(d => {
            d.data.push(
              item[0][config.children[index].aggField + '_' + config.aggMethod]
            )
          })
        }
      })
      this.param = data
    }
  }
}
</script>

<style lang="less" scope>
.chart-widget {
  width: 100%;
  height: 100%;
}
.chart-widget-box {
  width: 100%;
  height: 100%;
  display: flex;
}
</style>
