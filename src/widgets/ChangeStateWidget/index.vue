<template>
  <div class="jimu-widget jimu-tool-widget toggle"
      :class="changeStyle"
       @click="clickHandler" >
    <span class="gt-icon gt-icon-bars bars"></span>
  </div>
</template>

<script>

import BaseWidget from '@/BaseWidget'


export default {

  name: 'ChangeStateWidget',

  mixins: [BaseWidget],

  data(){
    return {  
      stateIndex:0
    }
  },
 
  methods:{
    clickHandler(){
      this.stateIndex++
      if(this.stateIndex>=this.config.states.length){
        this.stateIndex = 0
      }
      const state = this.config.states[this.stateIndex]
      this.bus.$emit(this.$events.state.STATE_UPDATE,state)
    }
  },

  computed:{
    changeStyle(){
      return this.stateIndex?'hidden-state':'show-state'
    }
  }
}
</script>

<style lang="less" scoped>

@import '@/assets/styles/common.less';
  .icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
  } 
  .toggle{
    height: 40px;
    width: 40px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .show-state{
      color: rgb(255, 255, 255);
      background-color: rgb(72, 95, 176);
  }
  .hidden-state{
    color: #8a8a8a;
    background-color: rgb(255, 255, 255);
  }
  .bars{
    font-size: 20px;
  }

</style>
