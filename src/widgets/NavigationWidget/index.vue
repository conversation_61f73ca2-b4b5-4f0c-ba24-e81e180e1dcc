<template>
  <div class="jimu-widget jimu-tool-widget navigation-widget">
    <span class="tool-item gt-icon gt-icon-home" @click="zoomInit" />
    <span class="tool-item gt-icon gt-icon-plus" @click="zoomIn" />
    <span class="tool-item gt-icon gt-icon-minus" @click="zoomOut" />
  </div>
</template>

<script>

import BaseWidget from '@/BaseWidget'
import { mapState } from 'vuex'

const DEFATULT_INIT = {
  zoom: 3,
  center: [108, 23]
};

export default {

  name: 'NavigationWidget',

  mixins: [BaseWidget],

  computed: {
    // 从全局配置中获取地图初始状态
    ...mapState({
      initExtent: state => ({
        zoom: state.config.map?.initZoom,
        center: state.config.map?.initCenter
      })
    })

  },

  methods:{

    zoomInit() {
      this.map.flyTo({
        zoom: this.initExtent.zoom || DEFATULT_INIT.zoom,
        center: this.initExtent.center || DEFATULT_INIT.center,
      });
    },

    zoomIn() {
      if (!this.map) return;
      this.map.zoomIn();
    },

    zoomOut() {
      if (!this.map) return;
      this.map.zoomOut();
    },
  }
}
</script>

<style lang="less" scoped>

@import '@/assets/styles/common.less';

.navigation-widget{

  width: 40px;
  padding: 4px;

  .tool-item {
    display: block;
    text-align: center;
    line-height: 40px;
    font-size: 24px;
    color: #8a8a8a;

    &:hover {
      cursor: pointer;
      color: @primaryColor;
    }
  }
}
</style>
