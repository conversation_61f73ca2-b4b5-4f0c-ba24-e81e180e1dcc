<template>
  <div class="jimu-widget">
    <div class="jimu-tool-widget small-legend" v-show="!show" @click="closeLegend">
      图例
    </div>
    <div class="jimu-tool-widget big-legend" v-show="show">
      <div class="legend-top">
        <span class="gt-icon gt-icon-close" @click="closeLegend"></span>
      </div>
      <div class="legend-content">
        <ul v-for="(legend,index) in legends" :key="index" class="legend">
          <p class="legend-title">{{legend.legend.title}}</p>
          <li v-for="(style,index) in legend.legend.items " :key="index" style="margin:0">
            <span v-if="style.symbol.icon"></span>
            <img v-if="style.symbol.img" :src="style.symbol.img" alt="" style="width:20px;margin-right:15px">
            <span v-else
              class="legend-icon"
              :class="`legend-icon-${legend.legend.type}`"
              :style="`background-color:${style.symbol.color}`"></span>
            <span>{{labelFormat(style.label,legend.legend.decimal,legend.legend.percentage)}}</span>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>

import BaseWidget from '@/BaseWidget'

export default {

  name: 'LegendWidget',

  mixins: [BaseWidget],

  data(){
    return {
      show:false,
      legendMap:{},
      legends:[]
    }
  },

  computed:{
    labelFormat(){
      return function(number, decimal, percentage){
        if(typeof number == 'object'){
          return `${this.$utils.number.format(number.min, decimal, percentage)} ~
                  ${this.$utils.number.format(number.max, decimal, percentage)}`
        }
        return this.$utils.number.format(number, decimal, percentage)
      }
    }
  },

  methods:{
    closeLegend(){
      this.show = !this.show
    },
    showLegends(legend){
      this.legendMap[legend.key] = legend
      const legendss = Object.values(this.legendMap)
      this.legends = legendss.sort((a,b)=>{
        return a.orderWeight-b.orderWeight
      })
      this.show = true
    },

    hideLegends(closekey){
      delete this.legendMap[closekey]
      const legends = Object.values(this.legendMap)
      this.legends = legends.sort((a,b)=>{
        return a.orderWeight-b.orderWeight
      })
      if(this.legends.length == 0) this.show = !this.show
    },
   
  },
  mounted(){
    this.bus.$on(this.$events.map.LEGEND_SHOW,this.showLegends)
    this.bus.$on(this.$events.map.LEGEND_HIDE,this.hideLegends)
  }
}
</script>

<style lang="less" scoped>

ul{
  list-style: none;
}
.small-legend{
  cursor: pointer;
  font-size: 18px;
  font-weight: 700;
}
.big-legend{
  width: 100%;
  .legend-top{
    position: absolute;
    top: 1%;
    right: 1%;
    .gt-icon-close{
      float:right;
      cursor: pointer;
    }
  }
  .legend-content{
    padding: 0px 0px 0px 20px;
    max-height: 500px;
    overflow-y: auto;
    overflow-x: hidden;
    width: 100%;
    margin-top: 8px;
    font-size: 17px;
    .legend{
      width: 100%;
      margin-bottom:5px;
      padding: 0px;
      .legend-title{
        font-size:17px;
        font-weight: 900;
        color: black;
        margin-bottom:5px;
        transform: translateX(-8px);
      }
      .legend-icon{
        display: inline-block;
        margin-right: 20px;
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 24%);
        transform: translateY(3px);
        line-height:20px;
      }
      .legend-icon-point{
        height: 15px;
        width: 15px;
        border-radius: 100%;
        border: 1px solid white;
      }
      .legend-icon-polygon{
        height: 18px;
        width: 18px;
        border: 1px solid white;
      }
      .legend-icon-line{
        height: 5px;
        width: 18px;
        transform: translateY(-2.5px);
      }
    }
  }
}
</style>
