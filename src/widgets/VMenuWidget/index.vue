<template>
  <div
    class="jimu-widget jimu-tool-widget v-menu-widget"
    v-show="items.length > 1"
  >
    <a-menu v-model="currentMenuItem">
      <a-menu-item v-for="item in items" :key="item.key">
        <router-link :to="item.path">
          <span class="menu-icon gt-icon" :class="item.icon" />
          <p class="menu-title">{{ item.title }}</p>
        </router-link>
      </a-menu-item>
    </a-menu>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";

export default {
  name: "VMenuWidget",

  mixins: [BaseWidget],

  data() {
    return {
      currentMenuItem: [],
      items: [],
    };
  },

  created() {
    this.bus.$on(this.$events.menu.MENU_CHANGE, this.menuChangeHandler);
  },

  methods: {
    menuChangeHandler(items) {
      this.items = items;
      const path = this.$route.path;
      this.currentMenuItem = [path];
    },
  },
};
</script>

<style lang="less" scoped>
.v-menu-widget {
  width: 100%;
  height: 100%;
  padding: 24px 0;
  border-radius: 0;

  .ant-menu-vertical {
    border-right: none;
  }

  .ant-menu-vertical > .ant-menu-item {
    height: auto;
    padding: 4px 12px 8px 12px;
    font-size: 16px;
    height: 100px;
    display: table;
    width: 100%;
    margin: 0;
    border-left: solid 3px #4065e000;
  }

  .ant-menu .ant-menu-item-selected,
  .ant-menu .ant-menu-item-active {
    background-color: #4065e022;
    border-left: solid 3px #4065e0;

    a {
      color: #4065e0;
      font-weight: 500;
    }
  }

  .ant-menu-item > a {
    color: #002159;
    display: table-cell;
    vertical-align: middle;
  }

  .menu-icon {
    font-size: 28px;
    display: block;
    width: 100%;
    text-align: center;
  }

  .menu-title {
    text-align: center;
    width: 100%;
    white-space: normal;
    line-height: 22px;
    margin-bottom: 0;
  }
}
</style>
