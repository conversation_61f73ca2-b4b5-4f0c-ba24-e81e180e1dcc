<template>
  <div class="jimu-widget h-menu-widget">
    <a-menu mode="horizontal" v-model="currentMenuItem">
      <template v-for="item in menuItems">
        <a-menu-item
          v-if="!item.children"
          :key="item.key"
          @click="clickHandler(item)"
        >
          <router-link :to="item.path">
            <span class="menu-icon gt-icon" :class="item.icon" />
            <span>{{ item.title }}</span>
          </router-link>
        </a-menu-item>
        <a-sub-menu v-else :key="item.key">
          <span slot="title">
            <span class="menu-icon gt-icon" :class="item.icon" /><span>{{
              item.title
            }}</span>
          </span>
          <a-menu-item
            v-for="subItem in item.children"
            :key="subItem.key"
            @click="clickHandler(subItem)"
          >
            <router-link :to="subItem.path">
              <!--span class="menu-icon gt-icon" :class="subItem.icon"/-->
              <span>{{ subItem.title }}</span>
            </router-link>
          </a-menu-item>
        </a-sub-menu>
      </template>
    </a-menu>
  </div>
</template>

<script>
import { mapState } from "vuex";

import BaseWidget from "@/BaseWidget";

export default {
  name: "HMenuWidget",

  mixins: [BaseWidget],

  data() {
    return {
      currentMenuItem: [],
    };
  },

  computed: {
    ...mapState({
      menuItems: (state) => state.menu,
    }),
  },

  mounted() {
    const path = this.$route.path;
    this.currentMenuItem = [path];
    // 遍历寻找当前选中的菜单
    if (!this.menuItems) return;
    this.findCurrentMenu(this.menuItems);
  },

  methods: {
    clickHandler(item) {
      console.log(item);
      this.currentMenuItem = [item.key];
      this.bus.$emit(this.$events.menu.MENU_CHANGE, item.modules);
    },

    findCurrentMenu(items) {
      if (!items) return;
      for (let item of items) {
        if (item.key === this.currentMenuItem[0]) {
          this.clickHandler(item);
          break;
        }
        if (item.modules) {
          for (let module of item.modules) {
            if (module.key === this.currentMenuItem[0]) {
              this.clickHandler(item);
              break;
            }
          }
        }
        if (item.children) {
          this.findCurrentMenu(item.children);
        }
      }
    },
  },
};
</script>

<style lang="less" scoped>
.h-menu-widget {
  height: 90px;
  // text-align: right;
  // margin-right: 5%;
  margin-left: 50%;
  // display: flex;
  // align-items: center;
  // justify-content: flex-end;
  /deep/ .ant-menu {
    display: flex;
    justify-content: space-between;
  }

  /deep/ .ant-menu-horizontal {
    border-bottom: none;
    line-height: 87px;
  }

  /deep/ .ant-menu-horizontal > .ant-menu-item,
  /deep/ .ant-menu-horizontal > .ant-menu-submenu {
    border-bottom: 3px solid #00ffcf00;
    top: -4px;
    padding-top: 3px;
  }

  /deep/ .ant-menu-horizontal > .ant-menu-item-selected,
  /deep/ .ant-menu-horizontal > .ant-menu-item-open,
  /deep/ .ant-menu-horizontal > .ant-menu-item-active {
    border-bottom: 3px solid #00ffcf;
    color: #ffffff;
    font-weight: 500;
    background-color: #00ffcf22;
  }

  /deep/ .ant-menu {
    background: transparent;
    color: #ffffff;
    font-size: 21px;
  }

  /deep/ .ant-menu-submenu-title {
    border-bottom: 3px solid #00ffcf00;
    line-height: 80px;

    &:hover {
      color: #ffffff;
    }
  }

  /deep/ .ant-menu-horizontal > .ant-menu-submenu-selected,
  /deep/ .ant-menu-horizontal > .ant-menu-submenu-active {
    border-bottom: 3px solid #00ffcf;
    color: #ffffff;
    font-weight: 500;
    background-color: #00ffcf22;
  }

  /deep/ .gt-icon.menu-icon {
    padding-right: 4px;
    font-size: 24px;
  }

  .ant-menu-horizontal > .ant-menu-item > a {
    color: #ffffff;
  }
}
</style>

<style lang="less">
.ant-menu-vertical .ant-menu-item {
  font-size: 18px;

  .gt-icon {
    font-size: 18px;
  }
}

.ant-menu-vertical .ant-menu-submenu-title {
  font-size: 18px;
}

.ant-menu-submenu > .ant-menu {
  background-color: #4065e0;
  color: #ffffff;
}

.ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected,
.ant-menu:not(.ant-menu-horizontal) .ant-menu-item-active,
.ant-menu-vertical .ant-menu-submenu-selected,
.ant-menu-vertical .ant-menu-submenu-active {
  background-color: #00ffcf22;
  color: #ffffff;
}

.ant-menu-vertical .ant-menu-submenu-open .ant-menu-submenu-title,
.ant-menu-vertical .ant-menu-submenu-title:hover {
  color: #ffffff;
}

.ant-menu-submenu-vertical-left
  > .ant-menu-submenu-title
  .ant-menu-submenu-arrow::before,
.ant-menu-submenu-vertical-left
  > .ant-menu-submenu-title
  .ant-menu-submenu-arrow::after,
.ant-menu-submenu-vertical-left
  > .ant-menu-submenu-title:hover
  .ant-menu-submenu-arrow::before,
.ant-menu-submenu-vertical-left
  > .ant-menu-submenu-title:hover
  .ant-menu-submenu-arrow::after {
  background-image: linear-gradient(to right, #ffffff, #ffffff);
}

.ant-menu-item > a {
  color: #ffffff;
}

.ant-menu-item > a:hover,
.ant-menu-item-selected > a {
  color: #ffffff;
}

.gt-icon.menu-icon {
  padding-right: 4px;
  font-size: 21px;
}
</style>
