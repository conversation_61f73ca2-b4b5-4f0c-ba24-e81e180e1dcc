<template>
  <div class="jimu-widget basemap-widget">
    <div class="jimu-tool-widget basemap-thumb" @click="show = !show">
      <img :src="`${selectedBasemapThumb}`" v-show="selectedBasemapThumb" />
    </div>
    <div
      class="jimu-tool-widget basemap-list"
      :style="`width:${basemapListWidth}px`"
      v-show="show"
    >
      <div
        v-for="(item, index) in basemaps"
        :key="index"
        class="basemap"
        :class="{ selected: index == selectedIndex }"
        @click="changeBasemap(item, index)"
      >
        <div class="thumb">
          <img :src="`${item.thumb}`" />
        </div>
        <span class="title">{{ item.name }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { uid } from 'uid'
import axios from 'axios'
import BaseWidget from '@/BaseWidget'

export default {
  name: 'BasemapWidget',

  mixins: [BaseWidget],

  data() {
    return {
      selectedIndex: 0,
      show: false,
      selectedBasemapThumb: null,
      basemapLayers: null,
      basemapListWidth: 99
    }
  },

  computed: {
    ...mapState({
      basemaps: state => state.config.basemaps
    })
  },

  mapLoaded() {
    // 设置默认地图
    this.initBasemapSelection()
    // 保存底图图层
    this.basemapLayers = this.map.getStyle().layers
    // 地图点击时关闭
    this.map.on('click', () => {
      this.show = false
    })
  },
  activated() {
    if (this.map) {
      this.map.resize()
    }
  },
  methods: {
    setbasemapListwidth(basemaps) {
      const length = basemaps.length
      if (length <= 3)
        return (this.basemapListWidth = this.basemapListWidth * length + length)
      else if (length == 4)
        return (this.basemapListWidth = this.basemapListWidth * 2 + 5)
      else return (this.basemapListWidth = this.basemapListWidth * 3 - 5)
    },

    initBasemapSelection() {
      for (const index in this.basemaps) {
        if (this.basemaps[index].default) {
          this.selectedIndex = index
          this.selectedBasemapThumb = this.basemaps[index].thumb
          break
        } else {
          this.selectedBasemapThumb = this.basemaps[0].thumb
        }
      }
    },

    async changeBasemap(basemap, index) {
      if (this.selectedIndex == index) return
      // 设置选中状态
      this.selectedIndex = index
      // 设置选中缩略图
      this.selectedBasemapThumb = basemap.thumb
      // 替换底图
      const oldStyle = this.map.getStyle()
      const style = await this.genBasemapStyle(basemap)
      // 替换底图：将新的底图图层替换原有底图图层
      oldStyle.layers.splice(0, this.basemapLayers.length, ...style.layers)
      Object.assign(oldStyle.sources, style.sources)
      if (style.glyphs && style.sprite) {
        oldStyle.glyphs = style.glyphs
        oldStyle.sprite = style.sprite
      }
      // 替换底图：更新地图
      // deepClone解决setStyle后个别图层异常的问题
      this.map.setStyle(this.$utils.object.deepCloneJson(oldStyle))
      // 保留底图图层
      this.basemapLayers = style.layers
    },

    async genBasemapStyle(basemap) {
      if (!basemap) return
      if (basemap.type == this.$constants.layerTypes.VECTOR) {
        return await this.genVectorStyle(basemap)
      } else if (basemap.type == this.$constants.layerTypes.RASTER) {
        return this.genRasterStyle(basemap)
      }
    },

    async genVectorStyle(basemap) {
      try {
        const resp = await axios.get(basemap.url)
        return resp.data
      } catch (e) {
        console.error(
          `${this.$options.name} load map style file error, url: ${basemap.url}`,
          e
        )
      }
    },

    genRasterStyle(basemap) {
      const id = `jimu-basemap-${uid()}`
      return {
        version: 8,
        sources: {
          [id]: {
            type: 'raster',
            tiles: [basemap.url],
            tileSize: basemap.tileSize || 256
          }
        },
        layers: [
          {
            id,
            type: 'raster',
            source: id
          }
        ]
      }
    }
  },

  mounted() {
    this.setbasemapListwidth(this.basemaps)
  }
}
</script>

<style lang="less" scoped>
@import '@/assets/styles/common.less';

.basemap-widget {
  .basemap-thumb {
    height: 40px;
    width: 40px;
    padding: 4px;
    &:hover {
      cursor: pointer;
    }
    img {
      width: 100%;
      border-radius: 4px;
    }
  }

  .basemap-list {
    position: absolute;
    right: 48px;
    top: 0px;
    width: 90px;
    display: flex;
    flex-wrap: wrap;
    padding: 8px;
    .basemap {
      width: 80px;
      text-align: center;
      margin: 6px;
      &:hover {
        cursor: pointer;
      }
      .thumb {
        margin-bottom: 4px;
        padding: 1px;
        border: 1px solid #ffffff;
        img {
          width: 100%;
        }
      }
      &.selected,
      &:hover {
        color: @primaryColor;
        .thumb {
          border: 1px solid @primaryColor;
        }
      }
    }
  }
}
</style>
