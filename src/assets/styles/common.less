// 通用样式变量
@primaryColor: #4065E0;
@lightColor: #aaa;
@boxShadow: 2px 2px 3px rgb(0 0 0 / 20%);
@font: PingFangSC-Semibold, PingFang SC;
@primaryTextColor: #002159;
@oneMapRed: #ee6860;
@oneMapGreen: #7ECF52;
@oneMapYellow: #EDCC5F;


//  清除浮动
.cleardiv::after{
  content:'';
  display: block;
  clear: both;
}

.jimu-widget-title-ui {
  font-size: 20px;
  font-weight: 600;
  color: #002159;
  position: relative;
  padding-left: 20px;
  vertical-align:middle;
  &::before {
    content: "";
    vertical-align:middle;
    display: inline-block;
    width: 5px;
    height: 20px;
    background: #4065e0;
    box-shadow: 2px 2px 3px 0px rgba(64, 101, 224, 0.35);
    border-radius: 50px;
    transform: translateX(-0.6vw) translateY(-1px);

  }
}

.jimu-widget,
.jimu-layout {

  font-size: 15px;
  position: inherit;

}

// 带有面板的工具型Widget样式
.jimu-tool-widget {
  background: #ffffff;
  border-radius: 4px;
  padding: 8px;
  box-shadow: @boxShadow;
  box-shadow: 0px 3px 10px 0px rgba(56,139,201,0.3);

}

.mapboxgl-popup-content {
  box-shadow: @boxShadow !important;
}

.mapboxgl-popup-close-button {
  font-size: 16px;

  &:hover {
    color: @primaryColor;
  }
}


::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  background-color: rgba(240, 240, 240, 0.1);
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 0px rgba(240, 240, 240, 0.1);
  border-radius: 10px;
  background-color: rgba(240, 240, 240, 0.1);
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 0px rgba(240, 240, 240, 0.5);
  background-color: rgba(240, 240, 240, 1);
}
