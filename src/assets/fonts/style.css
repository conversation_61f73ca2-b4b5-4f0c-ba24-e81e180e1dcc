@font-face {
  font-family: "gt-icon"; /* Project id 2690193 */
  src: url('iconfont.woff2?t=1636547119735') format('woff2'),
       url('iconfont.woff?t=1636547119735') format('woff'),
       url('iconfont.ttf?t=1636547119735') format('truetype');
}

.gt-icon {
  font-family: "gt-icon" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.gt-icon-gdmj:before{
  content: "\ea05"
}

.gt-icon-dksl:before{
  content: "\e66d"
}

.gt-icon-pjdysl:before{
  content: "\e63a"
}

.gt-icon-qt:before{
  content: "\e66a"
}

.gt-icon-sjd:before{
  content: "\e8bb"
}

.gt-icon-hd:before{
  content: "\e8ba"
}

.gt-icon-st:before{
  content: "\e8b8"
}

.gt-icon-table:before{
  content: "\e63e"
}

.gt-icon-echarts:before{
  content: "\e6db"
}

.gt-icon-aqlyyzt:before{
  content: "\eb77"
}

.gt-icon-zht:before{
  content: "\eb78"
}

.gt-icon-nydaqly:before{
  content: "\e608"
}

.gt-icon-logo:before{
  content: "\e61c"
}
.gt-icon-bars:before{
  content: "\e637"
}

.gt-icon-openTool:before {
  content: "\e7d3"
}

.gt-icon-qdlb:before {
  content: "\e614"
}

.gt-icon-tjfx:before {
  content: "\e60f"
}

.gt-icon-onemap:before {
  content: "\e60e"
}

.gt-icon-lhggq:before {
  content: "\e607"
}

.gt-icon-aqlytz:before {
  content: "\e606"
}

.gt-icon-qdlb:before {
  content: "\e605"
}

.gt-icon-zybh:before {
  content: "\e610"
}

.gt-icon-hjbh:before {
  content: "\e611"
}

.gt-icon-cdhj:before {
  content: "\e612"
}

.gt-icon-xtgl:before {
  content: "\e613"
}

.gt-icon-shengtaibaohu:before {
  content: "\e648";
}

.gt-icon-dikuai:before {
  content: "\e871";
}

.gt-icon-shujuguanli:before {
  content: "\e63e";
}

.gt-icon-ziyuan:before {
  content: "\eee9";
}

.gt-icon-grass:before {
  content: "\e608";
}

.gt-icon-open:before {
  content: "\e786";
}

.gt-icon-bars:before {
  content: "\e637";
}

.gt-icon-close:before {
  content: "\eb76";
}

.gt-icon-time:before {
  content: "\e680";
}

.gt-icon-point:before {
  content: "\e65e";
}

.gt-icon-minus:before {
  content: "\e960";
}

.gt-icon-home:before {
  content: "\e734";
}

.gt-icon-plus:before {
  content: "\e678";
}

