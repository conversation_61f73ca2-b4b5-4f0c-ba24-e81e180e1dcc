
import axios from 'axios'

import { mapMutations } from 'vuex'

export default {

  name: 'BaseWidget',

  inject: ['bus', 'module'],

  data () {
    return {
      configUrl: null, // 配置文件地址
      config: null, // 配置文件内容
      map: null,  // 地图实例
      states: null,
      style: null,
      changeStates: null //图层列表隐藏显示状态
    }
  },
  // 配置文件加载后回调，子类覆盖该方法
  configLoaded () {
    // do nothing
  },

  // 地图加载后回调，子类覆盖该方法
  mapLoaded () {
    // do nothing
  },

  // 配置文件和地图全部完成加载后回调，子类覆盖该方法
  allLoaded () {
    // do nothing
  },

  async mounted () {
    if ( this.states ) {
      //响应状态
      this.bus.$on( this.$events.state.STATE_UPDATE, this.stateUpdateHandler )
    }

    if ( this.changeStates ) {
      //图层列表响应状态
      this.bus.$on( this.$events.state.WIDGETSTATE_UPDATE, this.widgetstateUpdateHandler  )
    }

    // 获取地图实例
    this.bus.$on( this.$events.map.MAP_LOAD, ( map ) => {
      this.map = map
      const mapLoaded = this.$options.mapLoaded
      if ( mapLoaded ) {
        mapLoaded.call( this )
      }
      if ( !this.configUrl || this.config ) {
        const allLoaded = this.$options.allLoaded
        if ( allLoaded ) {
          allLoaded.call( this )
        }
      }
    } )

    // 读取配置文件
    if ( this.configUrl ) {
      try {
        const resp = await axios.get( this.configUrl )
        this.config = resp.data
        const configLoaded = this.$options.configLoaded
        if ( configLoaded ) {
          configLoaded.call( this )
        }
        if ( this.map ) {
          const allLoaded = this.$options.allLoaded
          if ( allLoaded ) {
            allLoaded.call( this )
          }
        }
      } catch ( e ) {
        console.error( `${ this.$options.name } load config file error, url: ${ this.configUrl }`, e )
      }
    } else {
      const configLoaded = this.$options.configLoaded
      if ( configLoaded ) {
        configLoaded.call( this )
      }
      if ( this.map ) {
        const allLoaded = this.$options.allLoaded
        if ( allLoaded ) {
          allLoaded.call( this )
        }
      }
    }
  },

  methods: {

    ...mapMutations( {
      _setGlobalData: 'setGlobalData',
      _setModuleData: 'setModuleData',
      _setModuleLayerFilter: 'setModuleLayerFilter',
      _setModuleLayerStyle: 'setModuleLayerStyle'
    } ),

    // 设置全局共享数据
    setGlobalData ( key, value ) {
      this._setGlobalData( {
        key,
        value
      } )
    },

    // 获取全局共享数据
    getGlobalData ( key ) {
      return this.$store.state.data[key]
    },

    // 设置模块共享数据
    setModuleData ( key, value ) {
      this._setModuleData( {
        module: this.module,
        key,
        value
      } )
    },

    // 设置模块图层数据
    setModuleLayerFilter ( layerId, key, value ) {
      this._setModuleLayerFilter( {
        module: this.module,
        layerId,
        key,
        value
      } )
    },

    // 设置模块图层数据
    setModuleLayerStyle ( layerId, key, value ) {
      this._setModuleLayerStyle( {
        module: this.module,
        layerId,
        key,
        value
      } )
    },

    // 获取模块共享数据
    getModuleData ( key ) {
      const data = this.$store.state.moduleData[this.module]
      return data ? data[key] : null
    },

    applyStyle ( style ) {
      if ( !style ) return
      let { left, top, right, bottom, width, height } = style

      this.$el.style.left = this.$utils.number.isPositiveInteger( left ) ? `${ left }px` : this.$el.style.left
      this.$el.style.top = this.$utils.number.isPositiveInteger( top ) ? `${ top }px` : this.$el.style.top
      this.$el.style.right = this.$utils.number.isPositiveInteger( right ) ? `${ right }px` : this.$el.style.right
      this.$el.style.height = this.$utils.number.isPositiveInteger( height ) ? `${ height }px` : this.$el.style.height
      this.$el.style.bottom = this.$utils.number.isPositiveInteger( bottom ) ? `${ bottom }px` : this.$el.style.bottom
      this.$el.style.width = this.$utils.number.isPositiveInteger( width ) ? `${ width }px` : this.$el.style.width

    },

    stateUpdateHandler ( state ) {
      let style = this.states[state]
      this.applyStyle( style )
    },

    widgetstateUpdateHandler(state){
      let style = this.changeStates[state]
      this.applyStyle( style )
    },


    getModuleLayerFilter ( layerId ) {
      const data = this.$store.state.moduleData[this.module]?.[this.$constants.shareDataKey.LAYER_FILTER]?.[layerId]

      if ( !data ) return
      let filters = Object.values( data ).filter( item => {
        return item
      } )
      if ( filters.length === 0 ) return
      if ( filters.length === 1 ) return filters[0]
      return ['all', ...filters]
    },

    getModuleLayerStyle ( layerId ) {
      const data = this.$store.state.moduleData[this.module]?.[this.$constants.shareDataKey.LAYER_STYLE]?.[layerId]
      return data
    },


    close () {

    }

  }

}